/**
 * 一级认证临时缓存管理
 * 用于存储一级认证的临时状态，不持久化到数据库
 */

// 内存缓存存储一级认证状态
const level1Cache = new Map();

// 缓存过期时间（30分钟）
const CACHE_EXPIRE_TIME = 30 * 60 * 1000;

/**
 * 设置一级认证状态
 * @param {number} userId - 用户ID
 * @param {object} data - 认证数据
 */
const setLevel1Status = (userId, data) => {
  const cacheData = {
    ...data,
    timestamp: Date.now(),
    expiresAt: Date.now() + CACHE_EXPIRE_TIME
  };
  
  level1Cache.set(userId, cacheData);
  console.log(`一级认证状态已缓存 - 用户ID: ${userId}`);
  
  // 设置自动清理
  setTimeout(() => {
    if (level1Cache.has(userId)) {
      const cached = level1Cache.get(userId);
      if (cached.timestamp === cacheData.timestamp) {
        level1Cache.delete(userId);
        console.log(`一级认证缓存已过期清理 - 用户ID: ${userId}`);
      }
    }
  }, CACHE_EXPIRE_TIME);
};

/**
 * 获取一级认证状态
 * @param {number} userId - 用户ID
 * @returns {object|null} - 认证数据或null
 */
const getLevel1Status = (userId) => {
  const cached = level1Cache.get(userId);
  
  if (!cached) {
    return null;
  }
  
  // 检查是否过期
  if (Date.now() > cached.expiresAt) {
    level1Cache.delete(userId);
    console.log(`一级认证缓存已过期 - 用户ID: ${userId}`);
    return null;
  }
  
  return cached;
};

/**
 * 删除一级认证状态
 * @param {number} userId - 用户ID
 */
const clearLevel1Status = (userId) => {
  const deleted = level1Cache.delete(userId);
  if (deleted) {
    console.log(`一级认证状态已清除 - 用户ID: ${userId}`);
  }
  return deleted;
};

/**
 * 检查用户是否有有效的一级认证
 * @param {number} userId - 用户ID
 * @returns {boolean} - 是否已认证
 */
const isLevel1Verified = (userId) => {
  const status = getLevel1Status(userId);
  return status && status.verified === true;
};

/**
 * 获取一级认证的详细信息（脱敏）
 * @param {number} userId - 用户ID
 * @returns {object|null} - 脱敏后的认证信息
 */
const getLevel1Info = (userId) => {
  const status = getLevel1Status(userId);
  
  if (!status || !status.verified) {
    return null;
  }
  
  return {
    verified: true,
    verifiedAt: status.verifiedAt,
    paymentMethod: status.paymentMethod,
    realName: status.realName ? status.realName.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
    idNumber: status.idNumber ? status.idNumber.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
    expiresAt: status.expiresAt
  };
};

/**
 * 清理所有过期的缓存
 */
const cleanupExpiredCache = () => {
  const now = Date.now();
  let cleanedCount = 0;
  
  for (const [userId, data] of level1Cache.entries()) {
    if (now > data.expiresAt) {
      level1Cache.delete(userId);
      cleanedCount++;
    }
  }
  
  if (cleanedCount > 0) {
    console.log(`清理了 ${cleanedCount} 个过期的一级认证缓存`);
  }
  
  return cleanedCount;
};

// 定期清理过期缓存（每10分钟）
setInterval(cleanupExpiredCache, 10 * 60 * 1000);

/**
 * 获取缓存统计信息
 */
const getCacheStats = () => {
  const now = Date.now();
  let activeCount = 0;
  let expiredCount = 0;
  
  for (const [userId, data] of level1Cache.entries()) {
    if (now > data.expiresAt) {
      expiredCount++;
    } else {
      activeCount++;
    }
  }
  
  return {
    total: level1Cache.size,
    active: activeCount,
    expired: expiredCount
  };
};

module.exports = {
  setLevel1Status,
  getLevel1Status,
  clearLevel1Status,
  isLevel1Verified,
  getLevel1Info,
  cleanupExpiredCache,
  getCacheStats
};
