{"ast": null, "code": "import { defineStore } from 'pinia';\nimport axios from 'axios';\nimport { getApiClient } from '../utils/api';\nimport { getAuthConfig } from '../utils/config';\n\n// 获取API客户端\nconst apiClient = getApiClient();\n\n// 获取认证配置\nconst getAuth = () => getAuthConfig();\n\n// 用户状态管理\nexport const useUserStore = defineStore('user', {\n  state: () => {\n    const auth = getAuth();\n    return {\n      token: localStorage.getItem(auth.tokenKey) || '',\n      user: JSON.parse(localStorage.getItem(auth.userKey) || 'null'),\n      loading: false,\n      error: null\n    };\n  },\n  getters: {\n    isLoggedIn: state => !!state.token,\n    currentUser: state => state.user,\n    isLoading: state => state.loading,\n    hasError: state => !!state.error,\n    authHeader: state => ({\n      Authorization: `Bearer ${state.token}`\n    })\n  },\n  actions: {\n    // 设置认证头\n    setAuthHeader() {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;\n    },\n    // 清除认证头\n    clearAuthHeader() {\n      delete axios.defaults.headers.common['Authorization'];\n    },\n    // 保存令牌和用户信息\n    saveAuth(token, user) {\n      const auth = getAuth();\n      this.token = token;\n      this.user = user;\n      localStorage.setItem(auth.tokenKey, token);\n      localStorage.setItem(auth.userKey, JSON.stringify(user));\n      this.setAuthHeader();\n    },\n    // 清除认证信息\n    clearAuth() {\n      const auth = getAuth();\n      this.token = '';\n      this.user = null;\n      localStorage.removeItem(auth.tokenKey);\n      localStorage.removeItem(auth.userKey);\n      this.clearAuthHeader();\n    },\n    // 注册\n    async register(userData) {\n      this.loading = true;\n      this.error = null;\n      try {\n        // 根据注册类型处理请求路径\n        let endpoint = '/auth/register';\n        if (userData.registerType === 'phone') {\n          endpoint = '/auth/register-by-phone';\n        } else if (userData.registerType === 'email') {\n          endpoint = '/auth/register-by-email';\n        }\n        const response = await apiClient.post(endpoint, userData);\n        const {\n          token,\n          user\n        } = response.data;\n        this.saveAuth(token, user);\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        console.error('注册错误:', error);\n\n        // 根据错误状态码提供更具体的错误信息\n        if (error.response?.status === 400) {\n          const message = error.response.data?.message;\n          if (message?.includes('用户名已存在')) {\n            this.error = '用户名已被使用，请选择其他用户名';\n          } else if (message?.includes('邮箱已被注册')) {\n            this.error = '该邮箱已被注册，请使用其他邮箱或直接登录';\n          } else if (message?.includes('验证码')) {\n            this.error = '验证码无效或已过期，请重新获取验证码';\n          } else {\n            this.error = message || '注册信息有误，请检查后重试';\n          }\n        } else {\n          this.error = error.response?.data?.message || '注册失败，请稍后重试';\n        }\n        throw error;\n      }\n    },\n    // 登录\n    async login(credentials) {\n      try {\n        this.loading = true;\n        const response = await getApiClient().post('/auth/login', {\n          username: credentials.account,\n          password: credentials.password\n        });\n        if (response.data.success) {\n          const {\n            token,\n            user\n          } = response.data;\n          this.saveAuth(token, user);\n          return response.data;\n        } else {\n          this.loading = false;\n          this.error = response.data.message || '登录失败';\n          throw new Error(response.data.message || '登录失败');\n        }\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '登录失败';\n        throw error;\n      }\n    },\n    // 退出登录\n    async logout() {\n      try {\n        await apiClient.post('/auth/logout');\n      } catch (error) {\n        console.error('Logout error:', error);\n      } finally {\n        this.clearAuth();\n      }\n    },\n    // 获取用户资料\n    async fetchUserProfile() {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.get('/users/profile');\n        this.user = response.data.user;\n        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '获取用户资料失败';\n\n        // 如果是认证错误，登出\n        if (error.response?.status === 401) {\n          this.logout();\n        }\n        throw error;\n      }\n    },\n    // 更新用户资料\n    async updateProfile(profileData) {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.put('/users/profile', profileData);\n        this.user = response.data.user;\n        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '更新用户资料失败';\n        throw error;\n      }\n    },\n    // 更新密码\n    async updatePassword(passwordData) {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.put('/auth/password', passwordData);\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '更新密码失败';\n        throw error;\n      }\n    },\n    // 请求重置密码\n    async forgotPassword(email) {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.post('/auth/forgot-password', {\n          email\n        });\n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '请求重置密码失败';\n        throw error;\n      }\n    },\n    // 重置密码\n    async resetPassword(token, password) {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.post('/auth/reset-password/' + token, {\n          password\n        });\n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '重置密码失败';\n        throw error;\n      }\n    },\n    // 验证邮箱\n    async verifyEmail(token) {\n      this.loading = true;\n      this.error = null;\n      try {\n        const response = await apiClient.get('/auth/verify-email/' + token);\n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '邮箱验证失败';\n        throw error;\n      }\n    }\n  }\n});", "map": {"version": 3, "names": ["defineStore", "axios", "getApiClient", "getAuthConfig", "apiClient", "getAuth", "useUserStore", "state", "auth", "token", "localStorage", "getItem", "<PERSON><PERSON><PERSON>", "user", "JSON", "parse", "<PERSON><PERSON><PERSON>", "loading", "error", "getters", "isLoggedIn", "currentUser", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "actions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "defaults", "headers", "common", "clear<PERSON><PERSON><PERSON><PERSON>er", "saveAuth", "setItem", "stringify", "clearAuth", "removeItem", "register", "userData", "endpoint", "registerType", "response", "post", "data", "console", "status", "message", "includes", "login", "credentials", "username", "account", "password", "success", "Error", "logout", "fetchUserProfile", "get", "updateProfile", "profileData", "put", "updatePassword", "passwordData", "forgotPassword", "email", "resetPassword", "verifyEmail"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/stores/user.js"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport axios from 'axios'\nimport { getApiClient } from '../utils/api'\nimport { getAuthConfig } from '../utils/config'\n\n// 获取API客户端\nconst apiClient = getApiClient();\n\n// 获取认证配置\nconst getAuth = () => getAuthConfig();\n\n// 用户状态管理\nexport const useUserStore = defineStore('user', {\n  state: () => {\n    const auth = getAuth();\n    return {\n      token: localStorage.getItem(auth.tokenKey) || '',\n      user: JSON.parse(localStorage.getItem(auth.userKey) || 'null'),\n      loading: false,\n      error: null\n    };\n  },\n  \n  getters: {\n    isLoggedIn: (state) => !!state.token,\n    currentUser: (state) => state.user,\n    isLoading: (state) => state.loading,\n    hasError: (state) => !!state.error,\n    authHeader: (state) => ({ Authorization: `Bearer ${state.token}` })\n  },\n  \n  actions: {\n    // 设置认证头\n    setAuthHeader() {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`\n    },\n    \n    // 清除认证头\n    clearAuthHeader() {\n      delete axios.defaults.headers.common['Authorization']\n    },\n    \n    // 保存令牌和用户信息\n    saveAuth(token, user) {\n      const auth = getAuth();\n      this.token = token;\n      this.user = user;\n      localStorage.setItem(auth.tokenKey, token);\n      localStorage.setItem(auth.userKey, JSON.stringify(user));\n      this.setAuthHeader();\n    },\n    \n    // 清除认证信息\n    clearAuth() {\n      const auth = getAuth();\n      this.token = '';\n      this.user = null;\n      localStorage.removeItem(auth.tokenKey);\n      localStorage.removeItem(auth.userKey);\n      this.clearAuthHeader();\n    },\n    \n    // 注册\n    async register(userData) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        // 根据注册类型处理请求路径\n        let endpoint = '/auth/register';\n        if (userData.registerType === 'phone') {\n          endpoint = '/auth/register-by-phone';\n        } else if (userData.registerType === 'email') {\n          endpoint = '/auth/register-by-email';\n        }\n        \n        const response = await apiClient.post(endpoint, userData);\n        const { token, user } = response.data;\n        this.saveAuth(token, user);\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        console.error('注册错误:', error);\n\n        // 根据错误状态码提供更具体的错误信息\n        if (error.response?.status === 400) {\n          const message = error.response.data?.message;\n          if (message?.includes('用户名已存在')) {\n            this.error = '用户名已被使用，请选择其他用户名';\n          } else if (message?.includes('邮箱已被注册')) {\n            this.error = '该邮箱已被注册，请使用其他邮箱或直接登录';\n          } else if (message?.includes('验证码')) {\n            this.error = '验证码无效或已过期，请重新获取验证码';\n          } else {\n            this.error = message || '注册信息有误，请检查后重试';\n          }\n        } else {\n          this.error = error.response?.data?.message || '注册失败，请稍后重试';\n        }\n\n        throw error;\n      }\n    },\n    \n    // 登录\n    async login(credentials) {\n      try {\n        this.loading = true;\n        const response = await getApiClient().post('/auth/login', {\n          username: credentials.account,\n          password: credentials.password\n        });\n        \n        if (response.data.success) {\n          const { token, user } = response.data;\n          this.saveAuth(token, user);\n          return response.data;\n        } else {\n          this.loading = false;\n          this.error = response.data.message || '登录失败';\n          throw new Error(response.data.message || '登录失败');\n        }\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '登录失败';\n        throw error;\n      }\n    },\n    \n    // 退出登录\n    async logout() {\n      try {\n        await apiClient.post('/auth/logout');\n      } catch (error) {\n        console.error('Logout error:', error);\n      } finally {\n        this.clearAuth();\n      }\n    },\n    \n    // 获取用户资料\n    async fetchUserProfile() {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.get('/users/profile');\n        this.user = response.data.user;\n        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '获取用户资料失败';\n        \n        // 如果是认证错误，登出\n        if (error.response?.status === 401) {\n          this.logout();\n        }\n        \n        throw error;\n      }\n    },\n    \n    // 更新用户资料\n    async updateProfile(profileData) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.put('/users/profile', profileData);\n        this.user = response.data.user;\n        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '更新用户资料失败';\n        throw error;\n      }\n    },\n    \n    // 更新密码\n    async updatePassword(passwordData) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.put('/auth/password', passwordData);\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '更新密码失败';\n        throw error;\n      }\n    },\n    \n    // 请求重置密码\n    async forgotPassword(email) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.post('/auth/forgot-password', { email });\n        \n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '请求重置密码失败';\n        throw error;\n      }\n    },\n    \n    // 重置密码\n    async resetPassword(token, password) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.post('/auth/reset-password/' + token, { password });\n        \n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '重置密码失败';\n        throw error;\n      }\n    },\n    \n    // 验证邮箱\n    async verifyEmail(token) {\n      this.loading = true;\n      this.error = null;\n      \n      try {\n        const response = await apiClient.get('/auth/verify-email/' + token);\n        \n        this.loading = false;\n        return response.data;\n      } catch (error) {\n        this.loading = false;\n        this.error = error.response?.data?.message || '邮箱验证失败';\n        throw error;\n      }\n    }\n  }\n}) "], "mappings": "AAAA,SAASA,WAAW,QAAQ,OAAO;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,YAAY,QAAQ,cAAc;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;;AAE/C;AACA,MAAMC,SAAS,GAAGF,YAAY,CAAC,CAAC;;AAEhC;AACA,MAAMG,OAAO,GAAGA,CAAA,KAAMF,aAAa,CAAC,CAAC;;AAErC;AACA,OAAO,MAAMG,YAAY,GAAGN,WAAW,CAAC,MAAM,EAAE;EAC9CO,KAAK,EAAEA,CAAA,KAAM;IACX,MAAMC,IAAI,GAAGH,OAAO,CAAC,CAAC;IACtB,OAAO;MACLI,KAAK,EAAEC,YAAY,CAACC,OAAO,CAACH,IAAI,CAACI,QAAQ,CAAC,IAAI,EAAE;MAChDC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACL,YAAY,CAACC,OAAO,CAACH,IAAI,CAACQ,OAAO,CAAC,IAAI,MAAM,CAAC;MAC9DC,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE;IACT,CAAC;EACH,CAAC;EAEDC,OAAO,EAAE;IACPC,UAAU,EAAGb,KAAK,IAAK,CAAC,CAACA,KAAK,CAACE,KAAK;IACpCY,WAAW,EAAGd,KAAK,IAAKA,KAAK,CAACM,IAAI;IAClCS,SAAS,EAAGf,KAAK,IAAKA,KAAK,CAACU,OAAO;IACnCM,QAAQ,EAAGhB,KAAK,IAAK,CAAC,CAACA,KAAK,CAACW,KAAK;IAClCM,UAAU,EAAGjB,KAAK,KAAM;MAAEkB,aAAa,EAAE,UAAUlB,KAAK,CAACE,KAAK;IAAG,CAAC;EACpE,CAAC;EAEDiB,OAAO,EAAE;IACP;IACAC,aAAaA,CAAA,EAAG;MACd1B,KAAK,CAAC2B,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAACrB,KAAK,EAAE;IACzE,CAAC;IAED;IACAsB,eAAeA,CAAA,EAAG;MAChB,OAAO9B,KAAK,CAAC2B,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD,CAAC;IAED;IACAE,QAAQA,CAACvB,KAAK,EAAEI,IAAI,EAAE;MACpB,MAAML,IAAI,GAAGH,OAAO,CAAC,CAAC;MACtB,IAAI,CAACI,KAAK,GAAGA,KAAK;MAClB,IAAI,CAACI,IAAI,GAAGA,IAAI;MAChBH,YAAY,CAACuB,OAAO,CAACzB,IAAI,CAACI,QAAQ,EAAEH,KAAK,CAAC;MAC1CC,YAAY,CAACuB,OAAO,CAACzB,IAAI,CAACQ,OAAO,EAAEF,IAAI,CAACoB,SAAS,CAACrB,IAAI,CAAC,CAAC;MACxD,IAAI,CAACc,aAAa,CAAC,CAAC;IACtB,CAAC;IAED;IACAQ,SAASA,CAAA,EAAG;MACV,MAAM3B,IAAI,GAAGH,OAAO,CAAC,CAAC;MACtB,IAAI,CAACI,KAAK,GAAG,EAAE;MACf,IAAI,CAACI,IAAI,GAAG,IAAI;MAChBH,YAAY,CAAC0B,UAAU,CAAC5B,IAAI,CAACI,QAAQ,CAAC;MACtCF,YAAY,CAAC0B,UAAU,CAAC5B,IAAI,CAACQ,OAAO,CAAC;MACrC,IAAI,CAACe,eAAe,CAAC,CAAC;IACxB,CAAC;IAED;IACA,MAAMM,QAAQA,CAACC,QAAQ,EAAE;MACvB,IAAI,CAACrB,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF;QACA,IAAIqB,QAAQ,GAAG,gBAAgB;QAC/B,IAAID,QAAQ,CAACE,YAAY,KAAK,OAAO,EAAE;UACrCD,QAAQ,GAAG,yBAAyB;QACtC,CAAC,MAAM,IAAID,QAAQ,CAACE,YAAY,KAAK,OAAO,EAAE;UAC5CD,QAAQ,GAAG,yBAAyB;QACtC;QAEA,MAAME,QAAQ,GAAG,MAAMrC,SAAS,CAACsC,IAAI,CAACH,QAAQ,EAAED,QAAQ,CAAC;QACzD,MAAM;UAAE7B,KAAK;UAAEI;QAAK,CAAC,GAAG4B,QAAQ,CAACE,IAAI;QACrC,IAAI,CAACX,QAAQ,CAACvB,KAAK,EAAEI,IAAI,CAAC;QAC1B,OAAO4B,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB2B,OAAO,CAAC1B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;;QAE7B;QACA,IAAIA,KAAK,CAACuB,QAAQ,EAAEI,MAAM,KAAK,GAAG,EAAE;UAClC,MAAMC,OAAO,GAAG5B,KAAK,CAACuB,QAAQ,CAACE,IAAI,EAAEG,OAAO;UAC5C,IAAIA,OAAO,EAAEC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC/B,IAAI,CAAC7B,KAAK,GAAG,kBAAkB;UACjC,CAAC,MAAM,IAAI4B,OAAO,EAAEC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtC,IAAI,CAAC7B,KAAK,GAAG,sBAAsB;UACrC,CAAC,MAAM,IAAI4B,OAAO,EAAEC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACnC,IAAI,CAAC7B,KAAK,GAAG,oBAAoB;UACnC,CAAC,MAAM;YACL,IAAI,CAACA,KAAK,GAAG4B,OAAO,IAAI,eAAe;UACzC;QACF,CAAC,MAAM;UACL,IAAI,CAAC5B,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,YAAY;QAC5D;QAEA,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAM8B,KAAKA,CAACC,WAAW,EAAE;MACvB,IAAI;QACF,IAAI,CAAChC,OAAO,GAAG,IAAI;QACnB,MAAMwB,QAAQ,GAAG,MAAMvC,YAAY,CAAC,CAAC,CAACwC,IAAI,CAAC,aAAa,EAAE;UACxDQ,QAAQ,EAAED,WAAW,CAACE,OAAO;UAC7BC,QAAQ,EAAEH,WAAW,CAACG;QACxB,CAAC,CAAC;QAEF,IAAIX,QAAQ,CAACE,IAAI,CAACU,OAAO,EAAE;UACzB,MAAM;YAAE5C,KAAK;YAAEI;UAAK,CAAC,GAAG4B,QAAQ,CAACE,IAAI;UACrC,IAAI,CAACX,QAAQ,CAACvB,KAAK,EAAEI,IAAI,CAAC;UAC1B,OAAO4B,QAAQ,CAACE,IAAI;QACtB,CAAC,MAAM;UACL,IAAI,CAAC1B,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,KAAK,GAAGuB,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,MAAM;UAC5C,MAAM,IAAIQ,KAAK,CAACb,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,MAAM,CAAC;QAClD;MACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,MAAM;QACpD,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAMqC,MAAMA,CAAA,EAAG;MACb,IAAI;QACF,MAAMnD,SAAS,CAACsC,IAAI,CAAC,cAAc,CAAC;MACtC,CAAC,CAAC,OAAOxB,KAAK,EAAE;QACd0B,OAAO,CAAC1B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACvC,CAAC,SAAS;QACR,IAAI,CAACiB,SAAS,CAAC,CAAC;MAClB;IACF,CAAC;IAED;IACA,MAAMqB,gBAAgBA,CAAA,EAAG;MACvB,IAAI,CAACvC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACqD,GAAG,CAAC,gBAAgB,CAAC;QACtD,IAAI,CAAC5C,IAAI,GAAG4B,QAAQ,CAACE,IAAI,CAAC9B,IAAI;QAC9BH,YAAY,CAACuB,OAAO,CAAC5B,OAAO,CAAC,CAAC,CAACW,OAAO,EAAEF,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACrB,IAAI,CAAC,CAAC;QAClE,OAAO4B,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,UAAU;;QAExD;QACA,IAAI5B,KAAK,CAACuB,QAAQ,EAAEI,MAAM,KAAK,GAAG,EAAE;UAClC,IAAI,CAACU,MAAM,CAAC,CAAC;QACf;QAEA,MAAMrC,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAMwC,aAAaA,CAACC,WAAW,EAAE;MAC/B,IAAI,CAAC1C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACwD,GAAG,CAAC,gBAAgB,EAAED,WAAW,CAAC;QACnE,IAAI,CAAC9C,IAAI,GAAG4B,QAAQ,CAACE,IAAI,CAAC9B,IAAI;QAC9BH,YAAY,CAACuB,OAAO,CAAC5B,OAAO,CAAC,CAAC,CAACW,OAAO,EAAEF,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACrB,IAAI,CAAC,CAAC;QAClE,OAAO4B,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,UAAU;QACxD,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAM2C,cAAcA,CAACC,YAAY,EAAE;MACjC,IAAI,CAAC7C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACwD,GAAG,CAAC,gBAAgB,EAAEE,YAAY,CAAC;QACpE,OAAOrB,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,QAAQ;QACtD,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAM6C,cAAcA,CAACC,KAAK,EAAE;MAC1B,IAAI,CAAC/C,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACsC,IAAI,CAAC,uBAAuB,EAAE;UAAEsB;QAAM,CAAC,CAAC;QAEzE,IAAI,CAAC/C,OAAO,GAAG,KAAK;QACpB,OAAOwB,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,UAAU;QACxD,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAM+C,aAAaA,CAACxD,KAAK,EAAE2C,QAAQ,EAAE;MACnC,IAAI,CAACnC,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACsC,IAAI,CAAC,uBAAuB,GAAGjC,KAAK,EAAE;UAAE2C;QAAS,CAAC,CAAC;QAEpF,IAAI,CAACnC,OAAO,GAAG,KAAK;QACpB,OAAOwB,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,QAAQ;QACtD,MAAM5B,KAAK;MACb;IACF,CAAC;IAED;IACA,MAAMgD,WAAWA,CAACzD,KAAK,EAAE;MACvB,IAAI,CAACQ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,KAAK,GAAG,IAAI;MAEjB,IAAI;QACF,MAAMuB,QAAQ,GAAG,MAAMrC,SAAS,CAACqD,GAAG,CAAC,qBAAqB,GAAGhD,KAAK,CAAC;QAEnE,IAAI,CAACQ,OAAO,GAAG,KAAK;QACpB,OAAOwB,QAAQ,CAACE,IAAI;MACtB,CAAC,CAAC,OAAOzB,KAAK,EAAE;QACd,IAAI,CAACD,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,KAAK,GAAGA,KAAK,CAACuB,QAAQ,EAAEE,IAAI,EAAEG,OAAO,IAAI,QAAQ;QACtD,MAAM5B,KAAK;MACb;IACF;EACF;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}