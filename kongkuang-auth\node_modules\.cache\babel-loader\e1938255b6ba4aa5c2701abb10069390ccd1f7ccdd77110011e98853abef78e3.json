{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"application-detail-container\"\n};\nconst _hoisted_2 = {\n  class: \"page-header\"\n};\nconst _hoisted_3 = {\n  class: \"page-title-section\"\n};\nconst _hoisted_4 = {\n  class: \"page-title\"\n};\nconst _hoisted_5 = {\n  class: \"page-actions\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"application-content\"\n};\nconst _hoisted_7 = {\n  class: \"form-actions\"\n};\nconst _hoisted_8 = {\n  class: \"oauth-info\"\n};\nconst _hoisted_9 = {\n  class: \"info-item\"\n};\nconst _hoisted_10 = {\n  class: \"info-value\"\n};\nconst _hoisted_11 = {\n  class: \"info-item\"\n};\nconst _hoisted_12 = {\n  class: \"info-value\"\n};\nconst _hoisted_13 = {\n  class: \"form-actions\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_arrow_back = _resolveComponent(\"arrow-back\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_input = _resolveComponent(\"n-input\");\n  const _component_n_form_item = _resolveComponent(\"n-form-item\");\n  const _component_n_select = _resolveComponent(\"n-select\");\n  const _component_n_color_picker = _resolveComponent(\"n-color-picker\");\n  const _component_n_form = _resolveComponent(\"n-form\");\n  const _component_n_card = _resolveComponent(\"n-card\");\n  const _component_n_tab_pane = _resolveComponent(\"n-tab-pane\");\n  const _component_copy = _resolveComponent(\"copy\");\n  const _component_n_space = _resolveComponent(\"n-space\");\n  const _component_n_divider = _resolveComponent(\"n-divider\");\n  const _component_n_dynamic_input = _resolveComponent(\"n-dynamic-input\");\n  const _component_n_empty = _resolveComponent(\"n-empty\");\n  const _component_n_tabs = _resolveComponent(\"n-tabs\");\n  const _component_n_spin = _resolveComponent(\"n-spin\");\n  const _component_n_modal = _resolveComponent(\"n-modal\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_n_button, {\n    quaternary: \"\",\n    circle: \"\",\n    onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/applications'))\n  }, {\n    icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_arrow_back)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"h2\", _hoisted_4, _toDisplayString(_ctx.application.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_n_button, {\n    type: \"error\",\n    onClick: _ctx.showDeleteConfirm\n  }, {\n    default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\" 删除应用 \")])),\n    _: 1 /* STABLE */,\n    __: [17]\n  }, 8 /* PROPS */, [\"onClick\"])])]), _createVNode(_component_n_spin, {\n    show: _ctx.loading\n  }, {\n    default: _withCtx(() => [_ctx.application.id ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createVNode(_component_n_tabs, {\n      type: \"line\",\n      animated: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_tab_pane, {\n        name: \"basic\",\n        tab: \"基本信息\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_card, {\n          title: \"应用信息\",\n          class: \"detail-card\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_form, {\n            ref: \"formRef\",\n            model: _ctx.formValue,\n            rules: _ctx.rules,\n            \"label-placement\": \"left\",\n            \"label-width\": \"120px\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_n_form_item, {\n              path: \"name\",\n              label: \"应用名称\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_input, {\n                value: _ctx.formValue.name,\n                \"onUpdate:value\": _cache[1] || (_cache[1] = $event => _ctx.formValue.name = $event),\n                placeholder: \"请输入应用名称\"\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"description\",\n              label: \"应用描述\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_input, {\n                value: _ctx.formValue.description,\n                \"onUpdate:value\": _cache[2] || (_cache[2] = $event => _ctx.formValue.description = $event),\n                type: \"textarea\",\n                placeholder: \"请输入应用描述\",\n                autosize: {\n                  minRows: 3,\n                  maxRows: 5\n                }\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"icon\",\n              label: \"应用图标\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_select, {\n                value: _ctx.formValue.icon,\n                \"onUpdate:value\": _cache[3] || (_cache[3] = $event => _ctx.formValue.icon = $event),\n                options: _ctx.iconOptions\n              }, null, 8 /* PROPS */, [\"value\", \"options\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"icon_color\",\n              label: \"图标颜色\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_color_picker, {\n                value: _ctx.formValue.icon_color,\n                \"onUpdate:value\": _cache[4] || (_cache[4] = $event => _ctx.formValue.icon_color = $event)\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"homepage_url\",\n              label: \"应用主页\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_input, {\n                value: _ctx.formValue.homepage_url,\n                \"onUpdate:value\": _cache[5] || (_cache[5] = $event => _ctx.formValue.homepage_url = $event),\n                placeholder: \"请输入应用主页URL\"\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"privacy_policy_url\",\n              label: \"隐私政策\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_input, {\n                value: _ctx.formValue.privacy_policy_url,\n                \"onUpdate:value\": _cache[6] || (_cache[6] = $event => _ctx.formValue.privacy_policy_url = $event),\n                placeholder: \"请输入隐私政策URL\"\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"terms_of_service_url\",\n              label: \"服务条款\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_input, {\n                value: _ctx.formValue.terms_of_service_url,\n                \"onUpdate:value\": _cache[7] || (_cache[7] = $event => _ctx.formValue.terms_of_service_url = $event),\n                placeholder: \"请输入服务条款URL\"\n              }, null, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_n_button, {\n              type: \"primary\",\n              loading: _ctx.submitting,\n              onClick: _ctx.handleUpdateBasic\n            }, {\n              default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\" 保存修改 \")])),\n              _: 1 /* STABLE */,\n              __: [18]\n            }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_n_tab_pane, {\n        name: \"oauth\",\n        tab: \"OAuth 设置\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_card, {\n          title: \"OAuth 配置\",\n          class: \"detail-card\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n            class: \"info-label\"\n          }, \"Client ID\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_n_input, {\n            readonly: \"\",\n            value: _ctx.application.client_id\n          }, {\n            suffix: _withCtx(() => [_createVNode(_component_n_button, {\n              text: \"\",\n              onClick: _cache[8] || (_cache[8] = $event => _ctx.copyToClipboard(_ctx.application.client_id))\n            }, {\n              icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n                default: _withCtx(() => [_createVNode(_component_copy)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"value\"])])]), _createElementVNode(\"div\", _hoisted_11, [_cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n            class: \"info-label\"\n          }, \"Client Secret\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_n_input, {\n            readonly: \"\",\n            type: \"password\",\n            value: _ctx.application.client_secret,\n            \"show-password-on\": _ctx.showSecret ? 'mousedown' : ''\n          }, {\n            suffix: _withCtx(() => [_createVNode(_component_n_space, null, {\n              default: _withCtx(() => [_createVNode(_component_n_button, {\n                text: \"\",\n                onClick: _ctx.toggleShowSecret\n              }, {\n                icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n                  default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.showSecret ? 'eye-outline' : 'eye-off-outline')))]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_n_button, {\n                text: \"\",\n                onClick: _cache[9] || (_cache[9] = $event => _ctx.copyToClipboard(_ctx.application.client_secret))\n              }, {\n                icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n                  default: _withCtx(() => [_createVNode(_component_copy)]),\n                  _: 1 /* STABLE */\n                })]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"value\", \"show-password-on\"])])])]), _createVNode(_component_n_divider), _createVNode(_component_n_form, {\n            ref: \"oauthFormRef\",\n            model: _ctx.oauthForm,\n            rules: _ctx.oauthRules,\n            \"label-placement\": \"left\",\n            \"label-width\": \"120px\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_n_form_item, {\n              path: \"redirect_uris\",\n              label: \"重定向 URI\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_dynamic_input, {\n                value: _ctx.oauthForm.redirect_uris,\n                \"onUpdate:value\": _cache[10] || (_cache[10] = $event => _ctx.oauthForm.redirect_uris = $event),\n                \"on-create\": () => '',\n                placeholder: \"请输入重定向 URI\"\n              }, {\n                default: _withCtx(({\n                  index\n                }) => [_createVNode(_component_n_input, {\n                  value: _ctx.oauthForm.redirect_uris[index],\n                  \"onUpdate:value\": $event => _ctx.oauthForm.redirect_uris[index] = $event,\n                  placeholder: \"例如: https://example.com/callback\"\n                }, null, 8 /* PROPS */, [\"value\", \"onUpdate:value\"])]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"value\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"scopes\",\n              label: \"授权范围\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_select, {\n                value: _ctx.oauthForm.scopes,\n                \"onUpdate:value\": _cache[11] || (_cache[11] = $event => _ctx.oauthForm.scopes = $event),\n                multiple: \"\",\n                options: _ctx.scopeOptions,\n                placeholder: \"请选择授权范围\"\n              }, null, 8 /* PROPS */, [\"value\", \"options\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_n_form_item, {\n              path: \"grant_types\",\n              label: \"授权类型\"\n            }, {\n              default: _withCtx(() => [_createVNode(_component_n_select, {\n                value: _ctx.oauthForm.grant_types,\n                \"onUpdate:value\": _cache[12] || (_cache[12] = $event => _ctx.oauthForm.grant_types = $event),\n                multiple: \"\",\n                options: _ctx.grantTypeOptions,\n                placeholder: \"请选择授权类型\"\n              }, null, 8 /* PROPS */, [\"value\", \"options\"])]),\n              _: 1 /* STABLE */\n            }), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_n_button, {\n              type: \"primary\",\n              loading: _ctx.submittingOAuth,\n              onClick: _ctx.handleUpdateOAuth\n            }, {\n              default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\" 保存 OAuth 设置 \")])),\n              _: 1 /* STABLE */,\n              __: [21]\n            }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_n_tab_pane, {\n        name: \"statistics\",\n        tab: \"使用统计\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_card, {\n          title: \"应用使用统计\",\n          class: \"detail-card\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_empty, {\n            description: \"暂无统计数据\"\n          }, {\n            extra: _withCtx(() => _cache[22] || (_cache[22] = [_createElementVNode(\"span\", {\n              class: \"empty-tip\"\n            }, \"应用使用后将显示统计数据\", -1 /* CACHED */)])),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createBlock(_component_n_empty, {\n      key: 1,\n      description: \"应用不存在或已被删除\"\n    }, {\n      extra: _withCtx(() => [_createVNode(_component_n_button, {\n        type: \"primary\",\n        onClick: _cache[13] || (_cache[13] = $event => _ctx.$router.push('/applications'))\n      }, {\n        default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 返回应用列表 \")])),\n        _: 1 /* STABLE */,\n        __: [23]\n      })]),\n      _: 1 /* STABLE */\n    }))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"]), _createCommentVNode(\" 删除确认对话框 \"), _createVNode(_component_n_modal, {\n    show: _ctx.showDeleteModal,\n    \"onUpdate:show\": _cache[15] || (_cache[15] = $event => _ctx.showDeleteModal = $event),\n    preset: \"dialog\",\n    title: \"删除应用\",\n    \"positive-text\": \"确认删除\",\n    \"negative-text\": \"取消\",\n    type: \"error\",\n    onPositiveClick: _ctx.handleDeleteApp,\n    onNegativeClick: _cache[16] || (_cache[16] = $event => _ctx.showDeleteModal = false)\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"p\", null, [_cache[24] || (_cache[24] = _createTextVNode(\"您确定要删除应用 \")), _createElementVNode(\"strong\", null, _toDisplayString(_ctx.application.name), 1 /* TEXT */), _cache[25] || (_cache[25] = _createTextVNode(\" 吗？\"))]), _cache[26] || (_cache[26] = _createElementVNode(\"p\", null, \"此操作不可逆，删除后所有相关的授权和令牌将被撤销。\", -1 /* CACHED */)), _createVNode(_component_n_input, {\n      value: _ctx.deleteConfirmText,\n      \"onUpdate:value\": _cache[14] || (_cache[14] = $event => _ctx.deleteConfirmText = $event),\n      placeholder: \"请输入应用名称以确认删除\",\n      status: _ctx.deleteConfirmText === _ctx.application.name ? 'success' : 'error'\n    }, null, 8 /* PROPS */, [\"value\", \"status\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\", \"onPositiveClick\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_n_button", "quaternary", "circle", "onClick", "_cache", "$event", "_ctx", "$router", "push", "icon", "_withCtx", "_component_n_icon", "_component_arrow_back", "_hoisted_4", "_toDisplayString", "application", "name", "_hoisted_5", "type", "showDeleteConfirm", "_component_n_spin", "show", "loading", "id", "_hoisted_6", "_component_n_tabs", "animated", "_component_n_tab_pane", "tab", "_component_n_card", "title", "_component_n_form", "ref", "model", "formValue", "rules", "_component_n_form_item", "path", "label", "_component_n_input", "value", "placeholder", "description", "autosize", "minRows", "maxRows", "_component_n_select", "options", "iconOptions", "_component_n_color_picker", "icon_color", "homepage_url", "privacy_policy_url", "terms_of_service_url", "_hoisted_7", "submitting", "handleUpdateBasic", "_hoisted_8", "_hoisted_9", "_hoisted_10", "readonly", "client_id", "suffix", "text", "copyToClipboard", "_component_copy", "_hoisted_11", "_hoisted_12", "client_secret", "showSecret", "_component_n_space", "toggleShowSecret", "_createBlock", "_resolveDynamicComponent", "_component_n_divider", "oauthForm", "oauthRules", "_component_n_dynamic_input", "redirect_uris", "on-create", "default", "index", "scopes", "multiple", "scopeOptions", "grant_types", "grantTypeOptions", "_hoisted_13", "submittingOAuth", "handleUpdateOAuth", "_component_n_empty", "extra", "_createCommentVNode", "_component_n_modal", "showDeleteModal", "preset", "onPositiveClick", "handleDeleteApp", "onNegativeClick", "deleteConfirmText", "status"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\ApplicationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"application-detail-container\">\n    <div class=\"page-header\">\n      <div class=\"page-title-section\">\n        <n-button quaternary circle @click=\"$router.push('/applications')\">\n          <template #icon>\n            <n-icon><arrow-back /></n-icon>\n          </template>\n        </n-button>\n        <h2 class=\"page-title\">{{ application.name }}</h2>\n      </div>\n      <div class=\"page-actions\">\n        <n-button type=\"error\" @click=\"showDeleteConfirm\">\n          删除应用\n        </n-button>\n      </div>\n    </div>\n    \n    <n-spin :show=\"loading\">\n      <div v-if=\"application.id\" class=\"application-content\">\n        <n-tabs type=\"line\" animated>\n          <n-tab-pane name=\"basic\" tab=\"基本信息\">\n            <n-card title=\"应用信息\" class=\"detail-card\">\n              <n-form\n                ref=\"formRef\"\n                :model=\"formValue\"\n                :rules=\"rules\"\n                label-placement=\"left\"\n                label-width=\"120px\"\n              >\n                <n-form-item path=\"name\" label=\"应用名称\">\n                  <n-input v-model:value=\"formValue.name\" placeholder=\"请输入应用名称\" />\n                </n-form-item>\n                \n                <n-form-item path=\"description\" label=\"应用描述\">\n                  <n-input\n                    v-model:value=\"formValue.description\"\n                    type=\"textarea\"\n                    placeholder=\"请输入应用描述\"\n                    :autosize=\"{ minRows: 3, maxRows: 5 }\"\n                  />\n                </n-form-item>\n                \n                <n-form-item path=\"icon\" label=\"应用图标\">\n                  <n-select v-model:value=\"formValue.icon\" :options=\"iconOptions\" />\n                </n-form-item>\n                \n                <n-form-item path=\"icon_color\" label=\"图标颜色\">\n                  <n-color-picker v-model:value=\"formValue.icon_color\" />\n                </n-form-item>\n                \n                <n-form-item path=\"homepage_url\" label=\"应用主页\">\n                  <n-input v-model:value=\"formValue.homepage_url\" placeholder=\"请输入应用主页URL\" />\n                </n-form-item>\n                \n                <n-form-item path=\"privacy_policy_url\" label=\"隐私政策\">\n                  <n-input v-model:value=\"formValue.privacy_policy_url\" placeholder=\"请输入隐私政策URL\" />\n                </n-form-item>\n                \n                <n-form-item path=\"terms_of_service_url\" label=\"服务条款\">\n                  <n-input v-model:value=\"formValue.terms_of_service_url\" placeholder=\"请输入服务条款URL\" />\n                </n-form-item>\n                \n                <div class=\"form-actions\">\n                  <n-button type=\"primary\" :loading=\"submitting\" @click=\"handleUpdateBasic\">\n                    保存修改\n                  </n-button>\n                </div>\n              </n-form>\n            </n-card>\n          </n-tab-pane>\n          \n          <n-tab-pane name=\"oauth\" tab=\"OAuth 设置\">\n            <n-card title=\"OAuth 配置\" class=\"detail-card\">\n              <div class=\"oauth-info\">\n                <div class=\"info-item\">\n                  <div class=\"info-label\">Client ID</div>\n                  <div class=\"info-value\">\n                    <n-input readonly :value=\"application.client_id\">\n                      <template #suffix>\n                        <n-button text @click=\"copyToClipboard(application.client_id)\">\n                          <template #icon>\n                            <n-icon><copy /></n-icon>\n                          </template>\n                        </n-button>\n                      </template>\n                    </n-input>\n                  </div>\n                </div>\n                \n                <div class=\"info-item\">\n                  <div class=\"info-label\">Client Secret</div>\n                  <div class=\"info-value\">\n                    <n-input\n                      readonly\n                      type=\"password\"\n                      :value=\"application.client_secret\"\n                      :show-password-on=\"showSecret ? 'mousedown' : ''\"\n                    >\n                      <template #suffix>\n                        <n-space>\n                          <n-button text @click=\"toggleShowSecret\">\n                            <template #icon>\n                              <n-icon>\n                                <component :is=\"showSecret ? 'eye-outline' : 'eye-off-outline'\" />\n                              </n-icon>\n                            </template>\n                          </n-button>\n                          <n-button text @click=\"copyToClipboard(application.client_secret)\">\n                            <template #icon>\n                              <n-icon><copy /></n-icon>\n                            </template>\n                          </n-button>\n                        </n-space>\n                      </template>\n                    </n-input>\n                  </div>\n                </div>\n              </div>\n              \n              <n-divider />\n              \n              <n-form\n                ref=\"oauthFormRef\"\n                :model=\"oauthForm\"\n                :rules=\"oauthRules\"\n                label-placement=\"left\"\n                label-width=\"120px\"\n              >\n                <n-form-item path=\"redirect_uris\" label=\"重定向 URI\">\n                  <n-dynamic-input\n                    v-model:value=\"oauthForm.redirect_uris\"\n                    :on-create=\"() => ''\"\n                    placeholder=\"请输入重定向 URI\"\n                  >\n                    <template #default=\"{ index }\">\n                      <n-input\n                        v-model:value=\"oauthForm.redirect_uris[index]\"\n                        placeholder=\"例如: https://example.com/callback\"\n                      />\n                    </template>\n                  </n-dynamic-input>\n                </n-form-item>\n                \n                <n-form-item path=\"scopes\" label=\"授权范围\">\n                  <n-select\n                    v-model:value=\"oauthForm.scopes\"\n                    multiple\n                    :options=\"scopeOptions\"\n                    placeholder=\"请选择授权范围\"\n                  />\n                </n-form-item>\n                \n                <n-form-item path=\"grant_types\" label=\"授权类型\">\n                  <n-select\n                    v-model:value=\"oauthForm.grant_types\"\n                    multiple\n                    :options=\"grantTypeOptions\"\n                    placeholder=\"请选择授权类型\"\n                  />\n                </n-form-item>\n                \n                <div class=\"form-actions\">\n                  <n-button type=\"primary\" :loading=\"submittingOAuth\" @click=\"handleUpdateOAuth\">\n                    保存 OAuth 设置\n                  </n-button>\n                </div>\n              </n-form>\n            </n-card>\n          </n-tab-pane>\n          \n          <n-tab-pane name=\"statistics\" tab=\"使用统计\">\n            <n-card title=\"应用使用统计\" class=\"detail-card\">\n              <n-empty description=\"暂无统计数据\">\n                <template #extra>\n                  <span class=\"empty-tip\">应用使用后将显示统计数据</span>\n                </template>\n              </n-empty>\n            </n-card>\n          </n-tab-pane>\n        </n-tabs>\n      </div>\n      \n      <n-empty v-else description=\"应用不存在或已被删除\">\n        <template #extra>\n          <n-button type=\"primary\" @click=\"$router.push('/applications')\">\n            返回应用列表\n          </n-button>\n        </template>\n      </n-empty>\n    </n-spin>\n    \n    <!-- 删除确认对话框 -->\n    <n-modal\n      v-model:show=\"showDeleteModal\"\n      preset=\"dialog\"\n      title=\"删除应用\"\n      positive-text=\"确认删除\"\n      negative-text=\"取消\"\n      type=\"error\"\n      @positive-click=\"handleDeleteApp\"\n      @negative-click=\"showDeleteModal = false\"\n    >\n      <template #default>\n        <p>您确定要删除应用 <strong>{{ application.name }}</strong> 吗？</p>\n        <p>此操作不可逆，删除后所有相关的授权和令牌将被撤销。</p>\n        <n-input\n          v-model:value=\"deleteConfirmText\"\n          placeholder=\"请输入应用名称以确认删除\"\n          :status=\"deleteConfirmText === application.name ? 'success' : 'error'\"\n        />\n      </template>\n    </n-modal>\n  </div>\n</template>\n\n<script>\nimport { defineComponent, ref, reactive, onMounted } from 'vue'\nimport { useRoute, useRouter } from 'vue-router'\nimport { \n  NButton, \n  NCard, \n  NForm,\n  NFormItem,\n  NInput,\n  NSelect,\n  NTabs,\n  NTabPane,\n  NSpin,\n  NEmpty,\n  NIcon,\n  NSpace,\n  NDynamicInput,\n  NColorPicker,\n  NModal,\n  NDivider,\n  useMessage\n} from 'naive-ui'\nimport { \n  ArrowBack,\n  Copy,\n  EyeOutline,\n  EyeOffOutline\n} from '@vicons/ionicons5'\nimport axios from 'axios'\n\nexport default defineComponent({\n  name: 'ApplicationDetailPage',\n  components: {\n    NButton,\n    NCard,\n    NForm,\n    NFormItem,\n    NInput,\n    NSelect,\n    NTabs,\n    NTabPane,\n    NSpin,\n    NEmpty,\n    NIcon,\n    NSpace,\n    NDynamicInput,\n    NColorPicker,\n    NModal,\n    NDivider,\n    ArrowBack,\n    Copy,\n    EyeOutline,\n    EyeOffOutline\n  },\n  setup() {\n    const route = useRoute()\n    const router = useRouter()\n    const message = useMessage()\n    const loading = ref(false)\n    const submitting = ref(false)\n    const submittingOAuth = ref(false)\n    const showSecret = ref(false)\n    const showDeleteModal = ref(false)\n    const deleteConfirmText = ref('')\n    \n    const application = reactive({\n      id: '',\n      name: '',\n      description: '',\n      icon: 'apps',\n      icon_color: '#2080f0',\n      homepage_url: '',\n      privacy_policy_url: '',\n      terms_of_service_url: '',\n      client_id: '',\n      client_secret: '',\n      redirect_uris: [],\n      scopes: [],\n      grant_types: []\n    })\n    \n    const formValue = reactive({\n      name: '',\n      description: '',\n      icon: 'apps',\n      icon_color: '#2080f0',\n      homepage_url: '',\n      privacy_policy_url: '',\n      terms_of_service_url: ''\n    })\n    \n    const oauthForm = reactive({\n      redirect_uris: [],\n      scopes: [],\n      grant_types: []\n    })\n    \n    const rules = {\n      name: [\n        { required: true, message: '请输入应用名称', trigger: 'blur' },\n        { min: 2, max: 50, message: '应用名称长度应在2-50个字符之间', trigger: 'blur' }\n      ],\n      description: [\n        { max: 200, message: '应用描述长度不能超过200个字符', trigger: 'blur' }\n      ],\n      homepage_url: [\n        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }\n      ],\n      privacy_policy_url: [\n        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }\n      ],\n      terms_of_service_url: [\n        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }\n      ]\n    }\n    \n    const oauthRules = {\n      redirect_uris: [\n        {\n          type: 'array',\n          min: 1,\n          message: '至少需要一个重定向URI',\n          trigger: 'change'\n        },\n        {\n          validator: (rule, value) => {\n            if (!value) return true\n            return value.every(uri => {\n              return /^https?:\\/\\/.+/i.test(uri) || uri === 'urn:ietf:wg:oauth:2.0:oob'\n            })\n          },\n          message: '重定向URI必须是有效的URL或特殊值urn:ietf:wg:oauth:2.0:oob',\n          trigger: 'change'\n        }\n      ]\n    }\n    \n    // 图标选项\n    const iconOptions = [\n      { label: '应用', value: 'apps' },\n      { label: '公文包', value: 'briefcase' },\n      { label: '聊天', value: 'chat' },\n      { label: '云', value: 'cloud' },\n      { label: '桌面', value: 'desktop' },\n      { label: '地球', value: 'globe' },\n      { label: '网格', value: 'grid' },\n      { label: '图层', value: 'layers' },\n      { label: '图表', value: 'chart' },\n      { label: '服务器', value: 'server' }\n    ]\n    \n    // 授权范围选项\n    const scopeOptions = [\n      { label: '基本信息 (profile)', value: 'profile' },\n      { label: '邮箱 (email)', value: 'email' },\n      { label: '地址 (address)', value: 'address' },\n      { label: '电话 (phone)', value: 'phone' }\n    ]\n    \n    // 授权类型选项\n    const grantTypeOptions = [\n      { label: '授权码模式 (authorization_code)', value: 'authorization_code' },\n      { label: '简化模式 (implicit)', value: 'implicit' },\n      { label: '密码模式 (password)', value: 'password' },\n      { label: '客户端凭证模式 (client_credentials)', value: 'client_credentials' },\n      { label: '刷新令牌 (refresh_token)', value: 'refresh_token' }\n    ]\n    \n    // 复制到剪贴板\n    const copyToClipboard = (text) => {\n      navigator.clipboard.writeText(text)\n        .then(() => {\n          message.success('已复制到剪贴板')\n        })\n        .catch(() => {\n          message.error('复制失败')\n        })\n    }\n    \n    // 切换显示密钥\n    const toggleShowSecret = () => {\n      showSecret.value = !showSecret.value\n    }\n    \n    // 加载应用详情\n    const loadApplicationDetail = async () => {\n      const appId = route.params.id\n      if (!appId) {\n        message.error('应用ID不存在')\n        return\n      }\n      \n      loading.value = true\n      try {\n        const response = await axios.get(`/oauth/clients/${appId}`)\n        const appData = response.data.client\n        \n        // 更新应用数据\n        Object.assign(application, appData)\n        \n        // 更新表单数据\n        formValue.name = appData.name || ''\n        formValue.description = appData.description || ''\n        formValue.icon = appData.icon || 'apps'\n        formValue.icon_color = appData.icon_color || '#2080f0'\n        formValue.homepage_url = appData.homepage_url || ''\n        formValue.privacy_policy_url = appData.privacy_policy_url || ''\n        formValue.terms_of_service_url = appData.terms_of_service_url || ''\n        \n        // 更新OAuth表单数据\n        oauthForm.redirect_uris = appData.redirect_uris || []\n        oauthForm.scopes = appData.scopes || []\n        oauthForm.grant_types = appData.grant_types || []\n      } catch (error) {\n        message.error('获取应用详情失败')\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    // 更新基本信息\n    const handleUpdateBasic = async () => {\n      submitting.value = true\n      try {\n        await axios.put(`/oauth/clients/${application.id}`, formValue)\n        message.success('应用信息更新成功')\n        loadApplicationDetail()\n      } catch (error) {\n        message.error(error.response?.data?.message || '更新应用信息失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    // 更新OAuth设置\n    const handleUpdateOAuth = async () => {\n      submittingOAuth.value = true\n      try {\n        await axios.put(`/oauth/clients/${application.id}/oauth`, oauthForm)\n        message.success('OAuth设置更新成功')\n        loadApplicationDetail()\n      } catch (error) {\n        message.error(error.response?.data?.message || '更新OAuth设置失败')\n      } finally {\n        submittingOAuth.value = false\n      }\n    }\n    \n    // 显示删除确认对话框\n    const showDeleteConfirm = () => {\n      deleteConfirmText.value = ''\n      showDeleteModal.value = true\n    }\n    \n    // 删除应用\n    const handleDeleteApp = async () => {\n      if (deleteConfirmText.value !== application.name) {\n        message.error('应用名称输入不正确，无法删除')\n        return\n      }\n      \n      try {\n        await axios.delete(`/oauth/clients/${application.id}`)\n        message.success('应用已成功删除')\n        router.push('/applications')\n      } catch (error) {\n        message.error(error.response?.data?.message || '删除应用失败')\n      }\n    }\n    \n    onMounted(() => {\n      loadApplicationDetail()\n    })\n    \n    return {\n      loading,\n      application,\n      formValue,\n      oauthForm,\n      rules,\n      oauthRules,\n      iconOptions,\n      scopeOptions,\n      grantTypeOptions,\n      submitting,\n      submittingOAuth,\n      showSecret,\n      showDeleteModal,\n      deleteConfirmText,\n      copyToClipboard,\n      toggleShowSecret,\n      handleUpdateBasic,\n      handleUpdateOAuth,\n      showDeleteConfirm,\n      handleDeleteApp\n    }\n  }\n})\n</script>\n\n<style scoped>\n.application-detail-container {\n  max-width: 1000px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n}\n\n.page-title-section {\n  display: flex;\n  align-items: center;\n}\n\n.page-title {\n  margin: 0 0 0 8px;\n  font-size: 24px;\n  font-weight: 500;\n}\n\n.detail-card {\n  margin-bottom: 24px;\n}\n\n.form-actions {\n  display: flex;\n  justify-content: center;\n  margin-top: 24px;\n}\n\n.oauth-info {\n  margin-bottom: 24px;\n}\n\n.info-item {\n  margin-bottom: 16px;\n}\n\n.info-label {\n  font-size: 14px;\n  font-weight: 500;\n  margin-bottom: 8px;\n}\n\n.empty-tip {\n  color: rgba(0, 0, 0, 0.45);\n  font-size: 14px;\n}\n</style> "], "mappings": ";;;EACOA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAoB;;EAMzBA,KAAK,EAAC;AAAY;;EAEnBA,KAAK,EAAC;AAAc;;;EAQEA,KAAK,EAAC;;;EA4ClBA,KAAK,EAAC;AAAc;;EAWtBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAY;;EAapBA,KAAK,EAAC;AAAW;;EAEfA,KAAK,EAAC;AAAY;;EAsEpBA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;;;;;;uBAjKvCC,mBAAA,CAoNM,OApNNC,UAoNM,GAnNJC,mBAAA,CAcM,OAdNC,UAcM,GAbJD,mBAAA,CAOM,OAPNE,UAOM,GANJC,YAAA,CAIWC,mBAAA;IAJDC,UAAU,EAAV,EAAU;IAACC,MAAM,EAAN,EAAM;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;IACnCC,IAAI,EAAAC,QAAA,CACb,MAA+B,CAA/BX,YAAA,CAA+BY,iBAAA;wBAAvB,MAAc,CAAdZ,YAAA,CAAca,qBAAA,E;;;;MAG1BhB,mBAAA,CAAkD,MAAlDiB,UAAkD,EAAAC,gBAAA,CAAxBR,IAAA,CAAAS,WAAW,CAACC,IAAI,iB,GAE5CpB,mBAAA,CAIM,OAJNqB,UAIM,GAHJlB,YAAA,CAEWC,mBAAA;IAFDkB,IAAI,EAAC,OAAO;IAAEf,OAAK,EAAEG,IAAA,CAAAa;;sBAAmB,MAElDf,MAAA,SAAAA,MAAA,Q,iBAFkD,QAElD,E;;;sCAIJL,YAAA,CA4KSqB,iBAAA;IA5KAC,IAAI,EAAEf,IAAA,CAAAgB;EAAO;sBACpB,MAkKM,CAlKKhB,IAAA,CAAAS,WAAW,CAACQ,EAAE,I,cAAzB7B,mBAAA,CAkKM,OAlKN8B,UAkKM,GAjKJzB,YAAA,CAgKS0B,iBAAA;MAhKDP,IAAI,EAAC,MAAM;MAACQ,QAAQ,EAAR;;wBAClB,MAiDa,CAjDb3B,YAAA,CAiDa4B,qBAAA;QAjDDX,IAAI,EAAC,OAAO;QAACY,GAAG,EAAC;;0BAC3B,MA+CS,CA/CT7B,YAAA,CA+CS8B,iBAAA;UA/CDC,KAAK,EAAC,MAAM;UAACrC,KAAK,EAAC;;4BACzB,MA6CS,CA7CTM,YAAA,CA6CSgC,iBAAA;YA5CPC,GAAG,EAAC,SAAS;YACZC,KAAK,EAAE3B,IAAA,CAAA4B,SAAS;YAChBC,KAAK,EAAE7B,IAAA,CAAA6B,KAAK;YACb,iBAAe,EAAC,MAAM;YACtB,aAAW,EAAC;;8BAEZ,MAEc,CAFdpC,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC;;gCAC7B,MAAgE,CAAhEvC,YAAA,CAAgEwC,kBAAA;gBAA/CC,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAAClB,IAAI;sEAAdV,IAAA,CAAA4B,SAAS,CAAClB,IAAI,GAAAX,MAAA;gBAAEoC,WAAW,EAAC;;;gBAGtD1C,YAAA,CAOcqC,sBAAA;cAPDC,IAAI,EAAC,aAAa;cAACC,KAAK,EAAC;;gCACpC,MAKE,CALFvC,YAAA,CAKEwC,kBAAA;gBAJQC,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACQ,WAAW;sEAArBpC,IAAA,CAAA4B,SAAS,CAACQ,WAAW,GAAArC,MAAA;gBACpCa,IAAI,EAAC,UAAU;gBACfuB,WAAW,EAAC,SAAS;gBACpBE,QAAQ,EAAE;kBAAAC,OAAA;kBAAAC,OAAA;gBAAA;;;gBAIf9C,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,MAAM;cAACC,KAAK,EAAC;;gCAC7B,MAAkE,CAAlEvC,YAAA,CAAkE+C,mBAAA;gBAAhDN,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACzB,IAAI;sEAAdH,IAAA,CAAA4B,SAAS,CAACzB,IAAI,GAAAJ,MAAA;gBAAG0C,OAAO,EAAEzC,IAAA,CAAA0C;;;gBAGrDjD,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,YAAY;cAACC,KAAK,EAAC;;gCACnC,MAAuD,CAAvDvC,YAAA,CAAuDkD,yBAAA;gBAA/BT,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACgB,UAAU;sEAApB5C,IAAA,CAAA4B,SAAS,CAACgB,UAAU,GAAA7C,MAAA;;;gBAGrDN,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,cAAc;cAACC,KAAK,EAAC;;gCACrC,MAA2E,CAA3EvC,YAAA,CAA2EwC,kBAAA;gBAA1DC,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACiB,YAAY;sEAAtB7C,IAAA,CAAA4B,SAAS,CAACiB,YAAY,GAAA9C,MAAA;gBAAEoC,WAAW,EAAC;;;gBAG9D1C,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,oBAAoB;cAACC,KAAK,EAAC;;gCAC3C,MAAiF,CAAjFvC,YAAA,CAAiFwC,kBAAA;gBAAhEC,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACkB,kBAAkB;sEAA5B9C,IAAA,CAAA4B,SAAS,CAACkB,kBAAkB,GAAA/C,MAAA;gBAAEoC,WAAW,EAAC;;;gBAGpE1C,YAAA,CAEcqC,sBAAA;cAFDC,IAAI,EAAC,sBAAsB;cAACC,KAAK,EAAC;;gCAC7C,MAAmF,CAAnFvC,YAAA,CAAmFwC,kBAAA;gBAAlEC,KAAK,EAAElC,IAAA,CAAA4B,SAAS,CAACmB,oBAAoB;sEAA9B/C,IAAA,CAAA4B,SAAS,CAACmB,oBAAoB,GAAAhD,MAAA;gBAAEoC,WAAW,EAAC;;;gBAGtE7C,mBAAA,CAIM,OAJN0D,UAIM,GAHJvD,YAAA,CAEWC,mBAAA;cAFDkB,IAAI,EAAC,SAAS;cAAEI,OAAO,EAAEhB,IAAA,CAAAiD,UAAU;cAAGpD,OAAK,EAAEG,IAAA,CAAAkD;;gCAAmB,MAE1EpD,MAAA,SAAAA,MAAA,Q,iBAF0E,QAE1E,E;;;;;;;;;UAMRL,YAAA,CAiGa4B,qBAAA;QAjGDX,IAAI,EAAC,OAAO;QAACY,GAAG,EAAC;;0BAC3B,MA+FS,CA/FT7B,YAAA,CA+FS8B,iBAAA;UA/FDC,KAAK,EAAC,UAAU;UAACrC,KAAK,EAAC;;4BAC7B,MA4CM,CA5CNG,mBAAA,CA4CM,OA5CN6D,UA4CM,GA3CJ7D,mBAAA,CAaM,OAbN8D,UAaM,G,4BAZJ9D,mBAAA,CAAuC;YAAlCH,KAAK,EAAC;UAAY,GAAC,WAAS,qBACjCG,mBAAA,CAUM,OAVN+D,WAUM,GATJ5D,YAAA,CAQUwC,kBAAA;YARDqB,QAAQ,EAAR,EAAQ;YAAEpB,KAAK,EAAElC,IAAA,CAAAS,WAAW,CAAC8C;;YACzBC,MAAM,EAAApD,QAAA,CACf,MAIW,CAJXX,YAAA,CAIWC,mBAAA;cAJD+D,IAAI,EAAJ,EAAI;cAAE5D,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAA0D,eAAe,CAAC1D,IAAA,CAAAS,WAAW,CAAC8C,SAAS;;cAC/CpD,IAAI,EAAAC,QAAA,CACb,MAAyB,CAAzBX,YAAA,CAAyBY,iBAAA;kCAAjB,MAAQ,CAARZ,YAAA,CAAQkE,eAAA,E;;;;;;4CAQ5BrE,mBAAA,CA2BM,OA3BNsE,WA2BM,G,4BA1BJtE,mBAAA,CAA2C;YAAtCH,KAAK,EAAC;UAAY,GAAC,eAAa,qBACrCG,mBAAA,CAwBM,OAxBNuE,WAwBM,GAvBJpE,YAAA,CAsBUwC,kBAAA;YArBRqB,QAAQ,EAAR,EAAQ;YACR1C,IAAI,EAAC,UAAU;YACdsB,KAAK,EAAElC,IAAA,CAAAS,WAAW,CAACqD,aAAa;YAChC,kBAAgB,EAAE9D,IAAA,CAAA+D,UAAU;;YAElBP,MAAM,EAAApD,QAAA,CACf,MAaU,CAbVX,YAAA,CAaUuE,kBAAA;gCAZR,MAMW,CANXvE,YAAA,CAMWC,mBAAA;gBAND+D,IAAI,EAAJ,EAAI;gBAAE5D,OAAK,EAAEG,IAAA,CAAAiE;;gBACV9D,IAAI,EAAAC,QAAA,CACb,MAES,CAFTX,YAAA,CAESY,iBAAA;oCADP,MAAkE,E,cAAlE6D,YAAA,CAAkEC,wBAAA,CAAlDnE,IAAA,CAAA+D,UAAU,wC;;;;8CAIhCtE,YAAA,CAIWC,mBAAA;gBAJD+D,IAAI,EAAJ,EAAI;gBAAE5D,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAA0D,eAAe,CAAC1D,IAAA,CAAAS,WAAW,CAACqD,aAAa;;gBACnD3D,IAAI,EAAAC,QAAA,CACb,MAAyB,CAAzBX,YAAA,CAAyBY,iBAAA;oCAAjB,MAAQ,CAARZ,YAAA,CAAQkE,eAAA,E;;;;;;;;kEAUhClE,YAAA,CAAa2E,oBAAA,GAEb3E,YAAA,CA6CSgC,iBAAA;YA5CPC,GAAG,EAAC,cAAc;YACjBC,KAAK,EAAE3B,IAAA,CAAAqE,SAAS;YAChBxC,KAAK,EAAE7B,IAAA,CAAAsE,UAAU;YAClB,iBAAe,EAAC,MAAM;YACtB,aAAW,EAAC;;8BAEZ,MAac,CAbd7E,YAAA,CAacqC,sBAAA;cAbDC,IAAI,EAAC,eAAe;cAACC,KAAK,EAAC;;gCACtC,MAWkB,CAXlBvC,YAAA,CAWkB8E,0BAAA;gBAVRrC,KAAK,EAAElC,IAAA,CAAAqE,SAAS,CAACG,aAAa;wEAAvBxE,IAAA,CAAAqE,SAAS,CAACG,aAAa,GAAAzE,MAAA;gBACrC,WAAS,EAAE0E,CAAA,OAAQ;gBACpBtC,WAAW,EAAC;;gBAEDuC,OAAO,EAAAtE,QAAA,CAChB,CAGE;kBAJkBuE;gBAAK,OACzBlF,YAAA,CAGEwC,kBAAA;kBAFQC,KAAK,EAAElC,IAAA,CAAAqE,SAAS,CAACG,aAAa,CAACG,KAAK;8CAA7B3E,IAAA,CAAAqE,SAAS,CAACG,aAAa,CAACG,KAAK,IAAA5E,MAAA;kBAC5CoC,WAAW,EAAC;;;;;gBAMpB1C,YAAA,CAOcqC,sBAAA;cAPDC,IAAI,EAAC,QAAQ;cAACC,KAAK,EAAC;;gCAC/B,MAKE,CALFvC,YAAA,CAKE+C,mBAAA;gBAJQN,KAAK,EAAElC,IAAA,CAAAqE,SAAS,CAACO,MAAM;wEAAhB5E,IAAA,CAAAqE,SAAS,CAACO,MAAM,GAAA7E,MAAA;gBAC/B8E,QAAQ,EAAR,EAAQ;gBACPpC,OAAO,EAAEzC,IAAA,CAAA8E,YAAY;gBACtB3C,WAAW,EAAC;;;gBAIhB1C,YAAA,CAOcqC,sBAAA;cAPDC,IAAI,EAAC,aAAa;cAACC,KAAK,EAAC;;gCACpC,MAKE,CALFvC,YAAA,CAKE+C,mBAAA;gBAJQN,KAAK,EAAElC,IAAA,CAAAqE,SAAS,CAACU,WAAW;wEAArB/E,IAAA,CAAAqE,SAAS,CAACU,WAAW,GAAAhF,MAAA;gBACpC8E,QAAQ,EAAR,EAAQ;gBACPpC,OAAO,EAAEzC,IAAA,CAAAgF,gBAAgB;gBAC1B7C,WAAW,EAAC;;;gBAIhB7C,mBAAA,CAIM,OAJN2F,WAIM,GAHJxF,YAAA,CAEWC,mBAAA;cAFDkB,IAAI,EAAC,SAAS;cAAEI,OAAO,EAAEhB,IAAA,CAAAkF,eAAe;cAAGrF,OAAK,EAAEG,IAAA,CAAAmF;;gCAAmB,MAE/ErF,MAAA,SAAAA,MAAA,Q,iBAF+E,eAE/E,E;;;;;;;;;UAMRL,YAAA,CAQa4B,qBAAA;QARDX,IAAI,EAAC,YAAY;QAACY,GAAG,EAAC;;0BAChC,MAMS,CANT7B,YAAA,CAMS8B,iBAAA;UANDC,KAAK,EAAC,QAAQ;UAACrC,KAAK,EAAC;;4BAC3B,MAIU,CAJVM,YAAA,CAIU2F,kBAAA;YAJDhD,WAAW,EAAC;UAAQ;YAChBiD,KAAK,EAAAjF,QAAA,CACd,MAA2CN,MAAA,SAAAA,MAAA,QAA3CR,mBAAA,CAA2C;cAArCH,KAAK,EAAC;YAAW,GAAC,cAAY,mB;;;;;;;;2BAQhD+E,YAAA,CAMUkB,kBAAA;;MANMhD,WAAW,EAAC;;MACfiD,KAAK,EAAAjF,QAAA,CACd,MAEW,CAFXX,YAAA,CAEWC,mBAAA;QAFDkB,IAAI,EAAC,SAAS;QAAEf,OAAK,EAAAC,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;0BAAmB,MAEhEJ,MAAA,SAAAA,MAAA,Q,iBAFgE,UAEhE,E;;;;;;;+BAKNwF,mBAAA,aAAgB,EAChB7F,YAAA,CAmBU8F,kBAAA;IAlBAxE,IAAI,EAAEf,IAAA,CAAAwF,eAAe;2DAAfxF,IAAA,CAAAwF,eAAe,GAAAzF,MAAA;IAC7B0F,MAAM,EAAC,QAAQ;IACfjE,KAAK,EAAC,MAAM;IACZ,eAAa,EAAC,MAAM;IACpB,eAAa,EAAC,IAAI;IAClBZ,IAAI,EAAC,OAAO;IACX8E,eAAc,EAAE1F,IAAA,CAAA2F,eAAe;IAC/BC,eAAc,EAAA9F,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,IAAA,CAAAwF,eAAe;;IAErBd,OAAO,EAAAtE,QAAA,CAChB,MAA0D,CAA1Dd,mBAAA,CAA0D,Y,6CAAvD,WAAS,IAAAA,mBAAA,CAAuC,gBAAAkB,gBAAA,CAA5BR,IAAA,CAAAS,WAAW,CAACC,IAAI,kB,6CAAY,KAAG,G,+BACtDpB,mBAAA,CAAgC,WAA7B,2BAAyB,qBAC5BG,YAAA,CAIEwC,kBAAA;MAHQC,KAAK,EAAElC,IAAA,CAAA6F,iBAAiB;8DAAjB7F,IAAA,CAAA6F,iBAAiB,GAAA9F,MAAA;MAChCoC,WAAW,EAAC,cAAc;MACzB2D,MAAM,EAAE9F,IAAA,CAAA6F,iBAAiB,KAAK7F,IAAA,CAAAS,WAAW,CAACC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}