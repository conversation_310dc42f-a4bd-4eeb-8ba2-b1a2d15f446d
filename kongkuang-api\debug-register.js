const { connectDB } = require('./config/db');
const { User, VerificationCode } = require('./models');
const { Op } = require('sequelize');

async function debugRegister() {
  try {
    await connectDB();
    console.log('数据库连接成功');
    
    const email = '<EMAIL>';
    const username = 'XIAOZENG';
    const code = '354498';
    
    console.log('\n=== 调试注册问题 ===');
    console.log('邮箱:', email);
    console.log('用户名:', username);
    console.log('验证码:', code);
    
    // 1. 检查验证码
    console.log('\n1. 检查验证码...');
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: email,
        code: code,
        type: 'register',
        contact_type: 'email',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (verificationCode) {
      console.log('✓ 验证码有效');
      console.log('验证码信息:', {
        code: verificationCode.code,
        expires_at: verificationCode.expires_at,
        used: verificationCode.used,
        created_at: verificationCode.created_at
      });
    } else {
      console.log('✗ 验证码无效或已过期');
      
      // 查找所有相关验证码
      const allCodes = await VerificationCode.findAll({
        where: {
          contact: email,
          type: 'register',
          contact_type: 'email'
        },
        order: [['created_at', 'DESC']],
        limit: 5
      });
      
      console.log('最近的验证码:');
      allCodes.forEach((c, index) => {
        console.log(`${index + 1}. 代码: ${c.code}, 过期时间: ${c.expires_at}, 已使用: ${c.used}, 创建时间: ${c.created_at}`);
      });
    }
    
    // 2. 检查用户是否存在
    console.log('\n2. 检查用户是否存在...');
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { username: username },
          { email: email }
        ]
      }
    });
    
    if (existingUser) {
      console.log('✗ 用户已存在');
      console.log('现有用户信息:', {
        id: existingUser.id,
        username: existingUser.username,
        email: existingUser.email,
        created_at: existingUser.created_at
      });
      
      if (existingUser.username === username) {
        console.log('冲突原因: 用户名已存在');
      }
      if (existingUser.email === email) {
        console.log('冲突原因: 邮箱已被注册');
      }
    } else {
      console.log('✓ 用户不存在，可以注册');
    }
    
    // 3. 生成新的验证码用于测试
    console.log('\n3. 生成新的测试验证码...');
    const newCode = Math.floor(100000 + Math.random() * 900000).toString();
    const testEmail = 'test' + Date.now() + '@example.com';
    
    await VerificationCode.create({
      contact: testEmail,
      code: newCode,
      type: 'register',
      contact_type: 'email',
      expires_at: new Date(Date.now() + 10 * 60 * 1000), // 10分钟后过期
      used: false,
      ip: '127.0.0.1'
    });
    
    console.log('测试验证码已生成:');
    console.log('邮箱:', testEmail);
    console.log('验证码:', newCode);
    console.log('可以用这个进行测试注册');
    
    process.exit(0);
  } catch (error) {
    console.error('调试失败:', error);
    process.exit(1);
  }
}

debugRegister();
