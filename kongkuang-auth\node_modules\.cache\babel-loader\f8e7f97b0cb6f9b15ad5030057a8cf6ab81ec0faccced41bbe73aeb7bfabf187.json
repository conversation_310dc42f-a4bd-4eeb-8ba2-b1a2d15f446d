{"ast": null, "code": "import { renderSlot as _renderSlot, resolveComponent as _resolveComponent, normalizeStyle as _normalizeStyle, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_n_scrollbar = _resolveComponent(\"n-scrollbar\");\n  return _openBlock(), _createBlock(_component_n_scrollbar, {\n    class: \"page-scrollbar\",\n    style: _normalizeStyle(_ctx.scrollbarStyle)\n  }, {\n    default: _withCtx(() => [_renderSlot(_ctx.$slots, \"default\", {}, undefined, true)]),\n    _: 3 /* FORWARDED */\n  }, 8 /* PROPS */, [\"style\"]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_n_scrollbar", "class", "style", "_normalizeStyle", "_ctx", "scrollbarStyle", "_renderSlot", "$slots", "undefined"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\PageScrollbar.vue"], "sourcesContent": ["<template>\n  <n-scrollbar class=\"page-scrollbar\" :style=\"scrollbarStyle\">\n    <slot></slot>\n  </n-scrollbar>\n</template>\n\n<script>\nimport { defineComponent, computed } from 'vue'\nimport { NScrollbar } from 'naive-ui'\n\nexport default defineComponent({\n  name: 'PageScrollbar',\n  components: {\n    NScrollbar\n  },\n  props: {\n    height: {\n      type: String,\n      default: '100vh'\n    }\n  },\n  setup(props) {\n    const scrollbarStyle = computed(() => ({\n      height: props.height,\n      width: '100%'\n    }))\n\n    return {\n      scrollbarStyle\n    }\n  }\n})\n</script>\n\n<style scoped>\n.page-scrollbar {\n  height: 100vh;\n  width: 100%;\n}\n\n:deep(.n-scrollbar-content) {\n  min-height: 100%;\n}\n</style>"], "mappings": ";;;uBACEA,YAAA,CAEcC,sBAAA;IAFDC,KAAK,EAAC,gBAAgB;IAAEC,KAAK,EAAAC,eAAA,CAAEC,IAAA,CAAAC,cAAc;;sBACxD,MAAa,CAAbC,WAAA,CAAaF,IAAA,CAAAG,MAAA,iBAAAC,SAAA,Q", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}