{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveDynamicComponent as _resolveDynamicComponent, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"email-item\"\n};\nconst _hoisted_5 = {\n  class: \"side-cards\"\n};\nconst _hoisted_6 = {\n  class: \"services-content\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"service-list\"\n};\nconst _hoisted_8 = {\n  class: \"service-name\"\n};\nconst _hoisted_9 = {\n  key: 1,\n  class: \"no-services\"\n};\nconst _hoisted_10 = {\n  class: \"security-dashboard-compact\"\n};\nconst _hoisted_11 = {\n  class: \"security-gauge-compact\"\n};\nconst _hoisted_12 = {\n  class: \"gauge-svg-compact\",\n  viewBox: \"0 0 120 80\",\n  width: \"120\",\n  height: \"80\"\n};\nconst _hoisted_13 = [\"stroke\"];\nconst _hoisted_14 = [\"d\", \"stroke\"];\nconst _hoisted_15 = {\n  x: \"60\",\n  y: \"55\",\n  \"text-anchor\": \"middle\",\n  class: \"gauge-score-compact\"\n};\nconst _hoisted_16 = {\n  x: \"60\",\n  y: \"70\",\n  \"text-anchor\": \"middle\",\n  class: \"gauge-label-compact\"\n};\nconst _hoisted_17 = {\n  class: \"security-items-compact\"\n};\nconst _hoisted_18 = {\n  class: \"item-name-compact\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [1]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 3,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"ID\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: $setup.userStore.user?.level2_verified ? 'success' : 'warning',\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.last_login || $setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录 IP\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"正常\")])),\n                _: 1 /* STABLE */,\n                __: [2]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [3]\n              })])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-top\": \"16px\"\n            }\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 更改密码 \")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" 授权服务卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"可使用空旷账户登录的服务\",\n          bordered: false,\n          class: \"right-card services-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [$setup.recentApps && $setup.recentApps.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps.slice(0, 3), app => {\n            return _openBlock(), _createElementBlock(\"div\", {\n              key: app.id,\n              class: \"service-item-compact\"\n            }, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n              class: \"service-dot\"\n            }, null, -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_8, _toDisplayString(app.name), 1 /* TEXT */)]);\n          }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode($setup[\"NIcon\"], {\n            size: \"24\",\n            color: \"#d0d0d0\"\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"AppsOutline\"])]),\n            _: 1 /* STABLE */\n          }), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"暂无服务\", -1 /* CACHED */))]))])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 账户安全指标卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"安全指标\",\n          bordered: false,\n          class: \"right-card security-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_createCommentVNode(\" 紧凑型安全评分仪表盘 \"), _createElementVNode(\"div\", _hoisted_11, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_12, [_createCommentVNode(\" 背景弧线 \"), _createElementVNode(\"path\", {\n            d: \"M 15 65 A 45 45 0 0 1 105 65\",\n            fill: \"none\",\n            stroke: $setup.isDarkMode ? '#3a3a3a' : '#e0e0e0',\n            \"stroke-width\": \"6\",\n            \"stroke-linecap\": \"round\"\n          }, null, 8 /* PROPS */, _hoisted_13), _createCommentVNode(\" 进度弧线 \"), _createElementVNode(\"path\", {\n            d: $setup.getCompactSecurityArcPath($setup.securityScore),\n            fill: \"none\",\n            stroke: $setup.getSecurityColor($setup.securityScore),\n            \"stroke-width\": \"6\",\n            \"stroke-linecap\": \"round\",\n            class: \"security-arc\"\n          }, null, 8 /* PROPS */, _hoisted_14), _createCommentVNode(\" 中心文字 \"), _createElementVNode(\"text\", _hoisted_15, _toDisplayString($setup.securityScore) + \"% \", 1 /* TEXT */), _createElementVNode(\"text\", _hoisted_16, _toDisplayString($setup.getSecurityLevelText($setup.securityScore)), 1 /* TEXT */)]))]), _createCommentVNode(\" 紧凑型安全项目列表 \"), _createElementVNode(\"div\", _hoisted_17, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.securityItems, item => {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"security-item-compact\",\n              key: item.key\n            }, [_createVNode($setup[\"NIcon\"], {\n              size: \"12\",\n              color: item.status ? '#18a058' : '#d03050'\n            }, {\n              default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(item.status ? 'checkmark-circle-outline' : 'close-circle-outline')))]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"]), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(item.name), 1 /* TEXT */)]);\n          }), 128 /* KEYED_FRAGMENT */))])])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "viewBox", "width", "height", "x", "y", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "column", "label", "id", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "_hoisted_4", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "recentApps", "length", "_hoisted_7", "_Fragment", "_renderList", "slice", "app", "key", "_hoisted_8", "name", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "d", "fill", "stroke", "getCompactSecurityArcPath", "securityScore", "getSecurityColor", "_hoisted_15", "_hoisted_16", "getSecurityLevelText", "_hoisted_17", "securityItems", "item", "status", "_createBlock", "_resolveDynamicComponent", "_hoisted_18"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <!-- 授权服务卡片 -->\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" class=\"right-card services-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"services-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item-compact\">\n                      <div class=\"service-dot\"></div>\n                      <span class=\"service-name\">{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-services\">\n                    <n-icon size=\"24\" color=\"#d0d0d0\">\n                      <apps-outline />\n                    </n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card title=\"安全指标\" :bordered=\"false\" class=\"right-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"security-dashboard-compact\">\n                  <!-- 紧凑型安全评分仪表盘 -->\n                  <div class=\"security-gauge-compact\">\n                    <svg class=\"gauge-svg-compact\" viewBox=\"0 0 120 80\" width=\"120\" height=\"80\">\n                      <!-- 背景弧线 -->\n                      <path\n                        d=\"M 15 65 A 45 45 0 0 1 105 65\"\n                        fill=\"none\"\n                        :stroke=\"isDarkMode ? '#3a3a3a' : '#e0e0e0'\"\n                        stroke-width=\"6\"\n                        stroke-linecap=\"round\"\n                      />\n                      <!-- 进度弧线 -->\n                      <path\n                        :d=\"getCompactSecurityArcPath(securityScore)\"\n                        fill=\"none\"\n                        :stroke=\"getSecurityColor(securityScore)\"\n                        stroke-width=\"6\"\n                        stroke-linecap=\"round\"\n                        class=\"security-arc\"\n                      />\n                      <!-- 中心文字 -->\n                      <text x=\"60\" y=\"55\" text-anchor=\"middle\" class=\"gauge-score-compact\">\n                        {{ securityScore }}%\n                      </text>\n                      <text x=\"60\" y=\"70\" text-anchor=\"middle\" class=\"gauge-label-compact\">\n                        {{ getSecurityLevelText(securityScore) }}\n                      </text>\n                    </svg>\n                  </div>\n\n                  <!-- 紧凑型安全项目列表 -->\n                  <div class=\"security-items-compact\">\n                    <div class=\"security-item-compact\" v-for=\"item in securityItems\" :key=\"item.key\">\n                      <n-icon\n                        size=\"12\"\n                        :color=\"item.status ? '#18a058' : '#d03050'\"\n                      >\n                        <component :is=\"item.status ? 'checkmark-circle-outline' : 'close-circle-outline'\" />\n                      </n-icon>\n                      <span class=\"item-name-compact\">{{ item.name }}</span>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 水平并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  gap: 16px;\n  height: 100%;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩 */\n}\n\n.services-content {\n  min-height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 0;\n}\n\n.service-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 13px;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: var(--n-text-color-3);\n  font-size: 12px;\n}\n\n.more-services {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 紧凑型安全指标卡片样式 */\n.security-card {\n  min-width: 0; /* 允许内容收缩 */\n}\n\n.security-dashboard-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  min-height: 120px;\n}\n\n.security-gauge-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 6px;\n}\n\n.gauge-svg-compact {\n  width: 100%;\n  height: auto;\n  max-width: 120px;\n}\n\n.gauge-score-compact {\n  font-size: 18px;\n  font-weight: 600;\n  fill: var(--n-text-color-1);\n}\n\n.gauge-label-compact {\n  font-size: 10px;\n  fill: var(--n-text-color-2);\n}\n\n.security-level-compact {\n  margin-top: 2px;\n}\n\n.security-items-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.security-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 2px 0;\n}\n\n.item-name-compact {\n  font-size: 11px;\n  color: var(--n-text-color-2);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards-horizontal {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .compact-card {\n    flex: none;\n  }\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAwCVA,KAAK,EAAC;AAAY;;EAaxBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAkB;;;EACqBA,KAAK,EAAC;;;EAG5CA,KAAK,EAAC;AAAc;;;EAGlBA,KAAK,EAAC;;;EAWfA,KAAK,EAAC;AAA4B;;EAEhCA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC,mBAAmB;EAACC,OAAO,EAAC,YAAY;EAACC,KAAK,EAAC,KAAK;EAACC,MAAM,EAAC;;;;;EAmB/DC,CAAC,EAAC,IAAI;EAACC,CAAC,EAAC,IAAI;EAAC,aAAW,EAAC,QAAQ;EAACL,KAAK,EAAC;;;EAGzCI,CAAC,EAAC,IAAI;EAACC,CAAC,EAAC,IAAI;EAAC,aAAW,EAAC,QAAQ;EAACL,KAAK,EAAC;;;EAO9CA,KAAK,EAAC;AAAwB;;EAQzBA,KAAK,EAAC;AAAmB;;uBArHnDM,mBAAA,CA+HM,OA/HNC,UA+HM,GA9HJC,YAAA,CA6HSC,MAAA;IA7HAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA2HM,CA3HNC,mBAAA,CA2HM,OA3HNC,UA2HM,GA1HJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAErB,KAAK,EAAC;;wBAAa,MAIpEsB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAiHSC,MAAA;MAjHD,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAyCO,CAzCPhB,YAAA,CAyCOC,MAAA;QAzCAgB,IAAI,EAAE;MAAC;0BACZ,MAuCS,CAvCTjB,YAAA,CAuCSC,MAAA;UAvCAY,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACrF,MAkCiB,CAlCjBnB,YAAA,CAkCiBC,MAAA;YAjCf,iBAAe,EAAC,KAAK;YACpBmB,MAAM,EAAE;;8BAET,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAI;gCAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;gBAEvBtB,YAAA,CAQsBC,MAAA;cARDoB,KAAK,EAAC;YAAM;gCAC/B,MAMQ,CANRrB,YAAA,CAMQC,MAAA;gBALLY,QAAQ,EAAE,KAAK;gBACfD,IAAI,EAAEX,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe;gBACtCC,IAAI,EAAC;;kCAEL,MAAqD,C,kCAAlDvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe,iC;;;;gBAGtCvB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,SAAS,kB;;gBAE7C1B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAQ;gCACjC,MAA+E,C,kCAA5EpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,UAAU,IAAI1B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmB,WAAW,kB;;gBAE7E5B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAS;gCAClC,MAAyC,C,kCAAtCpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,WAAW,yB;;gBAEhC7B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC9B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;gBAAvDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;gBAEzDd,YAAA,CAKqBC,MAAA;cALAoB,KAAK,EAAC,MAAM;cAAEJ,IAAI,EAAE;;gCACxC,MAGE,CAHFb,mBAAA,CAGE,OAHF0B,UAGE,GAFA1B,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEsB,KAAK,kBAC7B/B,YAAA,CAAwDC,MAAA;gBAA9C+B,IAAI,EAAJ,EAAI;gBAACpB,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;cAInDd,YAAA,CAEUC,MAAA;YAFAW,IAAI,EAAC,SAAS;YAACqB,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YAAetB,KAAyB,EAAzB;cAAA;YAAA;;8BAA0B,MAE7FF,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;;;UAIJd,YAAA,CAoEOC,MAAA;QApEAgB,IAAI,EAAE;MAAC;0BACZ,MAkEM,CAlENb,mBAAA,CAkEM,OAlENmC,UAkEM,GAjEJC,mBAAA,YAAe,EACfxC,YAAA,CAeSC,MAAA;UAfDU,KAAK,EAAC,cAAc;UAAEE,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,0BAA0B;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACnH,MAaM,CAbNf,mBAAA,CAaM,OAbNqC,UAaM,GAZOxC,MAAA,CAAAyC,UAAU,IAAIzC,MAAA,CAAAyC,UAAU,CAACC,MAAM,Q,cAA1C7C,mBAAA,CAKM,OALN8C,UAKM,I,kBAJJ9C,mBAAA,CAGM+C,SAAA,QAAAC,WAAA,CAHa7C,MAAA,CAAAyC,UAAU,CAACK,KAAK,QAAvBC,GAAG;iCAAflD,mBAAA,CAGM;cAHsCmD,GAAG,EAAED,GAAG,CAAC1B,EAAE;cAAE9B,KAAK,EAAC;0CAC7DY,mBAAA,CAA+B;cAA1BZ,KAAK,EAAC;YAAa,4BACxBY,mBAAA,CAAgD,QAAhD8C,UAAgD,EAAA3C,gBAAA,CAAlByC,GAAG,CAACG,IAAI,iB;6DAG1CrD,mBAAA,CAKM,OALNsD,UAKM,GAJJpD,YAAA,CAESC,MAAA;YAFDuB,IAAI,EAAC,IAAI;YAACN,KAAK,EAAC;;8BACtB,MAAgB,CAAhBlB,YAAA,CAAgBC,MAAA,iB;;wCAElBG,mBAAA,CAAiB,cAAX,MAAI,oB;;gDAKhBoC,mBAAA,cAAiB,EACjBxC,YAAA,CA6CSC,MAAA;UA7CDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,0BAA0B;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC3G,MA2CM,CA3CNf,mBAAA,CA2CM,OA3CNiD,WA2CM,GA1CJb,mBAAA,gBAAmB,EACnBpC,mBAAA,CA2BM,OA3BNkD,WA2BM,I,cA1BJxD,mBAAA,CAyBM,OAzBNyD,WAyBM,GAxBJf,mBAAA,UAAa,EACbpC,mBAAA,CAME;YALAoD,CAAC,EAAC,8BAA8B;YAChCC,IAAI,EAAC,MAAM;YACVC,MAAM,EAAEzD,MAAA,CAAAkB,UAAU;YACnB,cAAY,EAAC,GAAG;YAChB,gBAAc,EAAC;gDAEjBqB,mBAAA,UAAa,EACbpC,mBAAA,CAOE;YANCoD,CAAC,EAAEvD,MAAA,CAAA0D,yBAAyB,CAAC1D,MAAA,CAAA2D,aAAa;YAC3CH,IAAI,EAAC,MAAM;YACVC,MAAM,EAAEzD,MAAA,CAAA4D,gBAAgB,CAAC5D,MAAA,CAAA2D,aAAa;YACvC,cAAY,EAAC,GAAG;YAChB,gBAAc,EAAC,OAAO;YACtBpE,KAAK,EAAC;gDAERgD,mBAAA,UAAa,EACbpC,mBAAA,CAEO,QAFP0D,WAEO,EAAAvD,gBAAA,CADFN,MAAA,CAAA2D,aAAa,IAAG,IACrB,iBACAxD,mBAAA,CAEO,QAFP2D,WAEO,EAAAxD,gBAAA,CADFN,MAAA,CAAA+D,oBAAoB,CAAC/D,MAAA,CAAA2D,aAAa,kB,MAK3CpB,mBAAA,eAAkB,EAClBpC,mBAAA,CAUM,OAVN6D,WAUM,I,kBATJnE,mBAAA,CAQM+C,SAAA,QAAAC,WAAA,CAR4C7C,MAAA,CAAAiE,aAAa,EAArBC,IAAI;iCAA9CrE,mBAAA,CAQM;cARDN,KAAK,EAAC,uBAAuB;cAAgCyD,GAAG,EAAEkB,IAAI,CAAClB;gBAC1EjD,YAAA,CAKSC,MAAA;cAJPuB,IAAI,EAAC,IAAI;cACRN,KAAK,EAAEiD,IAAI,CAACC,MAAM;;gCAEnB,MAAqF,E,cAArFC,YAAA,CAAqFC,wBAAA,CAArEH,IAAI,CAACC,MAAM,0D;;4DAE7BhE,mBAAA,CAAsD,QAAtDmE,WAAsD,EAAAhE,gBAAA,CAAnB4D,IAAI,CAAChB,IAAI,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}