<template>
  <div class="dashboard-container">
    <n-spin :show="loading">
      <div class="dashboard-content">
        <h2 class="welcome-title">你好, {{ userStore.user?.username }}</h2>

        <n-alert title="通知" type="info" :bordered="true" class="info-alert">
          KongKuang ID 现已开放 OAuth 应用注册, 在"顶部菜单栏-更多"启用开发者选项(需要已完成实名认证).
          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.
          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.
        </n-alert>

        <div class="dashboard-main-layout">
          <!-- 左侧用户信息卡片 -->
          <div class="user-info-section" ref="userInfoSection">
            <n-card :bordered="false" class="user-info-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
              <n-descriptions
                label-placement="top"
                :column="2"
              >
                <n-descriptions-item label="ID">
                  {{ userStore.user?.id }}
                </n-descriptions-item>
                <n-descriptions-item label="实名状态">
                  <n-tag
                    :bordered="false"
                    :type="userStore.user?.level2_verified ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="注册时间">
                  {{ formatDateTime(userStore.user?.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录时间">
                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录 IP">
                  {{ userStore.user?.lastLoginIp || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="用户状态">
                   <n-tag :bordered="false" type="success" size="small">正常</n-tag>
                </n-descriptions-item>
                 <n-descriptions-item label="绑定邮箱" :span="2">
                  <div class="email-item">
                    <span>{{ userStore.user?.email }}</span>
                     <n-button text type="primary" size="small">换绑</n-button>
                  </div>
                </n-descriptions-item>
              </n-descriptions>
               <n-button type="primary" ghost @click="$router.push('/security')" style="margin-top: 16px;">
                  更改密码
              </n-button>
            </n-card>
          </div>

          <!-- 右侧两个卡片 -->
          <div class="right-cards-section" ref="rightCardsSection">
            <!-- 服务卡片 -->
            <div class="service-card-wrapper">
              <n-card title="可使用空旷账户登录的服务" :bordered="false" class="service-card-new" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <div class="service-content-new">
                  <div v-if="recentApps && recentApps.length > 0" class="service-list-new">
                    <div v-for="app in recentApps.slice(0, 3)" :key="app.id" class="service-item-new">
                      <div class="service-dot-new"></div>
                      <span class="service-name-new">{{ app.name }}</span>
                    </div>
                  </div>
                  <div v-else class="no-services-new">
                    <n-icon size="24" color="#d0d0d0">
                      <apps-outline />
                    </n-icon>
                    <span>暂无服务</span>
                  </div>
                </div>
              </n-card>
            </div>

            <!-- 安全指数卡片 -->
            <div class="security-card-wrapper">
              <n-card :bordered="false" class="security-card-new" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <template #header>
                  <div class="security-header-new">
                    <span>账户安全指数</span>
                    <n-button
                      text
                      type="primary"
                      size="small"
                      @click="$router.push('/security')"
                      class="security-action-btn-new"
                    >
                      <template #icon>
                        <n-icon>
                          <add-outline />
                        </n-icon>
                      </template>
                    </n-button>
                  </div>
                </template>

                <div class="security-content-new">
                  <!-- 安全指数展示 -->
                  <div class="security-score-new">
                    <div class="score-number-new" :style="{ color: getSecurityColor(securityScore) }">
                      {{ securityScore }}
                    </div>
                    <div class="score-label-new">
                      <n-tag
                        :type="getSecurityLevelType(securityScore)"
                        :bordered="false"
                        size="small"
                      >
                        {{ getSecurityLevelText(securityScore) }}
                      </n-tag>
                    </div>
                  </div>

                  <!-- 引导文字 -->
                  <div class="security-guide-new">
                    <span class="guide-text-new">你还未完成中心邮箱、安全认证</span>
                    <n-button
                      text
                      type="primary"
                      size="tiny"
                      @click="$router.push('/security')"
                    >
                      立即完善→
                    </n-button>
                  </div>
                </div>
              </n-card>
            </div>
          </div>
        </div>
      </div>
    </n-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import {
  NSpin,
  NGrid,
  NGi,
  NCard,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NTag,
  NList,
  NListItem,
  NIcon,
  NEmpty,
  useMessage
} from 'naive-ui';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';
import {
  CheckmarkCircleOutline,
  CloseCircleOutline,
  AppsOutline,
  AddOutline
} from '@vicons/ionicons5';

const loading = ref(false);
const userStore = useUserStore();
const message = useMessage();

const recentApps = ref([]);

// 用于高度同步的refs
const userInfoSection = ref(null);
const rightCardsSection = ref(null);



// 安全相关数据
const securityScore = ref(75); // 安全评分 0-100

const securityItems = computed(() => [
  {
    key: 'email_verified',
    name: '邮箱验证',
    status: userStore.user?.is_email_verified || false,
    actionText: '去验证'
  },
  {
    key: 'phone_verified',
    name: '手机验证',
    status: userStore.user?.is_phone_verified || false,
    actionText: '去验证'
  },
  {
    key: 'level2_verified',
    name: '实名认证',
    status: userStore.user?.level2_verified || false,
    actionText: '去认证'
  },
  {
    key: 'mfa_enabled',
    name: '双因子认证',
    status: userStore.user?.security_mfa_enabled || false,
    actionText: '去开启'
  }
]);

// 检查是否为深色模式
const isDarkMode = computed(() => {
  const themeMode = localStorage.getItem('theme') || 'system';
  if (themeMode === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return themeMode === 'dark';
});

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  // Using toLocaleString for a more standard format, customize as needed
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 安全相关方法
const getSecurityArcPath = (score) => {
  // 将分数转换为弧度 (0-100 映射到 0-π)
  const angle = (score / 100) * Math.PI;
  const x = 100 + 80 * Math.cos(Math.PI - angle);
  const y = 100 - 80 * Math.sin(Math.PI - angle);

  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;
};

const getSecurityColor = (score) => {
  if (score >= 80) return '#18a058'; // 绿色 - 安全
  if (score >= 60) return '#f0a020'; // 橙色 - 一般
  return '#d03050'; // 红色 - 危险
};

const getSecurityLevelType = (score) => {
  if (score >= 80) return 'success';
  if (score >= 60) return 'warning';
  return 'error';
};

const getSecurityLevelText = (score) => {
  if (score >= 80) return '安全';
  if (score >= 60) return '一般';
  return '危险';
};

// 紧凑型仪表盘弧线路径计算
const getCompactSecurityArcPath = (score) => {
  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸
  const angle = (score / 100) * Math.PI;
  const x = 60 + 45 * Math.cos(Math.PI - angle);
  const y = 65 - 45 * Math.sin(Math.PI - angle);

  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;
};

const handleSecurityAction = (key) => {
  switch (key) {
    case 'email_verified':
      message.info('邮箱验证功能开发中');
      break;
    case 'phone_verified':
      message.info('手机验证功能开发中');
      break;
    case 'level2_verified':
      window.location.href = '/verification';
      break;
    case 'mfa_enabled':
      window.location.href = '/security';
      break;
    default:
      message.info('功能开发中');
  }
};

// 计算安全评分
const calculateSecurityScore = () => {
  const items = securityItems.value;
  const completedItems = items.filter(item => item.status).length;
  const score = Math.round((completedItems / items.length) * 100);
  securityScore.value = score;
};

const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get('/dashboard');

    if (response.data && response.data.success) {
      // 更新用户信息，使用后端返回的格式化数据
      if (response.data.user) {
        userStore.user = {
          ...userStore.user,
          ...response.data.user,
          // 使用后端返回的格式化时间，如果没有则使用原始数据
          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,
          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,
          last_login: response.data.user.last_login,
          lastLoginIp: response.data.user.lastLoginIp,
          level2_verified: response.data.user.level2_verified
        };
      }

      // 更新应用列表
      recentApps.value = response.data.recentApps || [];

      console.log('仪表盘数据加载成功:', response.data);
    }

    // 计算安全评分
    calculateSecurityScore();
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

// 同步右侧卡片高度到左侧信息卡片高度
const syncCardHeights = () => {
  if (userInfoSection.value && rightCardsSection.value) {
    // 获取左侧信息卡片的实际高度
    const userInfoHeight = userInfoSection.value.offsetHeight;

    // 设置右侧容器的高度等于左侧信息卡片的高度
    rightCardsSection.value.style.height = `${userInfoHeight}px`;

    console.log('同步高度:', userInfoHeight);
  }
};

onMounted(() => {
  // We need user data, if not present, maybe fetch it or rely on login flow
  if (!userStore.user) {
    // This case might happen on a page refresh, you might want to fetch user data
    // For now, we assume user data is populated from login
  }
  fetchDashboardData();

  // 等待DOM渲染完成后同步高度
  setTimeout(() => {
    syncCardHeights();
  }, 200);

  // 监听窗口大小变化，重新同步高度
  window.addEventListener('resize', syncCardHeights);
});
</script>
<style scoped>
.dashboard-container {
  padding: 16px;
  min-height: 100%;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.info-alert {
  margin-bottom: 16px;
  flex-shrink: 0;
}

/* 重构后的布局样式 */
.dashboard-main-layout {
  display: flex;
  gap: 16px;
  flex: 1;
  align-items: stretch; /* 让左右两侧高度相等 */
}

.user-info-section {
  flex: 2; /* 占2/3宽度 */
}

.user-info-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content; /* 根据内容确定高度 */
}

.right-cards-section {
  flex: 1; /* 占1/3宽度 */
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* 高度将由JavaScript动态设置 */
}

.service-card-wrapper,
.security-card-wrapper {
  flex: 1; /* 两个卡片平分右侧高度 */
  display: flex;
  flex-direction: column;
}

.service-card-new,
.security-card-new {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  flex: 1;
  display: flex;
  flex-direction: column;
  /* 移除高度限制，让它们平分父容器高度 */
}

/* 服务卡片内容样式 */
.service-content-new {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 12px 0;
  min-height: 80px;
}

.service-list-new {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.service-item-new {
  display: flex;
  align-items: center;
  gap: 8px;
}

.service-dot-new {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #18a058;
  flex-shrink: 0;
}

.service-name-new {
  font-size: 14px;
  color: var(--text-color);
}

.no-services-new {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #d0d0d0;
  font-size: 14px;
}

/* 安全指数卡片样式 */
.security-header-new {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.security-action-btn-new {
  opacity: 0.7;
  transition: opacity 0.2s;
}

.security-action-btn-new:hover {
  opacity: 1;
}

.security-content-new {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 16px;
  padding: 12px 0;
  min-height: 80px;
}

.security-score-new {
  text-align: center;
}

.score-number-new {
  font-size: 48px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 8px;
}

.score-label-new {
  margin-bottom: 16px;
}

.security-guide-new {
  text-align: center;
}

.guide-text-new {
  font-size: 12px;
  color: var(--text-color-3);
  display: block;
  margin-bottom: 8px;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 垂直并排布局 */
.side-cards-horizontal {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
  flex: 1;
}

.compact-card {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

/* 授权服务卡片样式 */
.services-card {
  min-width: 0; /* 允许内容收缩，适应水平布局 */
}

.services-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1; /* 填充整个卡片高度 */
  padding: 8px 0;
}

.service-list {
  display: flex;
  flex-direction: column;
  gap: 12px; /* 增加项目间距 */
}

.service-item-compact {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 12px; /* 增加内边距 */
  background-color: var(--n-color-target);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.service-item-compact:hover {
  background-color: var(--n-color-target-hover);
}

.service-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--n-primary-color);
  flex-shrink: 0;
}

.service-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--n-text-color-1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.no-services {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: var(--n-text-color-3);
  font-size: 13px;
  padding: 20px;
}

.more-services {
  font-size: 12px;
  color: var(--n-text-color-3);
  text-align: center;
  margin-top: 8px;
  padding: 4px 8px;
  background-color: var(--n-color-target);
  border-radius: 4px;
}

.right-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.service-item {
  padding: 8px 4px !important;
  transition: color 0.3s;
}

.service-item:hover {
  color: var(--n-primary-color);
}

/* 安全指标卡片样式 */
.security-card {
  /* 简洁的安全指数显示 */
}

.security-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.security-action-btn {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.security-action-btn:hover {
  opacity: 1;
}

.security-index-display {
  display: flex;
  flex-direction: column;
  gap: 20px;
  flex: 1; /* 填充整个卡片高度 */
  padding: 16px 0;
  justify-content: center;
}

.security-score-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.score-number {
  font-size: 48px;
  font-weight: 700;
  line-height: 1;
  transition: color 0.3s ease;
}

.score-label {
  display: flex;
  justify-content: center;
}

.security-guide {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
  text-align: center;
}

.guide-text {
  font-size: 13px;
  color: var(--n-text-color-2);
  line-height: 1.4;
}

/* 通用样式 */
.security-arc {
  transition: all 0.3s ease;
}

/* 确保网格布局紧凑 */
:deep(.n-grid) {
  height: 100%;
}

:deep(.n-gi) {
  height: fit-content;
}

/* 减少卡片内部间距 */
:deep(.n-card .n-card__content) {
  padding: 16px;
}

:deep(.n-descriptions) {
  margin-bottom: 0;
}

:deep(.n-descriptions-item) {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .side-cards {
    gap: 12px;
  }

  .gauge-svg-compact {
    max-width: 100px;
  }

  .gauge-score-compact {
    font-size: 16px;
  }

  .service-name,
  .item-name-compact {
    font-size: 12px;
  }
}
</style>