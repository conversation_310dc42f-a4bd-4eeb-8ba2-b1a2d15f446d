<template>
  <div class="dashboard-container">
    <n-spin :show="loading">
      <div class="dashboard-content">
        <h2 class="welcome-title">你好, {{ userStore.user?.username }}</h2>

        <n-alert title="通知" type="info" :bordered="true" class="info-alert">
          KongKuang ID 现已开放 OAuth 应用注册, 在"顶部菜单栏-更多"启用开发者选项(需要已完成实名认证).
          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.
          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.
        </n-alert>

        <n-grid x-gap="16" y-gap="16" :cols="3" style="flex: 1;">
          <n-gi :span="2">
            <n-card :bordered="false" class="user-info-panel" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
              <template #header>
                <div class="card-header">
                  <h3>用户信息</h3>
                  <div class="user-avatar">
                    <n-avatar
                      :size="48"
                      :src="userStore.user?.avatar"
                      :fallback-src="getDefaultAvatar()"
                    >
                      {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}
                    </n-avatar>
                  </div>
                </div>
              </template>

              <n-descriptions
                label-placement="top"
                :column="2"
              >
                <n-descriptions-item label="用户ID">
                  <n-text code>{{ userStore.user?.id }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item label="用户名">
                  <n-text strong>{{ userStore.user?.username }}</n-text>
                </n-descriptions-item>
                <n-descriptions-item label="实名认证状态">
                  <div class="verification-status">
                    <div class="status-item">
                      <span class="status-label">一级认证：</span>
                      <n-tag
                        :bordered="false"
                        :type="verificationStatus.level1Completed ? 'success' : 'default'"
                        size="small"
                      >
                        {{ verificationStatus.level1Completed ? '已完成' : '未完成' }}
                      </n-tag>
                      <n-tooltip v-if="verificationStatus.level1Completed && verificationStatus.level1Info">
                        <template #trigger>
                          <n-icon size="14" color="#18a058" style="margin-left: 4px;">
                            <information-circle-outline />
                          </n-icon>
                        </template>
                        <div>
                          <p>认证时间: {{ formatDateTime(verificationStatus.level1Info.verifiedAt) }}</p>
                          <p>支付方式: {{ verificationStatus.level1Info.paymentMethod === 'alipay' ? '支付宝' : '微信' }}</p>
                          <p>过期时间: {{ formatDateTime(verificationStatus.level1Info.expiresAt) }}</p>
                        </div>
                      </n-tooltip>
                    </div>
                    <div class="status-item">
                      <span class="status-label">二级认证：</span>
                      <n-tag
                        :bordered="false"
                        :type="verificationStatus.level2Completed ? 'success' : 'default'"
                        size="small"
                      >
                        {{ verificationStatus.level2Completed ? '已完成' : '未完成' }}
                      </n-tag>
                      <n-tooltip v-if="verificationStatus.level2Completed && verificationStatus.level2Info">
                        <template #trigger>
                          <n-icon size="14" color="#18a058" style="margin-left: 4px;">
                            <information-circle-outline />
                          </n-icon>
                        </template>
                        <div>
                          <p>真实姓名: {{ verificationStatus.level2Info.realName }}</p>
                          <p>认证时间: {{ formatDateTime(verificationStatus.level2Info.verifiedAt) }}</p>
                        </div>
                      </n-tooltip>
                    </div>
                  </div>
                </n-descriptions-item>
                <n-descriptions-item label="账户状态">
                  <n-tag :bordered="false" type="success" size="small">
                    <template #icon>
                      <n-icon><checkmark-circle-outline /></n-icon>
                    </template>
                    正常
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="注册时间">
                  {{ formatDateTime(userStore.user?.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录">
                  <div class="login-info">
                    <div>{{ formatDateTime(userStore.user?.lastLoginAt) }}</div>
                    <div class="login-ip">IP: {{ userStore.user?.lastLoginIp || 'N/A' }}</div>
                  </div>
                </n-descriptions-item>
                <n-descriptions-item label="绑定邮箱" :span="2">
                  <div class="email-item">
                    <div class="email-info">
                      <span>{{ userStore.user?.email }}</span>
                      <n-tag
                        v-if="userStore.user?.is_email_verified"
                        :bordered="false"
                        type="success"
                        size="tiny"
                        style="margin-left: 8px;"
                      >
                        已验证
                      </n-tag>
                      <n-tag
                        v-else
                        :bordered="false"
                        type="warning"
                        size="tiny"
                        style="margin-left: 8px;"
                      >
                        未验证
                      </n-tag>
                    </div>
                    <n-button text type="primary" size="small">换绑</n-button>
                  </div>
                </n-descriptions-item>
                <n-descriptions-item label="绑定手机" :span="2" v-if="userStore.user?.phone">
                  <div class="phone-item">
                    <div class="phone-info">
                      <span>{{ maskPhone(userStore.user?.phone) }}</span>
                      <n-tag
                        v-if="userStore.user?.is_phone_verified"
                        :bordered="false"
                        type="success"
                        size="tiny"
                        style="margin-left: 8px;"
                      >
                        已验证
                      </n-tag>
                      <n-tag
                        v-else
                        :bordered="false"
                        type="warning"
                        size="tiny"
                        style="margin-left: 8px;"
                      >
                        未验证
                      </n-tag>
                    </div>
                    <n-button text type="primary" size="small">换绑</n-button>
                  </div>
                </n-descriptions-item>
              </n-descriptions>

              <div class="action-buttons">
                <n-button type="primary" ghost @click="$router.push('/security')">
                  <template #icon>
                    <n-icon><lock-closed-outline /></n-icon>
                  </template>
                  安全设置
                </n-button>
                <n-button type="primary" ghost @click="$router.push('/verification')">
                  <template #icon>
                    <n-icon><shield-checkmark-outline /></n-icon>
                  </template>
                  实名认证
                </n-button>
                <n-button type="primary" ghost @click="$router.push('/profile')">
                  <template #icon>
                    <n-icon><person-outline /></n-icon>
                  </template>
                  个人资料
                </n-button>
              </div>
            </n-card>
          </n-gi>

          <n-gi :span="1">
            <div class="side-cards">
              <!-- 认证状态卡片 -->
              <n-card title="认证状态" :bordered="false" class="right-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <div class="verification-summary">
                  <div class="verification-item">
                    <div class="verification-icon">
                      <n-icon
                        size="20"
                        :color="verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'"
                      >
                        <wallet-outline />
                      </n-icon>
                    </div>
                    <div class="verification-content">
                      <div class="verification-title">一级认证</div>
                      <div class="verification-desc">
                        {{ verificationStatus.level1Completed ? '识脸支付认证' : '未完成' }}
                      </div>
                      <div v-if="verificationStatus.level1Completed && verificationStatus.level1Info" class="verification-time">
                        {{ formatRelativeTime(verificationStatus.level1Info.verifiedAt) }}
                      </div>
                    </div>
                    <div class="verification-action">
                      <n-button
                        v-if="!verificationStatus.level1Completed"
                        text
                        type="primary"
                        size="small"
                        @click="$router.push('/verification')"
                      >
                        去认证
                      </n-button>
                      <n-tag
                        v-else
                        :bordered="false"
                        type="success"
                        size="small"
                      >
                        已完成
                      </n-tag>
                    </div>
                  </div>

                  <div class="verification-item">
                    <div class="verification-icon">
                      <n-icon
                        size="20"
                        :color="verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'"
                      >
                        <shield-checkmark-outline />
                      </n-icon>
                    </div>
                    <div class="verification-content">
                      <div class="verification-title">二级认证</div>
                      <div class="verification-desc">
                        {{ verificationStatus.level2Completed ? '二要素验证' : '未完成' }}
                      </div>
                      <div v-if="verificationStatus.level2Completed && verificationStatus.level2Info" class="verification-time">
                        {{ formatRelativeTime(verificationStatus.level2Info.verifiedAt) }}
                      </div>
                    </div>
                    <div class="verification-action">
                      <n-button
                        v-if="!verificationStatus.level2Completed"
                        text
                        type="primary"
                        size="small"
                        @click="$router.push('/verification')"
                      >
                        去认证
                      </n-button>
                      <n-tag
                        v-else
                        :bordered="false"
                        type="success"
                        size="small"
                      >
                        已完成
                      </n-tag>
                    </div>
                  </div>
                </div>
              </n-card>

              <!-- 快捷操作卡片 -->
              <n-card title="快捷操作" :bordered="false" class="right-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <div class="quick-actions">
                  <n-button
                    block
                    type="primary"
                    ghost
                    @click="$router.push('/applications')"
                    style="margin-bottom: 8px;"
                  >
                    <template #icon>
                      <n-icon><apps-outline /></n-icon>
                    </template>
                    我的应用
                  </n-button>
                  <n-button
                    block
                    type="primary"
                    ghost
                    @click="$router.push('/security')"
                    style="margin-bottom: 8px;"
                  >
                    <template #icon>
                      <n-icon><lock-closed-outline /></n-icon>
                    </template>
                    安全中心
                  </n-button>
                  <n-button
                    block
                    type="primary"
                    ghost
                    @click="handleLogout"
                  >
                    <template #icon>
                      <n-icon><log-out-outline /></n-icon>
                    </template>
                    退出登录
                  </n-button>
                </div>
              </n-card>

              <!-- 授权服务卡片 -->
              <n-card title="授权服务" :bordered="false" class="right-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <n-list :show-divider="false">
                  <n-list-item v-for="app in recentApps" :key="app.id" class="service-item">
                    <div class="service-info">
                      <div class="service-name">{{ app.name }}</div>
                      <div class="service-time">{{ formatRelativeTime(app.lastUsed) }}</div>
                    </div>
                  </n-list-item>
                  <n-empty v-if="!recentApps || recentApps.length === 0" description="暂无授权服务" size="small" />
                </n-list>
              </n-card>
            </div>
          </n-gi>
        </n-grid>
      </div>
    </n-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import {
  NSpin,
  NGrid,
  NGi,
  NCard,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NTag,
  NList,
  NListItem,
  NIcon,
  NEmpty,
  NAvatar,
  NText,
  NTooltip,
  useMessage,
  useDialog
} from 'naive-ui';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';
import {
  InformationCircleOutline,
  CheckmarkCircleOutline,
  LockClosedOutline,
  ShieldCheckmarkOutline,
  PersonOutline,
  WalletOutline,
  AppsOutline,
  LogOutOutline
} from '@vicons/ionicons5';

const loading = ref(false);
const userStore = useUserStore();
const message = useMessage();
const dialog = useDialog();

const recentApps = ref([]);
const verificationStatus = ref({
  level1Completed: false,
  level2Completed: false,
  level1Info: null,
  level2Info: null
});

// 检查是否为深色模式
const isDarkMode = computed(() => {
  const themeMode = localStorage.getItem('theme') || 'system';
  if (themeMode === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return themeMode === 'dark';
});

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  // Using toLocaleString for a more standard format, customize as needed
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 格式化相对时间（如：3天前，2小时前）
const formatRelativeTime = (dateString) => {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);
  const diffMonth = Math.floor(diffDay / 30);
  const diffYear = Math.floor(diffMonth / 12);

  if (diffYear > 0) {
    return `${diffYear}年前`;
  } else if (diffMonth > 0) {
    return `${diffMonth}个月前`;
  } else if (diffDay > 0) {
    return `${diffDay}天前`;
  } else if (diffHour > 0) {
    return `${diffHour}小时前`;
  } else if (diffMin > 0) {
    return `${diffMin}分钟前`;
  } else {
    return '刚刚';
  }
};

// 获取默认头像
const getDefaultAvatar = () => {
  return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';
};

// 手机号码脱敏
const maskPhone = (phone) => {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
};

// 处理退出登录
const handleLogout = () => {
  dialog.warning({
    title: '确认退出',
    content: '您确定要退出登录吗？',
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        const apiClient = getApiClient();
        await apiClient.post('/auth/logout');
        userStore.logout();
        message.success('已成功退出登录');
        window.location.href = '/login';
      } catch (error) {
        console.error('退出登录失败:', error);
        message.error('退出登录失败: ' + (error.response?.data?.message || error.message));
      }
    }
  });
};

const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const apiClient = getApiClient();

    // 并行获取仪表盘数据和认证状态
    const [dashboardResponse, verificationResponse] = await Promise.all([
      apiClient.get('/dashboard'),
      apiClient.get('/users/verification-status')
    ]);

    // 处理仪表盘数据
    if (dashboardResponse.data && dashboardResponse.data.success) {
      // 更新用户信息，使用后端返回的格式化数据
      if (dashboardResponse.data.user) {
        userStore.user = {
          ...userStore.user,
          ...dashboardResponse.data.user,
          // 使用后端返回的格式化时间，如果没有则使用原始数据
          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,
          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,
          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp
        };
      }

      // 更新应用列表
      recentApps.value = dashboardResponse.data.recentApps || [];
    }

    // 处理认证状态数据
    if (verificationResponse.data && verificationResponse.data.success) {
      verificationStatus.value = {
        level1Completed: verificationResponse.data.level1Completed || false,
        level2Completed: verificationResponse.data.level2Completed || false,
        level1Info: verificationResponse.data.level1Info || null,
        level2Info: verificationResponse.data.level2Info || null
      };
    }

    console.log('仪表盘数据加载成功:', {
      dashboard: dashboardResponse.data,
      verification: verificationResponse.data
    });
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

    onMounted(() => {
  // We need user data, if not present, maybe fetch it or rely on login flow
  if (!userStore.user) {
    // This case might happen on a page refresh, you might want to fetch user data
    // For now, we assume user data is populated from login
  }
  fetchDashboardData();
});
</script>
<style scoped>
.dashboard-container {
  padding: 16px;
  min-height: 100%;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.info-alert {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.user-info-panel {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-avatar {
  flex-shrink: 0;
}

.verification-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-weight: 500;
  min-width: 70px;
}

.login-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.login-ip {
  font-size: 12px;
  color: var(--n-text-color-3);
}

.email-item,
.phone-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.email-info,
.phone-info {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  flex-wrap: wrap;
}

.side-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.right-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.verification-summary {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.verification-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background-color: var(--n-color-target);
  border-radius: 6px;
  transition: all 0.3s ease;
}

.verification-item:hover {
  background-color: var(--n-color-target-hover);
}

.verification-icon {
  flex-shrink: 0;
}

.verification-content {
  flex: 1;
  min-width: 0;
}

.verification-title {
  font-weight: 600;
  font-size: 14px;
  margin-bottom: 2px;
}

.verification-desc {
  font-size: 12px;
  color: var(--n-text-color-2);
  margin-bottom: 2px;
}

.verification-time {
  font-size: 11px;
  color: var(--n-text-color-3);
}

.verification-action {
  flex-shrink: 0;
}

.quick-actions {
  display: flex;
  flex-direction: column;
}

.service-item {
  padding: 8px 4px !important;
  transition: color 0.3s;
}

.service-item:hover {
  color: var(--n-primary-color);
}

.service-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  width: 100%;
}

.service-name {
  font-weight: 500;
  font-size: 14px;
}

.service-time {
  font-size: 12px;
  color: var(--n-text-color-3);
}

/* 确保网格布局紧凑 */
:deep(.n-grid) {
  height: 100%;
}

:deep(.n-gi) {
  height: fit-content;
}

/* 减少卡片内部间距 */
:deep(.n-card .n-card__content) {
  padding: 16px;
}

:deep(.n-descriptions) {
  margin-bottom: 0;
}

:deep(.n-descriptions-item) {
  margin-bottom: 8px;
}
</style>