<template>
  <div class="dashboard-container">
    <n-spin :show="loading">
      <div class="dashboard-content">
        <h2 class="welcome-title">你好, {{ userStore.user?.username }}</h2>

        <n-alert title="通知" type="info" :bordered="true" class="info-alert">
          KongKuang ID 现已开放 OAuth 应用注册, 在"顶部菜单栏-更多"启用开发者选项(需要已完成实名认证).
          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.
          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.
        </n-alert>

        <n-grid x-gap="16" y-gap="16" :cols="3" style="flex: 1;">
          <n-gi :span="2">
            <n-card :bordered="false" class="user-info-panel" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
              <n-descriptions
                label-placement="top"
                :column="2"
              >
                <n-descriptions-item label="ID">
                  {{ userStore.user?.id }}
                </n-descriptions-item>
                <n-descriptions-item label="实名状态">
                  <n-tag
                    :bordered="false"
                    :type="userStore.user?.level2_verified ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="注册时间">
                  {{ formatDateTime(userStore.user?.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录时间">
                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录 IP">
                  {{ userStore.user?.lastLoginIp || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="用户状态">
                   <n-tag :bordered="false" type="success" size="small">正常</n-tag>
                </n-descriptions-item>
                 <n-descriptions-item label="绑定邮箱" :span="2">
                  <div class="email-item">
                    <span>{{ userStore.user?.email }}</span>
                     <n-button text type="primary" size="small">换绑</n-button>
              </div>
                </n-descriptions-item>
              </n-descriptions>
               <n-button type="primary" ghost @click="$router.push('/security')" style="margin-top: 16px;">
                  更改密码
              </n-button>
            </n-card>
          </n-gi>

          <n-gi :span="1">
            <div class="side-cards">
              <n-card title="可使用 KongKuang ID 登录的服务" :bordered="false" class="right-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                  <n-list :show-divider="false">
                    <n-list-item v-for="app in recentApps" :key="app.id" class="service-item">
                       {{ app.name }}
                    </n-list-item>
                     <n-empty v-if="!recentApps || recentApps.length === 0" description="暂无服务" />
                  </n-list>
              </n-card>

              <!-- 账户安全指标卡片 -->
              <n-card title="账户安全指标" :bordered="false" class="right-card security-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                <div class="security-dashboard">
                  <!-- 方向盘样式的安全指标 -->
                  <div class="steering-wheel">
                    <svg viewBox="0 0 200 200" class="wheel-svg">
                      <!-- 外圈 -->
                      <circle cx="100" cy="100" r="90" fill="none" :stroke="isDarkMode ? '#404040' : '#e0e0e0'" stroke-width="4"/>

                      <!-- 安全等级弧线 -->
                      <circle
                        cx="100"
                        cy="100"
                        r="90"
                        fill="none"
                        :stroke="getSecurityColor()"
                        stroke-width="8"
                        stroke-linecap="round"
                        :stroke-dasharray="getSecurityDashArray()"
                        :stroke-dashoffset="getSecurityDashOffset()"
                        transform="rotate(-90 100 100)"
                        class="security-arc"
                      />

                      <!-- 方向盘辐条 -->
                      <g :stroke="isDarkMode ? '#606060' : '#c0c0c0'" stroke-width="3">
                        <line x1="100" y1="30" x2="100" y2="70" />
                        <line x1="100" y1="130" x2="100" y2="170" />
                        <line x1="30" y1="100" x2="70" y2="100" />
                        <line x1="130" y1="100" x2="170" y2="100" />
                      </g>

                      <!-- 中心圆 -->
                      <circle cx="100" cy="100" r="25" :fill="getSecurityColor()" opacity="0.2"/>
                      <circle cx="100" cy="100" r="25" fill="none" :stroke="getSecurityColor()" stroke-width="2"/>

                      <!-- 安全等级文字 -->
                      <text x="100" y="95" text-anchor="middle" :fill="getSecurityColor()" font-size="12" font-weight="bold">
                        {{ getSecurityLevel() }}
                      </text>
                      <text x="100" y="110" text-anchor="middle" :fill="getSecurityColor()" font-size="10">
                        {{ getSecurityScore() }}%
                      </text>
                    </svg>
                  </div>

                  <!-- 安全项目列表 -->
                  <div class="security-items">
                    <div class="security-item" v-for="item in securityItems" :key="item.key">
                      <div class="item-icon">
                        <n-icon :size="16" :color="item.status ? '#18a058' : '#d03050'">
                          <component :is="item.icon" />
                        </n-icon>
                      </div>
                      <div class="item-content">
                        <div class="item-name">{{ item.name }}</div>
                        <div class="item-status" :class="{ 'status-ok': item.status, 'status-warning': !item.status }">
                          {{ item.status ? '已启用' : '未启用' }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 安全建议 -->
                  <div class="security-suggestion" v-if="getSecurityScore() < 100">
                    <n-alert type="warning" :bordered="false" size="small">
                      <template #icon>
                        <n-icon><shield-outline /></n-icon>
                      </template>
                      建议完善{{ getSecuritySuggestions().join('、') }}以提高账户安全性
                    </n-alert>
                  </div>
                </div>
              </n-card>
            </div>
          </n-gi>
        </n-grid>
      </div>
    </n-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import {
  NSpin,
  NGrid,
  NGi,
  NCard,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NTag,
  NList,
  NListItem,
  NIcon,
  NEmpty,
  useMessage
} from 'naive-ui';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';
import {
  ShieldOutline,
  LockClosedOutline,
  MailOutline,
  PhonePortraitOutline,
  KeyOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline
} from '@vicons/ionicons5';

const loading = ref(false);
const userStore = useUserStore();
const message = useMessage();

const recentApps = ref([]);

// 安全指标数据
const securityItems = computed(() => [
  {
    key: 'password',
    name: '登录密码',
    status: true, // 用户已设置密码
    icon: KeyOutline
  },
  {
    key: 'email',
    name: '邮箱验证',
    status: userStore.user?.is_email_verified || false,
    icon: MailOutline
  },
  {
    key: 'phone',
    name: '手机验证',
    status: userStore.user?.is_phone_verified || false,
    icon: PhonePortraitOutline
  },
  {
    key: 'realname',
    name: '实名认证',
    status: userStore.user?.level2_verified || false,
    icon: ShieldOutline
  },
  {
    key: 'mfa',
    name: '双因子认证',
    status: userStore.user?.security_mfa_enabled || false,
    icon: LockClosedOutline
  }
]);

// 检查是否为深色模式
const isDarkMode = computed(() => {
  const themeMode = localStorage.getItem('theme') || 'system';
  if (themeMode === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return themeMode === 'dark';
});

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  // Using toLocaleString for a more standard format, customize as needed
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

// 计算安全分数
const getSecurityScore = () => {
  const totalItems = securityItems.value.length;
  const completedItems = securityItems.value.filter(item => item.status).length;
  return Math.round((completedItems / totalItems) * 100);
};

// 获取安全等级
const getSecurityLevel = () => {
  const score = getSecurityScore();
  if (score >= 90) return '优秀';
  if (score >= 70) return '良好';
  if (score >= 50) return '一般';
  return '较低';
};

// 获取安全等级颜色
const getSecurityColor = () => {
  const score = getSecurityScore();
  if (score >= 90) return '#18a058'; // 绿色
  if (score >= 70) return '#2080f0'; // 蓝色
  if (score >= 50) return '#f0a020'; // 橙色
  return '#d03050'; // 红色
};

// 计算SVG弧线参数
const getSecurityDashArray = () => {
  const circumference = 2 * Math.PI * 90; // 半径90的圆周长
  return `${circumference} ${circumference}`;
};

const getSecurityDashOffset = () => {
  const circumference = 2 * Math.PI * 90;
  const score = getSecurityScore();
  return circumference - (score / 100) * circumference;
};

// 获取安全建议
const getSecuritySuggestions = () => {
  return securityItems.value
    .filter(item => !item.status)
    .map(item => item.name);
};

const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get('/dashboard');

    if (response.data && response.data.success) {
      // 更新用户信息，使用后端返回的格式化数据
      if (response.data.user) {
        userStore.user = {
          ...userStore.user,
          ...response.data.user,
          // 使用后端返回的格式化时间，如果没有则使用原始数据
          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,
          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,
          last_login: response.data.user.last_login,
          lastLoginIp: response.data.user.lastLoginIp,
          level2_verified: response.data.user.level2_verified
        };
      }

      // 更新应用列表
      recentApps.value = response.data.recentApps || [];

      console.log('仪表盘数据加载成功:', response.data);
    }
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

    onMounted(() => {
  // We need user data, if not present, maybe fetch it or rely on login flow
  if (!userStore.user) {
    // This case might happen on a page refresh, you might want to fetch user data
    // For now, we assume user data is populated from login
  }
  fetchDashboardData();
});
</script>
<style scoped>
.dashboard-container {
  padding: 16px;
  min-height: 100%;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.info-alert {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.user-info-panel {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.side-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.right-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.service-item {
  padding: 8px 4px !important;
  transition: color 0.3s;
}

.service-item:hover {
  color: var(--n-primary-color);
}

/* 安全指标卡片样式 */
.security-card {
  margin-top: 16px;
}

.security-dashboard {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.steering-wheel {
  width: 120px;
  height: 120px;
  position: relative;
}

.wheel-svg {
  width: 100%;
  height: 100%;
  transform: rotate(0deg);
}

.security-arc {
  transition: stroke-dashoffset 0.6s ease-in-out;
}

.security-items {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  background-color: var(--n-color-target);
  transition: all 0.3s ease;
}

.security-item:hover {
  background-color: var(--n-color-target-hover);
}

.item-icon {
  flex-shrink: 0;
}

.item-content {
  flex: 1;
  min-width: 0;
}

.item-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--n-text-color-1);
}

.item-status {
  font-size: 11px;
  margin-top: 2px;
}

.status-ok {
  color: var(--n-success-color);
}

.status-warning {
  color: var(--n-warning-color);
}

.security-suggestion {
  width: 100%;
  margin-top: 8px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .steering-wheel {
    width: 100px;
    height: 100px;
  }

  .security-item {
    padding: 4px 6px;
  }

  .item-name {
    font-size: 11px;
  }

  .item-status {
    font-size: 10px;
  }
}

/* 确保网格布局紧凑 */
:deep(.n-grid) {
  height: 100%;
}

:deep(.n-gi) {
  height: fit-content;
}

/* 减少卡片内部间距 */
:deep(.n-card .n-card__content) {
  padding: 16px;
}

:deep(.n-descriptions) {
  margin-bottom: 0;
}

:deep(.n-descriptions-item) {
  margin-bottom: 8px;
}
</style>