<template>
  <div class="dashboard-container">
    <n-spin :show="loading">
      <div class="dashboard-content">
        <h2 class="welcome-title">你好, {{ userStore.user?.username }}</h2>

        <n-alert title="通知" type="info" :bordered="true" class="info-alert">
          KongKuang ID 现已开放 OAuth 应用注册, 在"顶部菜单栏-更多"启用开发者选项(需要已完成实名认证).
          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.
          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.
        </n-alert>

        <n-grid x-gap="16" y-gap="16" :cols="3" style="flex: 1;">
          <n-gi :span="2">
            <n-card :bordered="false" class="user-info-panel" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
              <n-descriptions
                label-placement="top"
                :column="2"
              >
                <n-descriptions-item label="ID">
                  {{ userStore.user?.id }}
                </n-descriptions-item>
                <n-descriptions-item label="实名状态">
                  <n-tag :bordered="false" type="success" size="small">已实名</n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="注册时间">
                  {{ formatDateTime(userStore.user?.createdAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录时间">
                  {{ formatDateTime(userStore.user?.lastLoginAt) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后登录 IP">
                  {{ userStore.user?.lastLoginIp }}
                </n-descriptions-item>
                <n-descriptions-item label="用户状态">
                   <n-tag :bordered="false" type="success" size="small">正常</n-tag>
                </n-descriptions-item>
                 <n-descriptions-item label="绑定邮箱" :span="2">
                  <div class="email-item">
                    <span>{{ userStore.user?.email }}</span>
                     <n-button text type="primary" size="small">换绑</n-button>
              </div>
                </n-descriptions-item>
              </n-descriptions>
               <n-button type="primary" ghost @click="$router.push('/security')" style="margin-top: 16px;">
                  更改密码
              </n-button>
            </n-card>
          </n-gi>

          <n-gi :span="1">
            <div class="side-cards">
              <n-card title="可使用 KongKuang ID 登录的服务" :bordered="false" class="right-card" :theme-overrides="{ color: isDarkMode ? '#2a2a30' : '#ffffff' }">
                  <n-list :show-divider="false">
                    <n-list-item v-for="app in recentApps" :key="app.id" class="service-item">
                       {{ app.name }}
                    </n-list-item>
                     <n-empty v-if="!recentApps || recentApps.length === 0" description="暂无服务" />
                  </n-list>
        </n-card>
            </div>
          </n-gi>
        </n-grid>
      </div>
    </n-spin>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import {
  NSpin,
  NGrid,
  NGi,
  NCard,
  NAlert,
  NDescriptions,
  NDescriptionsItem,
  NButton,
  NTag,
  NList,
  NListItem,
  NIcon,
  NEmpty,
  useMessage
} from 'naive-ui';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';
import { Add as AddIcon, TrashOutline as TrashIcon } from '@vicons/ionicons5';

const loading = ref(false);
const userStore = useUserStore();
const message = useMessage();

const recentApps = ref([]);

// 检查是否为深色模式
const isDarkMode = computed(() => {
  const themeMode = localStorage.getItem('theme') || 'system';
  if (themeMode === 'system') {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }
  return themeMode === 'dark';
});

const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  // Using toLocaleString for a more standard format, customize as needed
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-');
};

const fetchDashboardData = async () => {
  loading.value = true;
  try {
    const apiClient = getApiClient();
    const response = await apiClient.get('/dashboard');

    if (response.data && response.data.success) {
      // 更新用户信息，使用后端返回的格式化数据
      if (response.data.user) {
        userStore.user = {
          ...userStore.user,
          ...response.data.user,
          // 使用后端返回的格式化时间，如果没有则使用原始数据
          createdAt: response.data.user.registrationTime?.formatted || userStore.user?.createdAt,
          lastLoginAt: response.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,
          lastLoginIp: response.data.user.lastLoginIp || userStore.user?.lastLoginIp
        };
      }

      // 更新应用列表
      recentApps.value = response.data.recentApps || [];

      console.log('仪表盘数据加载成功:', response.data);
    }
  } catch (error) {
    console.error("Failed to fetch dashboard data:", error);
    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));
  } finally {
    loading.value = false;
  }
};

    onMounted(() => {
  // We need user data, if not present, maybe fetch it or rely on login flow
  if (!userStore.user) {
    // This case might happen on a page refresh, you might want to fetch user data
    // For now, we assume user data is populated from login
  }
  fetchDashboardData();
});
</script>
<style scoped>
.dashboard-container {
  padding: 16px;
  min-height: 100%;
}

.dashboard-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.welcome-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--n-text-color-1);
}

.info-alert {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.user-info-panel {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.email-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.side-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.right-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.service-item {
  padding: 8px 4px !important;
  transition: color 0.3s;
}

.service-item:hover {
  color: var(--n-primary-color);
}

/* 确保网格布局紧凑 */
:deep(.n-grid) {
  height: 100%;
}

:deep(.n-gi) {
  height: fit-content;
}

/* 减少卡片内部间距 */
:deep(.n-card .n-card__content) {
  padding: 16px;
}

:deep(.n-descriptions) {
  margin-bottom: 0;
}

:deep(.n-descriptions-item) {
  margin-bottom: 8px;
}
</style>