# 阿里云API配置文档

## 📋 API信息

**服务名称**: 身份证实名认证-身份证二要素-身份证一致性验证-身份验证【最新版】
**API地址**: https://market.aliyun.com/apimarket/detail/cmapi022049

### 🔑 API凭证

```toml
# config.toml 中的配置
[aliyun_api]
app_key = "*********"
app_secret = "l77vwNhbVtax65GRQz9NnnOxxREz5AZS"
app_code = "ac2c8231f12445928b757dd27e67dbce"
id_verification_url = "https://idcert.market.alicloudapi.com/idcard"
timeout = 15000
use_real_api = true
```

## 🔧 API调用方式

### 请求格式
```http
GET https://idcert.market.alicloudapi.com/idcard?idCard={身份证号}&name={姓名}
Authorization: APPCODE ac2c8231f12445928b757dd27e67dbce
```

### 请求参数
- `idCard`: 身份证号码（18位）
- `name`: 真实姓名

### 响应格式
```json
{
  "status": "200",                    // 状态码
  "msg": "验证成功",                  // 消息
  "idCard": "110101****1234",         // 脱敏身份证号
  "name": "张*",                      // 脱敏姓名
  "sex": "男",                        // 性别
  "area": "北京市",                   // 地区
  "province": "北京市",               // 省份
  "city": "北京市",                   // 城市
  "prefecture": "东城区",             // 区县
  "birthday": "1990-01-01",           // 生日
  "addrCode": "110101",               // 地址码
  "lastCode": "1234",                 // 校验码
  "traceId": "20250717141429_65b2ic_c3398281"  // 追踪ID
}
```

### 状态码说明
- `200`: 验证成功（姓名与身份证号匹配）
- `201`: 验证失败（姓名与身份证号不匹配）
- `205`: 身份证格式不正确
- 其他: 其他错误

## 🏗️ 系统集成

### 配置管理
1. **配置文件**: `config.toml` - 主要配置存储
2. **环境变量**: 可覆盖配置文件设置
3. **代码默认值**: 最后的备用配置

### 配置优先级
```
环境变量 > config.toml > 代码默认值
```

### 环境变量
```bash
ALIYUN_APP_KEY=*********
ALIYUN_APP_SECRET=l77vwNhbVtax65GRQz9NnnOxxREz5AZS
ALIYUN_APP_CODE=ac2c8231f12445928b757dd27e67dbce
ALIYUN_API_URL=https://idcert.market.alicloudapi.com/idcard
USE_REAL_API=true
```

## 🔒 安全特性

### 数据保护
- **敏感信息脱敏**: 姓名和身份证号在日志中自动脱敏
- **配置安全**: API密钥通过配置文件管理，不硬编码
- **传输安全**: 使用HTTPS加密传输

### 错误处理
- **网络超时**: 15秒超时保护
- **重试机制**: 支持重试配置
- **错误分类**: 详细的错误码和消息

## 📊 使用统计

### API调用流程
1. 用户提交姓名和身份证号
2. 系统调用阿里云API进行验证
3. 解析响应结果
4. 存储验证结果到数据库（二级认证）
5. 返回验证结果给用户

### 存储策略
- **二级认证**: 永久存储到数据库，包含完整验证详情
- **一级认证**: 临时缓存，30分钟自动过期

## 🧪 测试

### 测试脚本
```bash
# 测试配置读取
node test-config.js

# 测试API调用
node test-aliyun-get.js

# 测试完整认证系统
node test-verification-system.js
```

### 测试数据
```javascript
// 测试用数据（请使用真实有效的身份证号进行测试）
{
  "name": "张三",
  "idcard": "110101199001011234"
}
```

## 📝 注意事项

1. **API配额**: 请注意API调用次数限制
2. **测试数据**: 使用真实有效的身份证号进行测试
3. **错误处理**: 妥善处理各种错误情况
4. **数据合规**: 遵守相关法律法规，保护用户隐私

## 🔄 更新记录

- **2025-07-17**: 初始配置完成
- **2025-07-17**: 集成到config.toml配置文件
- **2025-07-17**: 完成真实API测试验证

## 📞 技术支持

如有问题，请参考：
1. 阿里云API市场文档
2. 系统错误日志
3. 测试脚本输出
