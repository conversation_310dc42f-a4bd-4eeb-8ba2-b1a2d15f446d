{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"cards-container\"\n};\nconst _hoisted_5 = {\n  class: \"main-card\"\n};\nconst _hoisted_6 = {\n  class: \"email-item\"\n};\nconst _hoisted_7 = {\n  class: \"side-cards\"\n};\nconst _hoisted_8 = {\n  class: \"side-card\"\n};\nconst _hoisted_9 = {\n  class: \"compact-content\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"service-list\"\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"empty-state\"\n};\nconst _hoisted_12 = {\n  class: \"side-card\"\n};\nconst _hoisted_13 = {\n  class: \"card-header\"\n};\nconst _hoisted_14 = {\n  class: \"compact-content\"\n};\nconst _hoisted_15 = {\n  class: \"security-display\"\n};\nconst _hoisted_16 = {\n  class: \"security-tip\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [3]\n    }), _createCommentVNode(\" 完全重构的布局 \"), _createElementVNode(\"div\", _hoisted_4, [_createCommentVNode(\" 主要信息卡片 \"), _createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NCard\"], {\n      bordered: false,\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n        \"label-placement\": \"top\",\n        column: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"ID\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"实名状态\"\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n            bordered: false,\n            type: $setup.userStore.user?.level2_verified ? 'success' : 'warning',\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"注册时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"最后登录时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.last_login || $setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"最后登录 IP\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"用户状态\"\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"正常\")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"绑定邮箱\",\n          span: 2\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n            text: \"\",\n            type: \"primary\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"换绑\")])),\n            _: 1 /* STABLE */,\n            __: [5]\n          })])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NButton\"], {\n        type: \"primary\",\n        ghost: \"\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n        style: {\n          \"margin-top\": \"16px\"\n        }\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 更改密码 \")])),\n        _: 1 /* STABLE */,\n        __: [6]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])]), _createCommentVNode(\" 右侧两个卡片容器 \"), _createElementVNode(\"div\", _hoisted_7, [_createCommentVNode(\" 服务卡片 \"), _createElementVNode(\"div\", _hoisted_8, [_createVNode($setup[\"NCard\"], {\n      title: \"可使用空旷账户登录的服务\",\n      bordered: false,\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [$setup.recentApps && $setup.recentApps.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps.slice(0, 3), app => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: app.id,\n          class: \"service-item\"\n        }, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n          class: \"service-dot\"\n        }, null, -1 /* CACHED */)), _createElementVNode(\"span\", null, _toDisplayString(app.name), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode($setup[\"NIcon\"], {\n        size: \"20\",\n        color: \"#d0d0d0\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"AppsOutline\"])]),\n        _: 1 /* STABLE */\n      }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"暂无服务\", -1 /* CACHED */))]))])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])]), _createCommentVNode(\" 安全指数卡片 \"), _createElementVNode(\"div\", _hoisted_12, [_createVNode($setup[\"NCard\"], {\n      bordered: false,\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"账户安全指数\", -1 /* CACHED */)), _createVNode($setup[\"NButton\"], {\n        text: \"\",\n        type: \"primary\",\n        size: \"small\",\n        onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/security'))\n      }, {\n        icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n          default: _withCtx(() => [_createVNode($setup[\"AddOutline\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })])]),\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n        class: \"score-big\",\n        style: _normalizeStyle({\n          color: $setup.getSecurityColor($setup.securityScore)\n        })\n      }, _toDisplayString($setup.securityScore), 5 /* TEXT, STYLE */), _createVNode($setup[\"NTag\"], {\n        type: $setup.getSecurityLevelType($setup.securityScore),\n        bordered: false,\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getSecurityLevelText($setup.securityScore)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"div\", _hoisted_16, [_cache[11] || (_cache[11] = _createTextVNode(\" 你还未完成中心邮箱、安全认证 \")), _createVNode($setup[\"NButton\"], {\n        text: \"\",\n        type: \"primary\",\n        size: \"tiny\",\n        onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/security'))\n      }, {\n        default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\" 立即完善→ \")])),\n        _: 1 /* STABLE */,\n        __: [10]\n      })])])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "color", "isDarkMode", "column", "label", "id", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "span", "_hoisted_6", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "style", "_hoisted_7", "_hoisted_8", "_hoisted_9", "recentApps", "length", "_hoisted_10", "_Fragment", "_renderList", "slice", "app", "key", "name", "_hoisted_11", "_hoisted_12", "header", "_withCtx", "_hoisted_13", "icon", "_hoisted_14", "_hoisted_15", "_normalizeStyle", "getSecurityColor", "securityScore", "getSecurityLevelType", "getSecurityLevelText", "_hoisted_16"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <!-- 完全重构的布局 -->\n        <div class=\"cards-container\">\n          <!-- 主要信息卡片 -->\n          <div class=\"main-card\">\n            <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions label-placement=\"top\" :column=\"2\">\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\" size=\"small\">\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n              </n-descriptions>\n              <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                更改密码\n              </n-button>\n            </n-card>\n          </div>\n\n          <!-- 右侧两个卡片容器 -->\n          <div class=\"side-cards\">\n            <!-- 服务卡片 -->\n            <div class=\"side-card\">\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"compact-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item\">\n                      <div class=\"service-dot\"></div>\n                      <span>{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"empty-state\">\n                    <n-icon size=\"20\" color=\"#d0d0d0\"><apps-outline /></n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n\n            <!-- 安全指数卡片 -->\n            <div class=\"side-card\">\n              <n-card :bordered=\"false\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <template #header>\n                  <div class=\"card-header\">\n                    <span>账户安全指数</span>\n                    <n-button text type=\"primary\" size=\"small\" @click=\"$router.push('/security')\">\n                      <template #icon><n-icon><add-outline /></n-icon></template>\n                    </n-button>\n                  </div>\n                </template>\n                <div class=\"compact-content\">\n                  <div class=\"security-display\">\n                    <div class=\"score-big\" :style=\"{ color: getSecurityColor(securityScore) }\">\n                      {{ securityScore }}\n                    </div>\n                    <n-tag :type=\"getSecurityLevelType(securityScore)\" :bordered=\"false\" size=\"small\">\n                      {{ getSecurityLevelText(securityScore) }}\n                    </n-tag>\n                    <div class=\"security-tip\">\n                      你还未完成中心邮箱、安全认证\n                      <n-button text type=\"primary\" size=\"tiny\" @click=\"$router.push('/security')\">\n                        立即完善→\n                      </n-button>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline,\n  AddOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n\n\n\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n// 同步右侧卡片高度到左侧信息卡片高度\nconst syncCardHeights = () => {\n  if (userInfoSection.value && rightCardsSection.value) {\n    // 获取左侧信息卡片的实际高度\n    const userInfoHeight = userInfoSection.value.offsetHeight;\n\n    // 设置右侧容器的高度等于左侧信息卡片的高度\n    rightCardsSection.value.style.height = `${userInfoHeight}px`;\n\n    console.log('同步高度:', userInfoHeight);\n  }\n};\n\nonMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n\n  // 等待DOM渲染完成后同步高度\n  setTimeout(() => {\n    syncCardHeights();\n  }, 200);\n\n  // 监听窗口大小变化，重新同步高度\n  window.addEventListener('resize', syncCardHeights);\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n/* 重构后的布局样式 */\n.dashboard-main-layout {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n/* 完全重构的布局 - 精确控制高度 */\n.cards-container {\n  display: flex;\n  gap: 16px;\n  flex: 1;\n  align-items: flex-start; /* 从顶部对齐，不拉伸 */\n}\n\n.main-card {\n  flex: 2; /* 占2/3宽度 */\n}\n\n.side-cards {\n  flex: 1; /* 占1/3宽度 */\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.side-card {\n  /* 让卡片保持紧凑的高度 */\n}\n\n/* 卡片基础样式 */\n.main-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n}\n\n.side-card .n-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: auto; /* 让卡片根据内容确定高度 */\n}\n\n/* 紧凑的卡片内容样式 */\n.compact-content {\n  padding: 12px 0;\n  min-height: 80px; /* 设置较小的最小高度 */\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n/* 服务列表样式 */\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  width: 100%;\n}\n\n.service-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n/* 空状态样式 */\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 安全指数样式 */\n.security-display {\n  text-align: center;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px; /* 减小间距 */\n}\n\n.score-big {\n  font-size: 28px; /* 减小字体大小 */\n  font-weight: bold;\n  line-height: 1;\n}\n\n.security-tip {\n  font-size: 11px; /* 减小字体大小 */\n  color: var(--text-color-3);\n  text-align: center;\n  line-height: 1.3;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n\n\n.service-card-wrapper,\n.security-card-wrapper {\n  flex: 1; /* 两个卡片平分右侧高度 */\n  display: flex;\n  flex-direction: column;\n}\n\n.service-card-new,\n.security-card-new {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  /* 移除高度限制，让它们平分父容器高度 */\n}\n\n/* 服务卡片内容样式 */\n.service-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.service-list-new {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.service-item-new {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.service-dot-new {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: #18a058;\n  flex-shrink: 0;\n}\n\n.service-name-new {\n  font-size: 14px;\n  color: var(--text-color);\n}\n\n.no-services-new {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: #d0d0d0;\n  font-size: 14px;\n}\n\n/* 安全指数卡片样式 */\n.security-header-new {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 500;\n}\n\n.security-action-btn-new {\n  opacity: 0.7;\n  transition: opacity 0.2s;\n}\n\n.security-action-btn-new:hover {\n  opacity: 1;\n}\n\n.security-content-new {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  gap: 16px;\n  padding: 12px 0;\n  min-height: 80px;\n}\n\n.security-score-new {\n  text-align: center;\n}\n\n.score-number-new {\n  font-size: 48px;\n  font-weight: bold;\n  line-height: 1;\n  margin-bottom: 8px;\n}\n\n.score-label-new {\n  margin-bottom: 16px;\n}\n\n.security-guide-new {\n  text-align: center;\n}\n\n.guide-text-new {\n  font-size: 12px;\n  color: var(--text-color-3);\n  display: block;\n  margin-bottom: 8px;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 垂直并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n  flex: 1;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  flex-direction: column;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩，适应水平布局 */\n}\n\n.services-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 8px 0;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px; /* 增加项目间距 */\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 12px; /* 增加内边距 */\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.service-item-compact:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.service-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  color: var(--n-text-color-3);\n  font-size: 13px;\n  padding: 20px;\n}\n\n.more-services {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 8px;\n  padding: 4px 8px;\n  background-color: var(--n-color-target);\n  border-radius: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  /* 简洁的安全指数显示 */\n}\n\n.security-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.security-action-btn {\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.security-action-btn:hover {\n  opacity: 1;\n}\n\n.security-index-display {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 16px 0;\n  justify-content: center;\n}\n\n.security-score-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n}\n\n.score-number {\n  font-size: 48px;\n  font-weight: 700;\n  line-height: 1;\n  transition: color 0.3s ease;\n}\n\n.score-label {\n  display: flex;\n  justify-content: center;\n}\n\n.security-guide {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n  text-align: center;\n}\n\n.guide-text {\n  font-size: 13px;\n  color: var(--n-text-color-2);\n  line-height: 1.4;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards {\n    gap: 12px;\n  }\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EASpBA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAW;;EAwBTA,KAAK,EAAC;AAAY;;EAa1BA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAW;;EAEbA,KAAK,EAAC;AAAiB;;;EACsBA,KAAK,EAAC;;;;EAM1CA,KAAK,EAAC;;;EASnBA,KAAK,EAAC;AAAW;;EAGXA,KAAK,EAAC;AAAa;;EAOrBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAkB;;EAOtBA,KAAK,EAAC;AAAc;;uBAzF3CC,mBAAA,CAuGM,OAvGNC,UAuGM,GAtGJC,YAAA,CAqGSC,MAAA;IArGAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MAmGM,CAnGNC,mBAAA,CAmGM,OAnGNC,UAmGM,GAlGJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEhB,KAAK,EAAC;;wBAAa,MAIpEiB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAC,mBAAA,aAAgB,EAChBX,mBAAA,CAwFM,OAxFNY,UAwFM,GAvFJD,mBAAA,YAAe,EACfX,mBAAA,CAkCM,OAlCNa,UAkCM,GAjCJjB,YAAA,CAgCSC,MAAA;MAhCAY,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAK,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;MAAA;;wBAC7D,MA2BiB,CA3BjBnB,YAAA,CA2BiBC,MAAA;QA3BD,iBAAe,EAAC,KAAK;QAAEmB,MAAM,EAAE;;0BAC7C,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;UAFDoB,KAAK,EAAC;QAAI;4BAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;YAEvBtB,YAAA,CAIsBC,MAAA;UAJDoB,KAAK,EAAC;QAAM;4BAC/B,MAEQ,CAFRrB,YAAA,CAEQC,MAAA;YAFAY,QAAQ,EAAE,KAAK;YAAGD,IAAI,EAAEX,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe;YAA0BC,IAAI,EAAC;;8BAC5F,MAAqD,C,kCAAlDvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe,iC;;;;YAGtCvB,YAAA,CAEsBC,MAAA;UAFDoB,KAAK,EAAC;QAAM;4BAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,SAAS,kB;;YAE7C1B,YAAA,CAEsBC,MAAA;UAFDoB,KAAK,EAAC;QAAQ;4BACjC,MAA+E,C,kCAA5EpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,UAAU,IAAI1B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmB,WAAW,kB;;YAE7E5B,YAAA,CAEsBC,MAAA;UAFDoB,KAAK,EAAC;QAAS;4BAClC,MAAyC,C,kCAAtCpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,WAAW,yB;;YAEhC7B,YAAA,CAEsBC,MAAA;UAFDoB,KAAK,EAAC;QAAM;4BAC/B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;YAAvDY,QAAQ,EAAE,KAAK;YAAED,IAAI,EAAC,SAAS;YAACY,IAAI,EAAC;;8BAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;YAEzDd,YAAA,CAKsBC,MAAA;UALDoB,KAAK,EAAC,MAAM;UAAES,IAAI,EAAE;;4BACvC,MAGM,CAHN1B,mBAAA,CAGM,OAHN2B,UAGM,GAFJ3B,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEuB,KAAK,kBAC9BhC,YAAA,CAAwDC,MAAA;YAA9CgC,IAAI,EAAJ,EAAI;YAACrB,IAAI,EAAC,SAAS;YAACY,IAAI,EAAC;;8BAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;UAInDd,YAAA,CAEWC,MAAA;QAFDW,IAAI,EAAC,SAAS;QAACsB,KAAK,EAAL,EAAK;QAAEC,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;QAAeC,KAAyB,EAAzB;UAAA;QAAA;;0BAA0B,MAE5F1B,MAAA,QAAAA,MAAA,O,iBAF4F,QAE5F,E;;;;;8CAIJC,mBAAA,cAAiB,EACjBX,mBAAA,CAgDM,OAhDNqC,UAgDM,GA/CJ1B,mBAAA,UAAa,EACbX,mBAAA,CAeM,OAfNsC,UAeM,GAdJ1C,YAAA,CAaSC,MAAA;MAbDU,KAAK,EAAC,cAAc;MAAEE,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAK,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;MAAA;;wBAClF,MAWM,CAXNf,mBAAA,CAWM,OAXNuC,UAWM,GAVO1C,MAAA,CAAA2C,UAAU,IAAI3C,MAAA,CAAA2C,UAAU,CAACC,MAAM,Q,cAA1C/C,mBAAA,CAKM,OALNgD,WAKM,I,kBAJJhD,mBAAA,CAGMiD,SAAA,QAAAC,WAAA,CAHa/C,MAAA,CAAA2C,UAAU,CAACK,KAAK,QAAvBC,GAAG;6BAAfpD,mBAAA,CAGM;UAHsCqD,GAAG,EAAED,GAAG,CAAC5B,EAAE;UAAEzB,KAAK,EAAC;sCAC7DO,mBAAA,CAA+B;UAA1BP,KAAK,EAAC;QAAa,4BACxBO,mBAAA,CAA2B,cAAAG,gBAAA,CAAlB2C,GAAG,CAACE,IAAI,iB;yDAGrBtD,mBAAA,CAGM,OAHNuD,WAGM,GAFJrD,YAAA,CAA2DC,MAAA;QAAnDuB,IAAI,EAAC,IAAI;QAACN,KAAK,EAAC;;0BAAU,MAAgB,CAAhBlB,YAAA,CAAgBC,MAAA,iB;;oCAClDG,mBAAA,CAAiB,cAAX,MAAI,oB;;8CAMlBW,mBAAA,YAAe,EACfX,mBAAA,CA2BM,OA3BNkD,WA2BM,GA1BJtD,YAAA,CAyBSC,MAAA;MAzBAY,QAAQ,EAAE,KAAK;MAAG,iBAAe;QAAAK,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;MAAA;;MAClDoC,MAAM,EAAAC,QAAA,CACf,MAKM,CALNpD,mBAAA,CAKM,OALNqD,WAKM,G,0BAJJrD,mBAAA,CAAmB,cAAb,QAAM,qBACZJ,YAAA,CAEWC,MAAA;QAFDgC,IAAI,EAAJ,EAAI;QAACrB,IAAI,EAAC,SAAS;QAACY,IAAI,EAAC,OAAO;QAAEW,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;QAClDmB,IAAI,EAAAF,QAAA,CAAC,MAAgC,CAAhCxD,YAAA,CAAgCC,MAAA;4BAAxB,MAAe,CAAfD,YAAA,CAAeC,MAAA,gB;;;;;wBAI7C,MAeM,CAfNG,mBAAA,CAeM,OAfNuD,WAeM,GAdJvD,mBAAA,CAaM,OAbNwD,WAaM,GAZJxD,mBAAA,CAEM;QAFDP,KAAK,EAAC,WAAW;QAAE2C,KAAK,EAAAqB,eAAA;UAAA3C,KAAA,EAAWjB,MAAA,CAAA6D,gBAAgB,CAAC7D,MAAA,CAAA8D,aAAa;QAAA;0BACjE9D,MAAA,CAAA8D,aAAa,yBAElB/D,YAAA,CAEQC,MAAA;QAFAW,IAAI,EAAEX,MAAA,CAAA+D,oBAAoB,CAAC/D,MAAA,CAAA8D,aAAa;QAAIlD,QAAQ,EAAE,KAAK;QAAEW,IAAI,EAAC;;0BACxE,MAAyC,C,kCAAtCvB,MAAA,CAAAgE,oBAAoB,CAAChE,MAAA,CAAA8D,aAAa,kB;;mCAEvC3D,mBAAA,CAKM,OALN8D,WAKM,G,6CALoB,kBAExB,IAAAlE,YAAA,CAEWC,MAAA;QAFDgC,IAAI,EAAJ,EAAI;QAACrB,IAAI,EAAC,SAAS;QAACY,IAAI,EAAC,MAAM;QAAEW,OAAK,EAAArB,MAAA,QAAAA,MAAA,MAAAsB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;0BAAe,MAE7EzB,MAAA,SAAAA,MAAA,Q,iBAF6E,SAE7E,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}