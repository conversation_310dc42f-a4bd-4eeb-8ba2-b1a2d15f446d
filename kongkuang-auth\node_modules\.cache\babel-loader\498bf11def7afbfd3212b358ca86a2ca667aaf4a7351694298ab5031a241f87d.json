{"ast": null, "code": "import { ref, onMounted, computed } from 'vue';\nimport { NSpin, NGrid, NGi, NCard, NAlert, NDescriptions, NDescriptionsItem, NButton, NTag, NList, NListItem, NIcon, NEmpty, useMessage } from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nexport default {\n  __name: 'Dashboard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const userStore = useUserStore();\n    const message = useMessage();\n    const recentApps = ref([]);\n\n    // 检查是否为深色模式\n    const isDarkMode = computed(() => {\n      const themeMode = localStorage.getItem('theme') || 'system';\n      if (themeMode === 'system') {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      return themeMode === 'dark';\n    });\n    const formatDateTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      // Using toLocaleString for a more standard format, customize as needed\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).replace(/\\//g, '-');\n    };\n\n    // 格式化相对时间（如：3天前，2小时前）\n    const formatRelativeTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffMs = now - date;\n      const diffSec = Math.floor(diffMs / 1000);\n      const diffMin = Math.floor(diffSec / 60);\n      const diffHour = Math.floor(diffMin / 60);\n      const diffDay = Math.floor(diffHour / 24);\n      const diffMonth = Math.floor(diffDay / 30);\n      const diffYear = Math.floor(diffMonth / 12);\n      if (diffYear > 0) {\n        return `${diffYear}年前`;\n      } else if (diffMonth > 0) {\n        return `${diffMonth}个月前`;\n      } else if (diffDay > 0) {\n        return `${diffDay}天前`;\n      } else if (diffHour > 0) {\n        return `${diffHour}小时前`;\n      } else if (diffMin > 0) {\n        return `${diffMin}分钟前`;\n      } else {\n        return '刚刚';\n      }\n    };\n\n    // 获取默认头像\n    const getDefaultAvatar = () => {\n      return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n    };\n\n    // 手机号码脱敏\n    const maskPhone = phone => {\n      if (!phone) return '';\n      return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n    };\n\n    // 处理退出登录\n    const handleLogout = async () => {\n      if (confirm('您确定要退出登录吗？')) {\n        try {\n          const apiClient = getApiClient();\n          await apiClient.post('/auth/logout');\n          userStore.logout();\n          message.success('已成功退出登录');\n          window.location.href = '/login';\n        } catch (error) {\n          console.error('退出登录失败:', error);\n          message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n        }\n      }\n    };\n    const fetchDashboardData = async () => {\n      loading.value = true;\n      try {\n        const apiClient = getApiClient();\n\n        // 先只获取仪表盘数据\n        const dashboardResponse = await apiClient.get('/dashboard');\n\n        // 处理仪表盘数据\n        if (dashboardResponse.data && dashboardResponse.data.success) {\n          // 更新用户信息，使用后端返回的格式化数据\n          if (dashboardResponse.data.user) {\n            userStore.user = {\n              ...userStore.user,\n              ...dashboardResponse.data.user,\n              // 使用后端返回的格式化时间，如果没有则使用原始数据\n              createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n              lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n              lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n            };\n          }\n\n          // 更新应用列表\n          recentApps.value = dashboardResponse.data.recentApps || [];\n\n          // 如果后端返回了认证状态，使用它\n          if (dashboardResponse.data.verificationStatus) {\n            verificationStatus.value = dashboardResponse.data.verificationStatus;\n          }\n        }\n        console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n        // 尝试获取认证状态（如果失败不影响主要功能）\n        try {\n          const verificationResponse = await apiClient.get('/users/verification-status');\n          if (verificationResponse.data && verificationResponse.data.success) {\n            verificationStatus.value = {\n              level1Completed: verificationResponse.data.level1Completed || false,\n              level2Completed: verificationResponse.data.level2Completed || false,\n              level1Info: verificationResponse.data.level1Info || null,\n              level2Info: verificationResponse.data.level2Info || null\n            };\n          }\n        } catch (verificationError) {\n          console.warn('获取认证状态失败:', verificationError);\n          // 不显示错误，使用默认值\n        }\n      } catch (error) {\n        console.error(\"Failed to fetch dashboard data:\", error);\n        message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        loading.value = false;\n      }\n    };\n    onMounted(() => {\n      // We need user data, if not present, maybe fetch it or rely on login flow\n      if (!userStore.user) {\n        // This case might happen on a page refresh, you might want to fetch user data\n        // For now, we assume user data is populated from login\n      }\n      fetchDashboardData();\n    });\n    const __returned__ = {\n      loading,\n      userStore,\n      message,\n      recentApps,\n      isDarkMode,\n      formatDateTime,\n      formatRelativeTime,\n      getDefaultAvatar,\n      maskPhone,\n      handleLogout,\n      fetchDashboardData,\n      ref,\n      onMounted,\n      computed,\n      get NSpin() {\n        return NSpin;\n      },\n      get NGrid() {\n        return NGrid;\n      },\n      get NGi() {\n        return NGi;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NDescriptions() {\n        return NDescriptions;\n      },\n      get NDescriptionsItem() {\n        return NDescriptionsItem;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NList() {\n        return NList;\n      },\n      get NListItem() {\n        return NListItem;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NEmpty() {\n        return NEmpty;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "NSpin", "NGrid", "NGi", "NCard", "N<PERSON><PERSON><PERSON>", "NDescriptions", "NDescriptionsItem", "NButton", "NTag", "NList", "NListItem", "NIcon", "NEmpty", "useMessage", "useUserStore", "getApiClient", "loading", "userStore", "message", "recentApps", "isDarkMode", "themeMode", "localStorage", "getItem", "window", "matchMedia", "matches", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "formatRelativeTime", "now", "diffMs", "diffSec", "Math", "floor", "diffMin", "diffHour", "diffDay", "diff<PERSON><PERSON><PERSON>", "diffYear", "getDefaultAvatar", "maskPhone", "phone", "handleLogout", "confirm", "apiClient", "post", "logout", "success", "location", "href", "error", "console", "response", "data", "fetchDashboardData", "value", "dashboardResponse", "get", "user", "createdAt", "registrationTime", "formatted", "lastLoginAt", "lastLoginTime", "lastLoginIp", "verificationStatus", "log", "verificationResponse", "level1Completed", "level2Completed", "level1Info", "level2Info", "verificationError", "warn"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">已实名</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <n-card title=\"可使用 KongKuang ID 登录的服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                  <n-list :show-divider=\"false\">\n                    <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                       {{ app.name }}\n                    </n-list-item>\n                     <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无服务\" />\n                  </n-list>\n        </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 格式化相对时间（如：3天前，2小时前）\nconst formatRelativeTime = (dateString) => {\n  if (!dateString) return 'N/A';\n\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffMs = now - date;\n  const diffSec = Math.floor(diffMs / 1000);\n  const diffMin = Math.floor(diffSec / 60);\n  const diffHour = Math.floor(diffMin / 60);\n  const diffDay = Math.floor(diffHour / 24);\n  const diffMonth = Math.floor(diffDay / 30);\n  const diffYear = Math.floor(diffMonth / 12);\n\n  if (diffYear > 0) {\n    return `${diffYear}年前`;\n  } else if (diffMonth > 0) {\n    return `${diffMonth}个月前`;\n  } else if (diffDay > 0) {\n    return `${diffDay}天前`;\n  } else if (diffHour > 0) {\n    return `${diffHour}小时前`;\n  } else if (diffMin > 0) {\n    return `${diffMin}分钟前`;\n  } else {\n    return '刚刚';\n  }\n};\n\n// 获取默认头像\nconst getDefaultAvatar = () => {\n  return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n};\n\n// 手机号码脱敏\nconst maskPhone = (phone) => {\n  if (!phone) return '';\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n};\n\n// 处理退出登录\nconst handleLogout = async () => {\n  if (confirm('您确定要退出登录吗？')) {\n    try {\n      const apiClient = getApiClient();\n      await apiClient.post('/auth/logout');\n      userStore.logout();\n      message.success('已成功退出登录');\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('退出登录失败:', error);\n      message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n    }\n  }\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n\n    // 先只获取仪表盘数据\n    const dashboardResponse = await apiClient.get('/dashboard');\n\n    // 处理仪表盘数据\n    if (dashboardResponse.data && dashboardResponse.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (dashboardResponse.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...dashboardResponse.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = dashboardResponse.data.recentApps || [];\n\n      // 如果后端返回了认证状态，使用它\n      if (dashboardResponse.data.verificationStatus) {\n        verificationStatus.value = dashboardResponse.data.verificationStatus;\n      }\n    }\n\n    console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n    // 尝试获取认证状态（如果失败不影响主要功能）\n    try {\n      const verificationResponse = await apiClient.get('/users/verification-status');\n      if (verificationResponse.data && verificationResponse.data.success) {\n        verificationStatus.value = {\n          level1Completed: verificationResponse.data.level1Completed || false,\n          level2Completed: verificationResponse.data.level2Completed || false,\n          level1Info: verificationResponse.data.level1Info || null,\n          level2Info: verificationResponse.data.level2Info || null\n        };\n      }\n    } catch (verificationError) {\n      console.warn('获取认证状态失败:', verificationError);\n      // 不显示错误，使用默认值\n    }\n\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.verification-status {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-label {\n  font-weight: 500;\n  min-width: 70px;\n}\n\n.login-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.login-ip {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n.email-item,\n.phone-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.email-info,\n.phone-info {\n  display: flex;\n  align-items: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.verification-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.verification-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.verification-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.verification-icon {\n  flex-shrink: 0;\n}\n\n.verification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.verification-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n\n.verification-desc {\n  font-size: 12px;\n  color: var(--n-text-color-2);\n  margin-bottom: 2px;\n}\n\n.verification-time {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n}\n\n.verification-action {\n  flex-shrink: 0;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n.service-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  width: 100%;\n}\n\n.service-name {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": "AAqEA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SACEC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,UAAS,QACJ,UAAU;AACjB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,OAAO,GAAGnB,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMoB,SAAS,GAAGH,YAAY,CAAC,CAAC;IAChC,MAAMI,OAAO,GAAGL,UAAU,CAAC,CAAC;IAE5B,MAAMM,UAAU,GAAGtB,GAAG,CAAC,EAAE,CAAC;;IAE1B;IACA,MAAMuB,UAAU,GAAGrB,QAAQ,CAAC,MAAM;MAChC,MAAMsB,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ;MAC3D,IAAIF,SAAS,KAAK,QAAQ,EAAE;QAC1B,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAClE;MACA,OAAOL,SAAS,KAAK,MAAM;IAC7B,CAAC,CAAC;IAEF,MAAMM,cAAc,GAAIC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC;MACA,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACxB,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAIZ,UAAU,IAAK;MACzC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAE7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMa,GAAG,GAAG,IAAIX,IAAI,CAAC,CAAC;MACtB,MAAMY,MAAM,GAAGD,GAAG,GAAGZ,IAAI;MACzB,MAAMc,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,GAAG,IAAI,CAAC;MACzC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACxC,MAAMI,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,EAAE,CAAC;MACzC,MAAME,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE,CAAC;MACzC,MAAME,SAAS,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,GAAG,EAAE,CAAC;MAC1C,MAAME,QAAQ,GAAGN,IAAI,CAACC,KAAK,CAACI,SAAS,GAAG,EAAE,CAAC;MAE3C,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,GAAGA,QAAQ,IAAI;MACxB,CAAC,MAAM,IAAID,SAAS,GAAG,CAAC,EAAE;QACxB,OAAO,GAAGA,SAAS,KAAK;MAC1B,CAAC,MAAM,IAAID,OAAO,GAAG,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,IAAI;MACvB,CAAC,MAAM,IAAID,QAAQ,GAAG,CAAC,EAAE;QACvB,OAAO,GAAGA,QAAQ,KAAK;MACzB,CAAC,MAAM,IAAID,OAAO,GAAG,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,KAAK;MACxB,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;;IAED;IACA,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,4DAA4D;IACrE,CAAC;;IAED;IACA,MAAMC,SAAS,GAAIC,KAAK,IAAK;MAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;MACrB,OAAOA,KAAK,CAACd,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;IACzD,CAAC;;IAED;IACA,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAIC,OAAO,CAAC,YAAY,CAAC,EAAE;QACzB,IAAI;UACF,MAAMC,SAAS,GAAGzC,YAAY,CAAC,CAAC;UAChC,MAAMyC,SAAS,CAACC,IAAI,CAAC,cAAc,CAAC;UACpCxC,SAAS,CAACyC,MAAM,CAAC,CAAC;UAClBxC,OAAO,CAACyC,OAAO,CAAC,SAAS,CAAC;UAC1BnC,MAAM,CAACoC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B5C,OAAO,CAAC4C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACE,QAAQ,EAAEC,IAAI,EAAE/C,OAAO,IAAI4C,KAAK,CAAC5C,OAAO,CAAC,CAAC;QAC9E;MACF;IACF,CAAC;IAED,MAAMgD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrClD,OAAO,CAACmD,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMX,SAAS,GAAGzC,YAAY,CAAC,CAAC;;QAEhC;QACA,MAAMqD,iBAAiB,GAAG,MAAMZ,SAAS,CAACa,GAAG,CAAC,YAAY,CAAC;;QAE3D;QACA,IAAID,iBAAiB,CAACH,IAAI,IAAIG,iBAAiB,CAACH,IAAI,CAACN,OAAO,EAAE;UAC5D;UACA,IAAIS,iBAAiB,CAACH,IAAI,CAACK,IAAI,EAAE;YAC/BrD,SAAS,CAACqD,IAAI,GAAG;cACf,GAAGrD,SAAS,CAACqD,IAAI;cACjB,GAAGF,iBAAiB,CAACH,IAAI,CAACK,IAAI;cAC9B;cACAC,SAAS,EAAEH,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACE,gBAAgB,EAAEC,SAAS,IAAIxD,SAAS,CAACqD,IAAI,EAAEC,SAAS;cAC/FG,WAAW,EAAEN,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACK,aAAa,EAAEF,SAAS,IAAIxD,SAAS,CAACqD,IAAI,EAAEI,WAAW;cAChGE,WAAW,EAAER,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACM,WAAW,IAAI3D,SAAS,CAACqD,IAAI,EAAEM;YAC1E,CAAC;UACH;;UAEA;UACAzD,UAAU,CAACgD,KAAK,GAAGC,iBAAiB,CAACH,IAAI,CAAC9C,UAAU,IAAI,EAAE;;UAE1D;UACA,IAAIiD,iBAAiB,CAACH,IAAI,CAACY,kBAAkB,EAAE;YAC7CA,kBAAkB,CAACV,KAAK,GAAGC,iBAAiB,CAACH,IAAI,CAACY,kBAAkB;UACtE;QACF;QAEAd,OAAO,CAACe,GAAG,CAAC,YAAY,EAAEV,iBAAiB,CAACH,IAAI,CAAC;;QAEjD;QACA,IAAI;UACF,MAAMc,oBAAoB,GAAG,MAAMvB,SAAS,CAACa,GAAG,CAAC,4BAA4B,CAAC;UAC9E,IAAIU,oBAAoB,CAACd,IAAI,IAAIc,oBAAoB,CAACd,IAAI,CAACN,OAAO,EAAE;YAClEkB,kBAAkB,CAACV,KAAK,GAAG;cACzBa,eAAe,EAAED,oBAAoB,CAACd,IAAI,CAACe,eAAe,IAAI,KAAK;cACnEC,eAAe,EAAEF,oBAAoB,CAACd,IAAI,CAACgB,eAAe,IAAI,KAAK;cACnEC,UAAU,EAAEH,oBAAoB,CAACd,IAAI,CAACiB,UAAU,IAAI,IAAI;cACxDC,UAAU,EAAEJ,oBAAoB,CAACd,IAAI,CAACkB,UAAU,IAAI;YACtD,CAAC;UACH;QACF,CAAC,CAAC,OAAOC,iBAAiB,EAAE;UAC1BrB,OAAO,CAACsB,IAAI,CAAC,WAAW,EAAED,iBAAiB,CAAC;UAC5C;QACF;MAEF,CAAC,CAAC,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD5C,OAAO,CAAC4C,KAAK,CAAC,aAAa,IAAIA,KAAK,CAACE,QAAQ,EAAEC,IAAI,EAAE/C,OAAO,IAAI4C,KAAK,CAAC5C,OAAO,CAAC,CAAC;MACjF,CAAC,SAAS;QACRF,OAAO,CAACmD,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAEGrE,SAAS,CAAC,MAAM;MAClB;MACA,IAAI,CAACmB,SAAS,CAACqD,IAAI,EAAE;QACnB;QACA;MAAA;MAEFJ,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}