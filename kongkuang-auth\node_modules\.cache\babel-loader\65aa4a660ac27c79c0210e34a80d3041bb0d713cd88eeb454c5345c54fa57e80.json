{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"email-item\"\n};\nconst _hoisted_5 = {\n  class: \"side-cards\"\n};\nconst _hoisted_6 = {\n  class: \"verification-summary\"\n};\nconst _hoisted_7 = {\n  class: \"verification-item\"\n};\nconst _hoisted_8 = {\n  class: \"verification-icon\"\n};\nconst _hoisted_9 = {\n  class: \"verification-content\"\n};\nconst _hoisted_10 = {\n  class: \"verification-desc\"\n};\nconst _hoisted_11 = {\n  key: 0,\n  class: \"verification-time\"\n};\nconst _hoisted_12 = {\n  class: \"verification-action\"\n};\nconst _hoisted_13 = {\n  class: \"verification-item\"\n};\nconst _hoisted_14 = {\n  class: \"verification-icon\"\n};\nconst _hoisted_15 = {\n  class: \"verification-content\"\n};\nconst _hoisted_16 = {\n  class: \"verification-desc\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"verification-time\"\n};\nconst _hoisted_18 = {\n  class: \"verification-action\"\n};\nconst _hoisted_19 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_20 = {\n  class: \"service-info\"\n};\nconst _hoisted_21 = {\n  class: \"service-name\"\n};\nconst _hoisted_22 = {\n  class: \"service-time\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [5]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 3,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"ID\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"已实名\")])),\n                _: 1 /* STABLE */,\n                __: [6]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录 IP\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"正常\")])),\n                _: 1 /* STABLE */,\n                __: [7]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [8]\n              })])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-top\": \"16px\"\n            }\n          }, {\n            default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\" 更改密码 \")])),\n            _: 1 /* STABLE */,\n            __: [9]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" 认证状态卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"认证状态\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode($setup[\"NIcon\"], {\n            size: \"20\",\n            color: $setup.verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"WalletOutline\"])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_9, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", {\n            class: \"verification-title\"\n          }, \"一级认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.verificationStatus.level1Completed ? '识脸支付认证' : '未完成'), 1 /* TEXT */), $setup.verificationStatus.level1Completed && $setup.verificationStatus.level1Info ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, _toDisplayString($setup.formatRelativeTime($setup.verificationStatus.level1Info.verifiedAt)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_12, [!$setup.verificationStatus.level1Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n            key: 0,\n            text: \"\",\n            type: \"primary\",\n            size: \"small\",\n            onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/verification'))\n          }, {\n            default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 去认证 \")])),\n            _: 1 /* STABLE */,\n            __: [11]\n          })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n            key: 1,\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 已完成 \")])),\n            _: 1 /* STABLE */,\n            __: [12]\n          }))])]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createVNode($setup[\"NIcon\"], {\n            size: \"20\",\n            color: $setup.verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"ShieldCheckmarkOutline\"])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_15, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", {\n            class: \"verification-title\"\n          }, \"二级认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.verificationStatus.level2Completed ? '二要素验证' : '未完成'), 1 /* TEXT */), $setup.verificationStatus.level2Completed && $setup.verificationStatus.level2Info ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, _toDisplayString($setup.formatRelativeTime($setup.verificationStatus.level2Info.verifiedAt)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_18, [!$setup.verificationStatus.level2Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n            key: 0,\n            text: \"\",\n            type: \"primary\",\n            size: \"small\",\n            onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/verification'))\n          }, {\n            default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 去认证 \")])),\n            _: 1 /* STABLE */,\n            __: [14]\n          })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n            key: 1,\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\" 已完成 \")])),\n            _: 1 /* STABLE */,\n            __: [15]\n          }))])])])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 快捷操作卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"快捷操作\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_19, [_createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[3] || (_cache[3] = $event => _ctx.$router.push('/applications')),\n            style: {\n              \"margin-bottom\": \"8px\"\n            }\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"AppsOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[16] || (_cache[16] = _createTextVNode(\" 我的应用 \"))]),\n            _: 1 /* STABLE */,\n            __: [16]\n          }), _createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-bottom\": \"8px\"\n            }\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"LockClosedOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[17] || (_cache[17] = _createTextVNode(\" 安全中心 \"))]),\n            _: 1 /* STABLE */,\n            __: [17]\n          }), _createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: $setup.handleLogout\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"LogOutOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[18] || (_cache[18] = _createTextVNode(\" 退出登录 \"))]),\n            _: 1 /* STABLE */,\n            __: [18]\n          })])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 授权服务卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"授权服务\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NList\"], {\n            \"show-divider\": false\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps, app => {\n              return _openBlock(), _createBlock($setup[\"NListItem\"], {\n                key: app.id,\n                class: \"service-item\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, _toDisplayString(app.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, _toDisplayString($setup.formatRelativeTime(app.lastUsed)), 1 /* TEXT */)])]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */);\n            }), 128 /* KEYED_FRAGMENT */)), !$setup.recentApps || $setup.recentApps.length === 0 ? (_openBlock(), _createBlock($setup[\"NEmpty\"], {\n              key: 0,\n              description: \"暂无授权服务\",\n              size: \"small\"\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "column", "label", "id", "size", "formatDateTime", "createdAt", "lastLoginAt", "lastLoginIp", "_hoisted_4", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_5", "_createCommentVNode", "_hoisted_6", "_hoisted_7", "_hoisted_8", "verificationStatus", "level1Completed", "_hoisted_9", "_hoisted_10", "level1Info", "_hoisted_11", "formatRelativeTime", "verifiedAt", "_hoisted_12", "_createBlock", "_hoisted_13", "_hoisted_14", "level2Completed", "_hoisted_15", "_hoisted_16", "level2Info", "_hoisted_17", "_hoisted_18", "_hoisted_19", "block", "icon", "_withCtx", "handleLogout", "_Fragment", "_renderList", "recentApps", "app", "key", "_hoisted_20", "_hoisted_21", "name", "_hoisted_22", "lastUsed", "length", "description"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">已实名</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <!-- 认证状态卡片 -->\n              <n-card title=\"认证状态\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"verification-summary\">\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <wallet-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">一级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level1Completed ? '识脸支付认证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level1Completed && verificationStatus.level1Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level1Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level1Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <shield-checkmark-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">二级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level2Completed ? '二要素验证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level2Completed && verificationStatus.level2Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level2Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level2Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 快捷操作卡片 -->\n              <n-card title=\"快捷操作\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"quick-actions\">\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/applications')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><apps-outline /></n-icon>\n                    </template>\n                    我的应用\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/security')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><lock-closed-outline /></n-icon>\n                    </template>\n                    安全中心\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"handleLogout\"\n                  >\n                    <template #icon>\n                      <n-icon><log-out-outline /></n-icon>\n                    </template>\n                    退出登录\n                  </n-button>\n                </div>\n              </n-card>\n\n              <!-- 授权服务卡片 -->\n              <n-card title=\"授权服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <n-list :show-divider=\"false\">\n                  <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                    <div class=\"service-info\">\n                      <div class=\"service-name\">{{ app.name }}</div>\n                      <div class=\"service-time\">{{ formatRelativeTime(app.lastUsed) }}</div>\n                    </div>\n                  </n-list-item>\n                  <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无授权服务\" size=\"small\" />\n                </n-list>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  NAvatar,\n  NText,\n  NTooltip,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  InformationCircleOutline,\n  CheckmarkCircleOutline,\n  LockClosedOutline,\n  ShieldCheckmarkOutline,\n  PersonOutline,\n  WalletOutline,\n  AppsOutline,\n  LogOutOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\nconst verificationStatus = ref({\n  level1Completed: false,\n  level2Completed: false,\n  level1Info: null,\n  level2Info: null\n});\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 格式化相对时间（如：3天前，2小时前）\nconst formatRelativeTime = (dateString) => {\n  if (!dateString) return 'N/A';\n\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffMs = now - date;\n  const diffSec = Math.floor(diffMs / 1000);\n  const diffMin = Math.floor(diffSec / 60);\n  const diffHour = Math.floor(diffMin / 60);\n  const diffDay = Math.floor(diffHour / 24);\n  const diffMonth = Math.floor(diffDay / 30);\n  const diffYear = Math.floor(diffMonth / 12);\n\n  if (diffYear > 0) {\n    return `${diffYear}年前`;\n  } else if (diffMonth > 0) {\n    return `${diffMonth}个月前`;\n  } else if (diffDay > 0) {\n    return `${diffDay}天前`;\n  } else if (diffHour > 0) {\n    return `${diffHour}小时前`;\n  } else if (diffMin > 0) {\n    return `${diffMin}分钟前`;\n  } else {\n    return '刚刚';\n  }\n};\n\n// 获取默认头像\nconst getDefaultAvatar = () => {\n  return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n};\n\n// 手机号码脱敏\nconst maskPhone = (phone) => {\n  if (!phone) return '';\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n};\n\n// 处理退出登录\nconst handleLogout = async () => {\n  if (confirm('您确定要退出登录吗？')) {\n    try {\n      const apiClient = getApiClient();\n      await apiClient.post('/auth/logout');\n      userStore.logout();\n      message.success('已成功退出登录');\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('退出登录失败:', error);\n      message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n    }\n  }\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n\n    // 先只获取仪表盘数据\n    const dashboardResponse = await apiClient.get('/dashboard');\n\n    // 处理仪表盘数据\n    if (dashboardResponse.data && dashboardResponse.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (dashboardResponse.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...dashboardResponse.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = dashboardResponse.data.recentApps || [];\n\n      // 如果后端返回了认证状态，使用它\n      if (dashboardResponse.data.verificationStatus) {\n        verificationStatus.value = dashboardResponse.data.verificationStatus;\n      }\n    }\n\n    console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n    // 尝试获取认证状态（如果失败不影响主要功能）\n    try {\n      const verificationResponse = await apiClient.get('/users/verification-status');\n      if (verificationResponse.data && verificationResponse.data.success) {\n        verificationStatus.value = {\n          level1Completed: verificationResponse.data.level1Completed || false,\n          level2Completed: verificationResponse.data.level2Completed || false,\n          level1Info: verificationResponse.data.level1Info || null,\n          level2Info: verificationResponse.data.level2Info || null\n        };\n      }\n    } catch (verificationError) {\n      console.warn('获取认证状态失败:', verificationError);\n      // 不显示错误，使用默认值\n    }\n\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.verification-status {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-label {\n  font-weight: 500;\n  min-width: 70px;\n}\n\n.login-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.login-ip {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n.email-item,\n.phone-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.email-info,\n.phone-info {\n  display: flex;\n  align-items: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.verification-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.verification-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.verification-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.verification-icon {\n  flex-shrink: 0;\n}\n\n.verification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.verification-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n\n.verification-desc {\n  font-size: 12px;\n  color: var(--n-text-color-2);\n  margin-bottom: 2px;\n}\n\n.verification-time {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n}\n\n.verification-action {\n  flex-shrink: 0;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n.service-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  width: 100%;\n}\n\n.service-name {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAkCVA,KAAK,EAAC;AAAY;;EAaxBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAmB;;;EAGkDA,KAAK,EAAC;;;EAInFA,KAAK,EAAC;AAAqB;;EAqB7BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAmB;;;EAGkDA,KAAK,EAAC;;;EAInFA,KAAK,EAAC;AAAqB;;EAyB/BA,KAAK,EAAC;AAAe;;EA2CjBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAc;;uBArL7CC,mBAAA,CAgMM,OAhMNC,UAgMM,GA/LJC,YAAA,CA8LSC,MAAA;IA9LAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA4LM,CA5LNC,mBAAA,CA4LM,OA5LNC,UA4LM,GA3LJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEhB,KAAK,EAAC;;wBAAa,MAIpEiB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAkLSC,MAAA;MAlLD,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAmCO,CAnCPhB,YAAA,CAmCOC,MAAA;QAnCAgB,IAAI,EAAE;MAAC;0BACZ,MAiCS,CAjCTjB,YAAA,CAiCSC,MAAA;UAjCAY,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACrF,MA4BiB,CA5BjBnB,YAAA,CA4BiBC,MAAA;YA3Bf,iBAAe,EAAC,KAAK;YACpBmB,MAAM,EAAE;;8BAET,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAI;gCAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;gBAEvBtB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAAgE,CAAhErB,YAAA,CAAgEC,MAAA;gBAAxDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAGT,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;;;gBAE1Dd,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEgB,SAAS,kB;;gBAE7CzB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAQ;gCACjC,MAAiD,C,kCAA9CpB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,WAAW,kB;;gBAE/C1B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAS;gCAClC,MAAiC,C,kCAA9BpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,WAAW,iB;;gBAEhC3B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC9B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;gBAAvDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;gBAEzDd,YAAA,CAKqBC,MAAA;cALAoB,KAAK,EAAC,MAAM;cAAEJ,IAAI,EAAE;;gCACxC,MAGE,CAHFb,mBAAA,CAGE,OAHFwB,UAGE,GAFAxB,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,KAAK,kBAC7B7B,YAAA,CAAwDC,MAAA;gBAA9C6B,IAAI,EAAJ,EAAI;gBAAClB,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;cAInDd,YAAA,CAEUC,MAAA;YAFAW,IAAI,EAAC,SAAS;YAACmB,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YAAepB,KAAyB,EAAzB;cAAA;YAAA;;8BAA0B,MAE7FF,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;;;UAIJd,YAAA,CA2IOC,MAAA;QA3IAgB,IAAI,EAAE;MAAC;0BACZ,MAyIM,CAzINb,mBAAA,CAyIM,OAzINiC,UAyIM,GAxIJC,mBAAA,YAAe,EACftC,YAAA,CAgFSC,MAAA;UAhFDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MA8EM,CA9ENf,mBAAA,CA8EM,OA9ENmC,UA8EM,GA7EJnC,mBAAA,CAqCM,OArCNoC,UAqCM,GApCJpC,mBAAA,CAOM,OAPNqC,UAOM,GANJzC,YAAA,CAKSC,MAAA;YAJPsB,IAAI,EAAC,IAAI;YACRL,KAAK,EAAEjB,MAAA,CAAAyC,kBAAkB,CAACC,eAAe;;8BAE1C,MAAkB,CAAlB3C,YAAA,CAAkBC,MAAA,mB;;0CAGtBG,mBAAA,CAQM,OARNwC,UAQM,G,4BAPJxC,mBAAA,CAA0C;YAArCP,KAAK,EAAC;UAAoB,GAAC,MAAI,qBACpCO,mBAAA,CAEM,OAFNyC,WAEM,EAAAtC,gBAAA,CADDN,MAAA,CAAAyC,kBAAkB,CAACC,eAAe,qCAE5B1C,MAAA,CAAAyC,kBAAkB,CAACC,eAAe,IAAI1C,MAAA,CAAAyC,kBAAkB,CAACI,UAAU,I,cAA9EhD,mBAAA,CAEM,OAFNiD,WAEM,EAAAxC,gBAAA,CADDN,MAAA,CAAA+C,kBAAkB,CAAC/C,MAAA,CAAAyC,kBAAkB,CAACI,UAAU,CAACG,UAAU,qB,qCAGlE7C,mBAAA,CAkBM,OAlBN8C,WAkBM,G,CAhBKjD,MAAA,CAAAyC,kBAAkB,CAACC,eAAe,I,cAD3CQ,YAAA,CAQWlD,MAAA;;YANT6B,IAAI,EAAJ,EAAI;YACJlB,IAAI,EAAC,SAAS;YACdW,IAAI,EAAC,OAAO;YACXS,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;8BACrB,MAEDtB,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;+BACAqC,YAAA,CAOQlD,MAAA;;YALLY,QAAQ,EAAE,KAAK;YAChBD,IAAI,EAAC,SAAS;YACdW,IAAI,EAAC;;8BACN,MAEDT,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;mBAIJV,mBAAA,CAqCM,OArCNgD,WAqCM,GApCJhD,mBAAA,CAOM,OAPNiD,WAOM,GANJrD,YAAA,CAKSC,MAAA;YAJPsB,IAAI,EAAC,IAAI;YACRL,KAAK,EAAEjB,MAAA,CAAAyC,kBAAkB,CAACY,eAAe;;8BAE1C,MAA4B,CAA5BtD,YAAA,CAA4BC,MAAA,4B;;0CAGhCG,mBAAA,CAQM,OARNmD,WAQM,G,4BAPJnD,mBAAA,CAA0C;YAArCP,KAAK,EAAC;UAAoB,GAAC,MAAI,qBACpCO,mBAAA,CAEM,OAFNoD,WAEM,EAAAjD,gBAAA,CADDN,MAAA,CAAAyC,kBAAkB,CAACY,eAAe,oCAE5BrD,MAAA,CAAAyC,kBAAkB,CAACY,eAAe,IAAIrD,MAAA,CAAAyC,kBAAkB,CAACe,UAAU,I,cAA9E3D,mBAAA,CAEM,OAFN4D,WAEM,EAAAnD,gBAAA,CADDN,MAAA,CAAA+C,kBAAkB,CAAC/C,MAAA,CAAAyC,kBAAkB,CAACe,UAAU,CAACR,UAAU,qB,qCAGlE7C,mBAAA,CAkBM,OAlBNuD,WAkBM,G,CAhBK1D,MAAA,CAAAyC,kBAAkB,CAACY,eAAe,I,cAD3CH,YAAA,CAQWlD,MAAA;;YANT6B,IAAI,EAAJ,EAAI;YACJlB,IAAI,EAAC,SAAS;YACdW,IAAI,EAAC,OAAO;YACXS,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;8BACrB,MAEDtB,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;+BACAqC,YAAA,CAOQlD,MAAA;;YALLY,QAAQ,EAAE,KAAK;YAChBD,IAAI,EAAC,SAAS;YACdW,IAAI,EAAC;;8BACN,MAEDT,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;;;gDAMRwB,mBAAA,YAAe,EACftC,YAAA,CAsCSC,MAAA;UAtCDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MAoCM,CApCNf,mBAAA,CAoCM,OApCNwD,WAoCM,GAnCJ5D,YAAA,CAWWC,MAAA;YAVT4D,KAAK,EAAL,EAAK;YACLjD,IAAI,EAAC,SAAS;YACdmB,KAAK,EAAL,EAAK;YACJC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YACpBpB,KAA2B,EAA3B;cAAA;YAAA;;YAEW8C,IAAI,EAAAC,QAAA,CACb,MAAiC,CAAjC/D,YAAA,CAAiCC,MAAA;gCAAzB,MAAgB,CAAhBD,YAAA,CAAgBC,MAAA,iB;;;8BACf,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAWWC,MAAA;YAVT4D,KAAK,EAAL,EAAK;YACLjD,IAAI,EAAC,SAAS;YACdmB,KAAK,EAAL,EAAK;YACJC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YACpBpB,KAA2B,EAA3B;cAAA;YAAA;;YAEW8C,IAAI,EAAAC,QAAA,CACb,MAAwC,CAAxC/D,YAAA,CAAwCC,MAAA;gCAAhC,MAAuB,CAAvBD,YAAA,CAAuBC,MAAA,uB;;;8BACtB,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAUWC,MAAA;YATT4D,KAAK,EAAL,EAAK;YACLjD,IAAI,EAAC,SAAS;YACdmB,KAAK,EAAL,EAAK;YACJC,OAAK,EAAE/B,MAAA,CAAA+D;;YAEGF,IAAI,EAAAC,QAAA,CACb,MAAoC,CAApC/D,YAAA,CAAoCC,MAAA;gCAA5B,MAAmB,CAAnBD,YAAA,CAAmBC,MAAA,mB;;;8BAClB,MAEb,C,6CAFa,QAEb,G;;;;;gDAIJqC,mBAAA,YAAe,EACftC,YAAA,CAUSC,MAAA;UAVDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MAQS,CARTnB,YAAA,CAQSC,MAAA;YARA,cAAY,EAAE;UAAK;8BACb,MAAyB,E,kBAAtCH,mBAAA,CAKcmE,SAAA,QAAAC,WAAA,CALajE,MAAA,CAAAkE,UAAU,EAAjBC,GAAG;mCAAvBjB,YAAA,CAKclD,MAAA;gBAL0BoE,GAAG,EAAED,GAAG,CAAC9C,EAAE;gBAAEzB,KAAK,EAAC;;kCACzD,MAGM,CAHNO,mBAAA,CAGM,OAHNkE,WAGM,GAFJlE,mBAAA,CAA8C,OAA9CmE,WAA8C,EAAAhE,gBAAA,CAAjB6D,GAAG,CAACI,IAAI,kBACrCpE,mBAAA,CAAsE,OAAtEqE,WAAsE,EAAAlE,gBAAA,CAAzCN,MAAA,CAAA+C,kBAAkB,CAACoB,GAAG,CAACM,QAAQ,kB;;;6CAGhDzE,MAAA,CAAAkE,UAAU,IAAIlE,MAAA,CAAAkE,UAAU,CAACQ,MAAM,U,cAA/CxB,YAAA,CAA2FlD,MAAA;;cAApC2E,WAAW,EAAC,QAAQ;cAACrD,IAAI,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}