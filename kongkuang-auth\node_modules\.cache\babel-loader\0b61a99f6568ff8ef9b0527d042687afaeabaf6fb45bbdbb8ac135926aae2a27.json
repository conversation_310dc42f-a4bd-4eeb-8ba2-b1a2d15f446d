{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, resolveDynamicComponent as _resolveDynamicComponent } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"email-item\"\n};\nconst _hoisted_5 = {\n  class: \"side-cards-horizontal\"\n};\nconst _hoisted_6 = {\n  class: \"security-dashboard\"\n};\nconst _hoisted_7 = {\n  class: \"security-gauge\"\n};\nconst _hoisted_8 = {\n  class: \"gauge-container\"\n};\nconst _hoisted_9 = {\n  class: \"gauge-svg\",\n  viewBox: \"0 0 200 120\",\n  width: \"200\",\n  height: \"120\"\n};\nconst _hoisted_10 = [\"stroke\"];\nconst _hoisted_11 = [\"d\", \"stroke\"];\nconst _hoisted_12 = {\n  x: \"100\",\n  y: \"85\",\n  \"text-anchor\": \"middle\",\n  class: \"gauge-score\"\n};\nconst _hoisted_13 = {\n  class: \"security-level\"\n};\nconst _hoisted_14 = {\n  class: \"security-items\"\n};\nconst _hoisted_15 = {\n  class: \"item-icon\"\n};\nconst _hoisted_16 = {\n  class: \"item-content\"\n};\nconst _hoisted_17 = {\n  class: \"item-name\"\n};\nconst _hoisted_18 = {\n  class: \"item-action\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [1]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 3,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"ID\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: $setup.userStore.user?.level2_verified ? 'success' : 'warning',\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.last_login || $setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录 IP\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"正常\")])),\n                _: 1 /* STABLE */,\n                __: [2]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [3]\n              })])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-top\": \"16px\"\n            }\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 更改密码 \")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createCommentVNode(\" 服务卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"授权服务\",\n          bordered: false,\n          class: \"horizontal-card service-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NList\"], {\n            \"show-divider\": false\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps, app => {\n              return _openBlock(), _createBlock($setup[\"NListItem\"], {\n                key: app.id,\n                class: \"service-item\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(app.name), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */);\n            }), 128 /* KEYED_FRAGMENT */)), !$setup.recentApps || $setup.recentApps.length === 0 ? (_openBlock(), _createBlock($setup[\"NEmpty\"], {\n              key: 0,\n              description: \"暂无服务\",\n              size: \"small\"\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 账户安全指标卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"安全指标\",\n          bordered: false,\n          class: \"horizontal-card security-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 安全评分仪表盘 \"), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_9, [_createCommentVNode(\" 背景弧线 \"), _createElementVNode(\"path\", {\n            d: \"M 20 100 A 80 80 0 0 1 180 100\",\n            fill: \"none\",\n            stroke: $setup.isDarkMode ? '#3a3a3a' : '#e0e0e0',\n            \"stroke-width\": \"8\",\n            \"stroke-linecap\": \"round\"\n          }, null, 8 /* PROPS */, _hoisted_10), _createCommentVNode(\" 进度弧线 \"), _createElementVNode(\"path\", {\n            d: $setup.getSecurityArcPath($setup.securityScore),\n            fill: \"none\",\n            stroke: $setup.getSecurityColor($setup.securityScore),\n            \"stroke-width\": \"8\",\n            \"stroke-linecap\": \"round\",\n            class: \"security-arc\"\n          }, null, 8 /* PROPS */, _hoisted_11), _createCommentVNode(\" 中心文字 \"), _createElementVNode(\"text\", _hoisted_12, _toDisplayString($setup.securityScore), 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"text\", {\n            x: \"100\",\n            y: \"100\",\n            \"text-anchor\": \"middle\",\n            class: \"gauge-label\"\n          }, \" 安全评分 \", -1 /* CACHED */))]))]), _createCommentVNode(\" 安全等级显示 \"), _createElementVNode(\"div\", _hoisted_13, [_createVNode($setup[\"NTag\"], {\n            type: $setup.getSecurityLevelType($setup.securityScore),\n            bordered: false,\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getSecurityLevelText($setup.securityScore)), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"])])]), _createCommentVNode(\" 安全项目列表 \"), _createElementVNode(\"div\", _hoisted_14, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.securityItems, item => {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"security-item\",\n              key: item.key\n            }, [_createElementVNode(\"div\", _hoisted_15, [_createVNode($setup[\"NIcon\"], {\n              size: \"16\",\n              color: item.status ? '#18a058' : '#d03050'\n            }, {\n              default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(item.status ? 'checkmark-circle-outline' : 'close-circle-outline')))]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", _hoisted_17, _toDisplayString(item.name), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_18, [!item.status ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n              key: 0,\n              text: \"\",\n              type: \"primary\",\n              size: \"tiny\",\n              onClick: $event => $setup.handleSecurityAction(item.key)\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(item.actionText), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n          }), 128 /* KEYED_FRAGMENT */))])])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "viewBox", "width", "height", "x", "y", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "column", "label", "id", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "_hoisted_4", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_5", "_createCommentVNode", "_Fragment", "_renderList", "recentApps", "app", "_createBlock", "key", "name", "length", "description", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "d", "fill", "stroke", "getSecurityArcPath", "securityScore", "getSecurityColor", "_hoisted_12", "_hoisted_13", "getSecurityLevelType", "getSecurityLevelText", "_hoisted_14", "securityItems", "item", "_hoisted_15", "status", "_resolveDynamicComponent", "_hoisted_16", "_hoisted_17", "_hoisted_18", "handleSecurityAction", "actionText"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards-horizontal\">\n              <!-- 服务卡片 -->\n              <n-card title=\"授权服务\" :bordered=\"false\" class=\"horizontal-card service-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                  <n-list :show-divider=\"false\">\n                    <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                       {{ app.name }}\n                    </n-list-item>\n                     <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无服务\" size=\"small\" />\n                  </n-list>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card title=\"安全指标\" :bordered=\"false\" class=\"horizontal-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"security-dashboard\">\n                  <!-- 安全评分仪表盘 -->\n                  <div class=\"security-gauge\">\n                    <div class=\"gauge-container\">\n                      <svg class=\"gauge-svg\" viewBox=\"0 0 200 120\" width=\"200\" height=\"120\">\n                        <!-- 背景弧线 -->\n                        <path\n                          d=\"M 20 100 A 80 80 0 0 1 180 100\"\n                          fill=\"none\"\n                          :stroke=\"isDarkMode ? '#3a3a3a' : '#e0e0e0'\"\n                          stroke-width=\"8\"\n                          stroke-linecap=\"round\"\n                        />\n                        <!-- 进度弧线 -->\n                        <path\n                          :d=\"getSecurityArcPath(securityScore)\"\n                          fill=\"none\"\n                          :stroke=\"getSecurityColor(securityScore)\"\n                          stroke-width=\"8\"\n                          stroke-linecap=\"round\"\n                          class=\"security-arc\"\n                        />\n                        <!-- 中心文字 -->\n                        <text x=\"100\" y=\"85\" text-anchor=\"middle\" class=\"gauge-score\">\n                          {{ securityScore }}\n                        </text>\n                        <text x=\"100\" y=\"100\" text-anchor=\"middle\" class=\"gauge-label\">\n                          安全评分\n                        </text>\n                      </svg>\n                    </div>\n\n                    <!-- 安全等级显示 -->\n                    <div class=\"security-level\">\n                      <n-tag\n                        :type=\"getSecurityLevelType(securityScore)\"\n                        :bordered=\"false\"\n                        size=\"small\"\n                      >\n                        {{ getSecurityLevelText(securityScore) }}\n                      </n-tag>\n                    </div>\n                  </div>\n\n                  <!-- 安全项目列表 -->\n                  <div class=\"security-items\">\n                    <div class=\"security-item\" v-for=\"item in securityItems\" :key=\"item.key\">\n                      <div class=\"item-icon\">\n                        <n-icon\n                          size=\"16\"\n                          :color=\"item.status ? '#18a058' : '#d03050'\"\n                        >\n                          <component :is=\"item.status ? 'checkmark-circle-outline' : 'close-circle-outline'\" />\n                        </n-icon>\n                      </div>\n                      <div class=\"item-content\">\n                        <span class=\"item-name\">{{ item.name }}</span>\n                      </div>\n                      <div class=\"item-action\">\n                        <n-button\n                          v-if=\"!item.status\"\n                          text\n                          type=\"primary\"\n                          size=\"tiny\"\n                          @click=\"handleSecurityAction(item.key)\"\n                        >\n                          {{ item.actionText }}\n                        </n-button>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.side-cards-horizontal {\n  display: flex;\n  flex-direction: row;\n  gap: 16px;\n  height: 100%;\n}\n\n.horizontal-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n  flex: 1;\n  min-width: 0;\n}\n\n.service-card {\n  flex: 0.6; /* 服务卡片占60%宽度 */\n}\n\n.security-card {\n  flex: 0.4; /* 安全指标卡片占40%宽度 */\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  margin-top: 16px;\n}\n\n.security-dashboard {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.security-gauge {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.gauge-container {\n  position: relative;\n  width: 160px;\n  height: 100px;\n}\n\n.gauge-svg {\n  width: 100%;\n  height: 100%;\n}\n\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n.gauge-score {\n  font-size: 28px;\n  font-weight: 600;\n  fill: var(--n-text-color-1);\n}\n\n.gauge-label {\n  font-size: 12px;\n  fill: var(--n-text-color-2);\n}\n\n.security-level {\n  margin-top: 8px;\n}\n\n.security-items {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.security-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.security-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.item-icon {\n  flex-shrink: 0;\n}\n\n.item-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.item-name {\n  font-size: 14px;\n  font-weight: 500;\n}\n\n.item-action {\n  flex-shrink: 0;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAwCVA,KAAK,EAAC;AAAY;;EAaxBA,KAAK,EAAC;AAAuB;;EAazBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC,WAAW;EAACC,OAAO,EAAC,aAAa;EAACC,KAAK,EAAC,KAAK;EAACC,MAAM,EAAC;;;;;EAmBxDC,CAAC,EAAC,KAAK;EAACC,CAAC,EAAC,IAAI;EAAC,aAAW,EAAC,QAAQ;EAACL,KAAK,EAAC;;;EAU/CA,KAAK,EAAC;AAAgB;;EAYxBA,KAAK,EAAC;AAAgB;;EAElBA,KAAK,EAAC;AAAW;;EAQjBA,KAAK,EAAC;AAAc;;EACjBA,KAAK,EAAC;AAAW;;EAEpBA,KAAK,EAAC;AAAa;;uBA/H5CM,mBAAA,CAmJM,OAnJNC,UAmJM,GAlJJC,YAAA,CAiJSC,MAAA;IAjJAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA+IM,CA/INC,mBAAA,CA+IM,OA/INC,UA+IM,GA9IJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAErB,KAAK,EAAC;;wBAAa,MAIpEsB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAqISC,MAAA;MArID,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAyCO,CAzCPhB,YAAA,CAyCOC,MAAA;QAzCAgB,IAAI,EAAE;MAAC;0BACZ,MAuCS,CAvCTjB,YAAA,CAuCSC,MAAA;UAvCAY,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACrF,MAkCiB,CAlCjBnB,YAAA,CAkCiBC,MAAA;YAjCf,iBAAe,EAAC,KAAK;YACpBmB,MAAM,EAAE;;8BAET,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAI;gCAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;gBAEvBtB,YAAA,CAQsBC,MAAA;cARDoB,KAAK,EAAC;YAAM;gCAC/B,MAMQ,CANRrB,YAAA,CAMQC,MAAA;gBALLY,QAAQ,EAAE,KAAK;gBACfD,IAAI,EAAEX,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe;gBACtCC,IAAI,EAAC;;kCAEL,MAAqD,C,kCAAlDvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe,iC;;;;gBAGtCvB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,SAAS,kB;;gBAE7C1B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAQ;gCACjC,MAA+E,C,kCAA5EpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,UAAU,IAAI1B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmB,WAAW,kB;;gBAE7E5B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAS;gCAClC,MAAyC,C,kCAAtCpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,WAAW,yB;;gBAEhC7B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC9B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;gBAAvDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;gBAEzDd,YAAA,CAKqBC,MAAA;cALAoB,KAAK,EAAC,MAAM;cAAEJ,IAAI,EAAE;;gCACxC,MAGE,CAHFb,mBAAA,CAGE,OAHF0B,UAGE,GAFA1B,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEsB,KAAK,kBAC7B/B,YAAA,CAAwDC,MAAA;gBAA9C+B,IAAI,EAAJ,EAAI;gBAACpB,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;cAInDd,YAAA,CAEUC,MAAA;YAFAW,IAAI,EAAC,SAAS;YAACqB,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YAAetB,KAAyB,EAAzB;cAAA;YAAA;;8BAA0B,MAE7FF,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;;;UAIJd,YAAA,CAwFOC,MAAA;QAxFAgB,IAAI,EAAE;MAAC;0BACZ,MAsFM,CAtFNb,mBAAA,CAsFM,OAtFNmC,UAsFM,GArFJC,mBAAA,UAAa,EACbxC,YAAA,CAOSC,MAAA;UAPDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,8BAA8B;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7G,MAKS,CALTnB,YAAA,CAKSC,MAAA;YALA,cAAY,EAAE;UAAK;8BACb,MAAyB,E,kBAAtCH,mBAAA,CAEc2C,SAAA,QAAAC,WAAA,CAFazC,MAAA,CAAA0C,UAAU,EAAjBC,GAAG;mCAAvBC,YAAA,CAEc5C,MAAA;gBAF0B6C,GAAG,EAAEF,GAAG,CAACtB,EAAE;gBAAE9B,KAAK,EAAC;;kCACxD,MAAc,C,kCAAXoD,GAAG,CAACG,IAAI,iB;;;6CAEG9C,MAAA,CAAA0C,UAAU,IAAI1C,MAAA,CAAA0C,UAAU,CAACK,MAAM,U,cAA/CH,YAAA,CAAyF5C,MAAA;;cAAlCgD,WAAW,EAAC,MAAM;cAACzB,IAAI,EAAC;;;;;gDAItFgB,mBAAA,cAAiB,EACjBxC,YAAA,CAyESC,MAAA;UAzEDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAErB,KAAK,EAAC,+BAA+B;UAAE,iBAAe;YAAA0B,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAChH,MAuEM,CAvENf,mBAAA,CAuEM,OAvEN8C,UAuEM,GAtEJV,mBAAA,aAAgB,EAChBpC,mBAAA,CAwCM,OAxCN+C,UAwCM,GAvCJ/C,mBAAA,CA2BM,OA3BNgD,UA2BM,I,cA1BJtD,mBAAA,CAyBM,OAzBNuD,UAyBM,GAxBJb,mBAAA,UAAa,EACbpC,mBAAA,CAME;YALAkD,CAAC,EAAC,gCAAgC;YAClCC,IAAI,EAAC,MAAM;YACVC,MAAM,EAAEvD,MAAA,CAAAkB,UAAU;YACnB,cAAY,EAAC,GAAG;YAChB,gBAAc,EAAC;gDAEjBqB,mBAAA,UAAa,EACbpC,mBAAA,CAOE;YANCkD,CAAC,EAAErD,MAAA,CAAAwD,kBAAkB,CAACxD,MAAA,CAAAyD,aAAa;YACpCH,IAAI,EAAC,MAAM;YACVC,MAAM,EAAEvD,MAAA,CAAA0D,gBAAgB,CAAC1D,MAAA,CAAAyD,aAAa;YACvC,cAAY,EAAC,GAAG;YAChB,gBAAc,EAAC,OAAO;YACtBlE,KAAK,EAAC;gDAERgD,mBAAA,UAAa,EACbpC,mBAAA,CAEO,QAFPwD,WAEO,EAAArD,gBAAA,CADFN,MAAA,CAAAyD,aAAa,kB,0BAElBtD,mBAAA,CAEO;YAFDR,CAAC,EAAC,KAAK;YAACC,CAAC,EAAC,KAAK;YAAC,aAAW,EAAC,QAAQ;YAACL,KAAK,EAAC;aAAc,QAE/D,oB,MAIJgD,mBAAA,YAAe,EACfpC,mBAAA,CAQM,OARNyD,WAQM,GAPJ7D,YAAA,CAMQC,MAAA;YALLW,IAAI,EAAEX,MAAA,CAAA6D,oBAAoB,CAAC7D,MAAA,CAAAyD,aAAa;YACxC7C,QAAQ,EAAE,KAAK;YAChBW,IAAI,EAAC;;8BAEL,MAAyC,C,kCAAtCvB,MAAA,CAAA8D,oBAAoB,CAAC9D,MAAA,CAAAyD,aAAa,kB;;2CAK3ClB,mBAAA,YAAe,EACfpC,mBAAA,CAyBM,OAzBN4D,WAyBM,I,kBAxBJlE,mBAAA,CAuBM2C,SAAA,QAAAC,WAAA,CAvBoCzC,MAAA,CAAAgE,aAAa,EAArBC,IAAI;iCAAtCpE,mBAAA,CAuBM;cAvBDN,KAAK,EAAC,eAAe;cAAgCsD,GAAG,EAAEoB,IAAI,CAACpB;gBAClE1C,mBAAA,CAOM,OAPN+D,WAOM,GANJnE,YAAA,CAKSC,MAAA;cAJPuB,IAAI,EAAC,IAAI;cACRN,KAAK,EAAEgD,IAAI,CAACE,MAAM;;gCAEnB,MAAqF,E,cAArFvB,YAAA,CAAqFwB,wBAAA,CAArEH,IAAI,CAACE,MAAM,0D;;8DAG/BhE,mBAAA,CAEM,OAFNkE,WAEM,GADJlE,mBAAA,CAA8C,QAA9CmE,WAA8C,EAAAhE,gBAAA,CAAnB2D,IAAI,CAACnB,IAAI,iB,GAEtC3C,mBAAA,CAUM,OAVNoE,WAUM,G,CARKN,IAAI,CAACE,MAAM,I,cADpBvB,YAAA,CAQW5C,MAAA;;cANT+B,IAAI,EAAJ,EAAI;cACJpB,IAAI,EAAC,SAAS;cACdY,IAAI,EAAC,MAAM;cACVU,OAAK,EAAAC,MAAA,IAAElC,MAAA,CAAAwE,oBAAoB,CAACP,IAAI,CAACpB,GAAG;;gCAErC,MAAqB,C,kCAAlBoB,IAAI,CAACQ,UAAU,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}