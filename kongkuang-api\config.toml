# 空旷账户中心配置文件

# 服务器配置
[server]
port = 3001
env = "development"

# 数据库配置
[database]
host = "***********"
port = 3306
username = "authtest"
password = "HCeB8hZYWCZHShtQ"
name = "authtest"
timezone = "+08:00"
max_connections = 10

# JWT配置
[jwt]
secret = "kongkuang_jwt_secret_key_development"
expires_in = "7d"



# 邮件配置 (SMTP)
[mail]
host = "smtp.larksuite.com" # 请替换为您的SMTP服务器地址, 例如 smtp.qq.com
port = 465
secure = true
user = "<EMAIL>"
pass = "VorUuNGNSEf7prof"
from = "空旷账户 | KongKuang Auth <<EMAIL>>"

# 腾讯云短信配置
[tencent_sms]
secret_id = "your_tencent_secret_id"
secret_key = "your_tencent_secret_key"
app_id = "your_tencent_sms_app_id"
sign = "your_tencent_sms_sign"
template_id = "your_tencent_sms_template_id"

# 前端地址
[frontend]
url = "http://localhost:8080"

# OAuth 2.0配置
[oauth]
client_id = "your_oauth_client_id"
client_secret = "your_oauth_client_secret"
redirect_uri = "http://localhost:8080/oauth/callback"

# 验证码配置
[verification_code]
expire_minutes = 10
max_attempts = 5
cooldown_seconds = 60

# 头像和上传配置
[upload]
avatar_path = "uploads/avatars"
max_file_size = 5242880  # 5MB

# 阿里云OSS配置
[aliyun_oss]
access_key_id = "your_access_key_id"          # 请替换为您的AccessKey ID
access_key_secret = "your_access_key_secret"  # 请替换为您的AccessKey Secret
bucket = "your-bucket-name"                   # 请替换为您的Bucket名称
region = "oss-cn-hangzhou"                    # 请替换为您的Bucket所在地域
endpoint = "https://oss-cn-hangzhou.aliyuncs.com" # OSS访问域名
internal = false                              # 是否使用内网
secure = true                                 # 是否使用HTTPS
cname = false                                 # 是否使用自定义域名
avatar_directory = "avatars/"                 # 头像存储目录

# 阿里云API市场配置 - 身份证二要素验证
[aliyun_api]
app_key = "*********"
app_secret = "l77vwNhbVtax65GRQz9NnnOxxREz5AZS"
app_code = "ac2c8231f12445928b757dd27e67dbce"
id_verification_url = "https://idcert.market.alicloudapi.com/idcard"
timeout = 15000                               # API请求超时时间(毫秒)
use_real_api = false                          # 开发环境是否使用真实API