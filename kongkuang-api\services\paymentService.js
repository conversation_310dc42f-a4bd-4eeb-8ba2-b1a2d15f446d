/**
 * 支付服务 - 处理识脸支付认证
 */

const config = require('../config/config');
const crypto = require('crypto');
const level1Cache = require('../utils/level1Cache');

/**
 * 支付宝识脸支付服务
 */
class AlipayFaceAuthService {
  constructor() {
    this.appId = config.payment?.alipay?.app_id;
    this.gatewayUrl = config.payment?.alipay?.gateway_url || 'https://openapi.alipay.com/gateway.do';
    this.charset = config.payment?.alipay?.charset || 'utf-8';
    this.signType = config.payment?.alipay?.sign_type || 'RSA2';
    this.faceAuthAmount = config.payment?.alipay?.face_auth_amount || 1.00;
  }

  /**
   * 创建识脸支付订单
   */
  async createFaceAuthOrder(userId, userInfo = {}) {
    try {
      const orderId = `ALIPAY_FACE_${userId}_${Date.now()}`;
      
      // 在开发环境中模拟支付宝识脸支付
      if (config.server.env === 'development') {
        console.log(`[开发模式] 创建支付宝识脸支付订单: ${orderId}`);
        
        return {
          success: true,
          orderId: orderId,
          paymentUrl: `/payment/alipay/face-auth?orderId=${orderId}`,
          amount: this.faceAuthAmount,
          qrCode: `data:image/svg+xml;base64,${Buffer.from(`
            <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200">
              <rect width="200" height="200" fill="#1677ff"/>
              <text x="100" y="100" text-anchor="middle" fill="white" font-size="12">
                支付宝识脸支付
              </text>
              <text x="100" y="120" text-anchor="middle" fill="white" font-size="10">
                ${orderId}
              </text>
            </svg>
          `).toString('base64')}`,
          message: '请使用支付宝扫码进行识脸支付认证'
        };
      }
      
      // 生产环境中调用真实的支付宝API
      // TODO: 集成真实的支付宝识脸支付API
      
      return {
        success: false,
        message: '支付宝识脸支付功能暂未在生产环境中实现'
      };
      
    } catch (error) {
      console.error('创建支付宝识脸支付订单失败:', error);
      return {
        success: false,
        message: '创建支付订单失败',
        error: error.message
      };
    }
  }

  /**
   * 模拟支付宝识脸支付成功回调
   */
  async simulateFaceAuthSuccess(orderId, userId) {
    try {
      // 模拟从支付宝获取的实名信息
      const faceAuthResult = {
        verified: true,
        verifiedAt: new Date(),
        paymentMethod: 'alipay_face',
        realName: '李明', // 模拟从支付宝识脸获取的真实姓名
        idNumber: '110101199001011234', // 模拟从支付宝获取的身份证号
        certNo: '110101199001011234',
        userName: '李明',
        orderId: orderId,
        amount: this.faceAuthAmount,
        faceScore: 0.95, // 人脸识别置信度
        authType: 'face_payment',
        alipayUserId: 'mock_alipay_user_' + userId,
        authTime: new Date().toISOString()
      };

      // 将识脸认证结果存储到缓存
      level1Cache.setLevel1Status(userId, faceAuthResult);
      
      console.log(`支付宝识脸支付认证成功 - 用户ID: ${userId}, 姓名: ${faceAuthResult.realName.replace(/(.{1}).*(.{1})/, '$1***$2')}`);
      
      return {
        success: true,
        message: '支付宝识脸认证成功',
        data: {
          realName: faceAuthResult.realName.replace(/(.{1}).*(.{1})/, '$1***$2'),
          idNumber: faceAuthResult.idNumber.replace(/(.{6}).*(.{4})/, '$1****$2'),
          verifiedAt: faceAuthResult.verifiedAt,
          faceScore: faceAuthResult.faceScore,
          authType: faceAuthResult.authType
        }
      };
      
    } catch (error) {
      console.error('支付宝识脸支付回调处理失败:', error);
      return {
        success: false,
        message: '支付回调处理失败',
        error: error.message
      };
    }
  }
}

/**
 * 微信识脸支付服务
 */
class WechatFaceAuthService {
  constructor() {
    this.appId = config.payment?.wechat?.app_id;
    this.mchId = config.payment?.wechat?.mch_id;
    this.apiKey = config.payment?.wechat?.api_key;
    this.notifyUrl = config.payment?.wechat?.notify_url;
    this.faceAuthAmount = config.payment?.wechat?.face_auth_amount || 100; // 分
  }

  /**
   * 创建识脸支付订单
   */
  async createFaceAuthOrder(userId, userInfo = {}) {
    try {
      const orderId = `WECHAT_FACE_${userId}_${Date.now()}`;
      
      // 在开发环境中模拟微信识脸支付
      if (config.server.env === 'development') {
        console.log(`[开发模式] 创建微信识脸支付订单: ${orderId}`);
        
        return {
          success: true,
          orderId: orderId,
          paymentUrl: `/payment/wechat/face-auth?orderId=${orderId}`,
          amount: this.faceAuthAmount / 100, // 转换为元
          qrCode: `data:image/svg+xml;base64,${Buffer.from(`
            <svg xmlns="http://www.w3.org/2000/svg" width="200" height="200">
              <rect width="200" height="200" fill="#07c160"/>
              <text x="100" y="100" text-anchor="middle" fill="white" font-size="12">
                微信识脸支付
              </text>
              <text x="100" y="120" text-anchor="middle" fill="white" font-size="10">
                ${orderId}
              </text>
            </svg>
          `).toString('base64')}`,
          message: '请使用微信扫码进行识脸支付认证'
        };
      }
      
      // 生产环境中调用真实的微信支付API
      // TODO: 集成真实的微信识脸支付API
      
      return {
        success: false,
        message: '微信识脸支付功能暂未在生产环境中实现'
      };
      
    } catch (error) {
      console.error('创建微信识脸支付订单失败:', error);
      return {
        success: false,
        message: '创建支付订单失败',
        error: error.message
      };
    }
  }

  /**
   * 模拟微信识脸支付成功回调
   */
  async simulateFaceAuthSuccess(orderId, userId) {
    try {
      // 模拟从微信获取的实名信息
      const faceAuthResult = {
        verified: true,
        verifiedAt: new Date(),
        paymentMethod: 'wechat_face',
        realName: '王芳', // 模拟从微信识脸获取的真实姓名
        idNumber: '110101199002021234', // 模拟从微信获取的身份证号
        certId: '110101199002021234',
        trueName: '王芳',
        orderId: orderId,
        amount: this.faceAuthAmount / 100,
        faceScore: 0.92, // 人脸识别置信度
        authType: 'face_payment',
        wechatOpenId: 'mock_wechat_openid_' + userId,
        authTime: new Date().toISOString()
      };

      // 将识脸认证结果存储到缓存
      level1Cache.setLevel1Status(userId, faceAuthResult);
      
      console.log(`微信识脸支付认证成功 - 用户ID: ${userId}, 姓名: ${faceAuthResult.realName.replace(/(.{1}).*(.{1})/, '$1***$2')}`);
      
      return {
        success: true,
        message: '微信识脸认证成功',
        data: {
          realName: faceAuthResult.realName.replace(/(.{1}).*(.{1})/, '$1***$2'),
          idNumber: faceAuthResult.idNumber.replace(/(.{6}).*(.{4})/, '$1****$2'),
          verifiedAt: faceAuthResult.verifiedAt,
          faceScore: faceAuthResult.faceScore,
          authType: faceAuthResult.authType
        }
      };
      
    } catch (error) {
      console.error('微信识脸支付回调处理失败:', error);
      return {
        success: false,
        message: '支付回调处理失败',
        error: error.message
      };
    }
  }
}

module.exports = {
  AlipayFaceAuthService,
  WechatFaceAuthService
};
