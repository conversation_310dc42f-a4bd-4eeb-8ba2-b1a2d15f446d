{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"email-item\"\n};\nconst _hoisted_5 = {\n  class: \"side-cards\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [1]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 3,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"ID\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"已实名\")])),\n                _: 1 /* STABLE */,\n                __: [2]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录 IP\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"正常\")])),\n                _: 1 /* STABLE */,\n                __: [3]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [4]\n              })])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-top\": \"16px\"\n            }\n          }, {\n            default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\" 更改密码 \")])),\n            _: 1 /* STABLE */,\n            __: [5]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NCard\"], {\n          title: \"可使用 KongKuang ID 登录的服务\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NList\"], {\n            \"show-divider\": false\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps, app => {\n              return _openBlock(), _createBlock($setup[\"NListItem\"], {\n                key: app.id,\n                class: \"service-item\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(app.name), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */);\n            }), 128 /* KEYED_FRAGMENT */)), !$setup.recentApps || $setup.recentApps.length === 0 ? (_openBlock(), _createBlock($setup[\"NEmpty\"], {\n              key: 0,\n              description: \"暂无服务\"\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "column", "label", "id", "size", "formatDateTime", "createdAt", "lastLoginAt", "lastLoginIp", "_hoisted_4", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_5", "_Fragment", "_renderList", "recentApps", "app", "_createBlock", "key", "name", "length", "description"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">已实名</n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <n-card title=\"可使用 KongKuang ID 登录的服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                  <n-list :show-divider=\"false\">\n                    <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                       {{ app.name }}\n                    </n-list-item>\n                     <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无服务\" />\n                  </n-list>\n        </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n\n    // 先只获取仪表盘数据\n    const dashboardResponse = await apiClient.get('/dashboard');\n\n    // 处理仪表盘数据\n    if (dashboardResponse.data && dashboardResponse.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (dashboardResponse.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...dashboardResponse.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = dashboardResponse.data.recentApps || [];\n\n      // 如果后端返回了认证状态，使用它\n      if (dashboardResponse.data.verificationStatus) {\n        verificationStatus.value = dashboardResponse.data.verificationStatus;\n      }\n    }\n\n    console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n    // 尝试获取认证状态（如果失败不影响主要功能）\n    try {\n      const verificationResponse = await apiClient.get('/users/verification-status');\n      if (verificationResponse.data && verificationResponse.data.success) {\n        verificationStatus.value = {\n          level1Completed: verificationResponse.data.level1Completed || false,\n          level2Completed: verificationResponse.data.level2Completed || false,\n          level1Info: verificationResponse.data.level1Info || null,\n          level2Info: verificationResponse.data.level2Info || null\n        };\n      }\n    } catch (verificationError) {\n      console.warn('获取认证状态失败:', verificationError);\n      // 不显示错误，使用默认值\n    }\n\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.verification-status {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-label {\n  font-weight: 500;\n  min-width: 70px;\n}\n\n.login-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.login-ip {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n.email-item,\n.phone-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.email-info,\n.phone-info {\n  display: flex;\n  align-items: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.verification-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.verification-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.verification-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.verification-icon {\n  flex-shrink: 0;\n}\n\n.verification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.verification-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n\n.verification-desc {\n  font-size: 12px;\n  color: var(--n-text-color-2);\n  margin-bottom: 2px;\n}\n\n.verification-time {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n}\n\n.verification-action {\n  flex-shrink: 0;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n.service-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  width: 100%;\n}\n\n.service-name {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAkCVA,KAAK,EAAC;AAAY;;EAaxBA,KAAK,EAAC;AAAY;;uBAlDjCC,mBAAA,CAgEM,OAhENC,UAgEM,GA/DJC,YAAA,CA8DSC,MAAA;IA9DAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA4DM,CA5DNC,mBAAA,CA4DM,OA5DNC,UA4DM,GA3DJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEhB,KAAK,EAAC;;wBAAa,MAIpEiB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAkDSC,MAAA;MAlDD,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAmCO,CAnCPhB,YAAA,CAmCOC,MAAA;QAnCAgB,IAAI,EAAE;MAAC;0BACZ,MAiCS,CAjCTjB,YAAA,CAiCSC,MAAA;UAjCAY,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACrF,MA4BiB,CA5BjBnB,YAAA,CA4BiBC,MAAA;YA3Bf,iBAAe,EAAC,KAAK;YACpBmB,MAAM,EAAE;;8BAET,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAI;gCAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;gBAEvBtB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAAgE,CAAhErB,YAAA,CAAgEC,MAAA;gBAAxDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAGT,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;;;gBAE1Dd,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEgB,SAAS,kB;;gBAE7CzB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAQ;gCACjC,MAAiD,C,kCAA9CpB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,WAAW,kB;;gBAE/C1B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAS;gCAClC,MAAiC,C,kCAA9BpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,WAAW,iB;;gBAEhC3B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC9B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;gBAAvDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;gBAEzDd,YAAA,CAKqBC,MAAA;cALAoB,KAAK,EAAC,MAAM;cAAEJ,IAAI,EAAE;;gCACxC,MAGE,CAHFb,mBAAA,CAGE,OAHFwB,UAGE,GAFAxB,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,KAAK,kBAC7B7B,YAAA,CAAwDC,MAAA;gBAA9C6B,IAAI,EAAJ,EAAI;gBAAClB,IAAI,EAAC,SAAS;gBAACW,IAAI,EAAC;;kCAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;cAInDd,YAAA,CAEUC,MAAA;YAFAW,IAAI,EAAC,SAAS;YAACmB,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAAlB,MAAA,QAAAA,MAAA,MAAAmB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YAAepB,KAAyB,EAAzB;cAAA;YAAA;;8BAA0B,MAE7FF,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;;;UAIJd,YAAA,CAWOC,MAAA;QAXAgB,IAAI,EAAE;MAAC;0BACZ,MASM,CATNb,mBAAA,CASM,OATNiC,UASM,GARJrC,YAAA,CAOGC,MAAA;UAPKU,KAAK,EAAC,wBAAwB;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7G,MAKS,CALTnB,YAAA,CAKSC,MAAA;YALA,cAAY,EAAE;UAAK;8BACb,MAAyB,E,kBAAtCH,mBAAA,CAEcwC,SAAA,QAAAC,WAAA,CAFatC,MAAA,CAAAuC,UAAU,EAAjBC,GAAG;mCAAvBC,YAAA,CAEczC,MAAA;gBAF0B0C,GAAG,EAAEF,GAAG,CAACnB,EAAE;gBAAEzB,KAAK,EAAC;;kCACxD,MAAc,C,kCAAX4C,GAAG,CAACG,IAAI,iB;;;6CAEG3C,MAAA,CAAAuC,UAAU,IAAIvC,MAAA,CAAAuC,UAAU,CAACK,MAAM,U,cAA/CH,YAAA,CAA4EzC,MAAA;;cAArB6C,WAAW,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}