{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { NCard, NButton, NAlert, NIcon, NAvatar, NModal, NTag, NSpin, useMessage } from 'naive-ui';\nimport { ShieldOutline, ShieldCheckmarkOutline, Checkmark, LogoAlipay, LogoWechat, CardOutline, WalletOutline, CheckmarkCircleOutline, CloseCircleOutline, ArrowForward } from '@vicons/ionicons5';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nexport default {\n  __name: 'Verification',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n    const message = useMessage();\n    const userStore = useUserStore();\n\n    // 步骤状态\n    const currentStep = ref(0);\n    const stepStatus = ref('process');\n\n    // 表单数据\n    const formRef = ref(null);\n    const formValue = ref({\n      realName: '',\n      idNumber: ''\n    });\n\n    // 表单验证规则\n    const rules = {\n      realName: {\n        required: true,\n        message: '请输入真实姓名',\n        trigger: ['blur', 'input']\n      },\n      idNumber: [{\n        required: true,\n        message: '请输入身份证号码',\n        trigger: ['blur', 'input']\n      }, {\n        validator(rule, value) {\n          // 简单的身份证号码验证，实际项目中应使用更严格的验证\n          const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\n          if (!reg.test(value)) {\n            return new Error('请输入正确的身份证号码');\n          }\n          return true;\n        },\n        trigger: ['blur', 'input']\n      }]\n    };\n\n    // 上传文件列表\n    const frontIdCardFiles = ref([]);\n    const backIdCardFiles = ref([]);\n    const canProceed = computed(() => frontIdCardFiles.value.length > 0 && backIdCardFiles.value.length > 0);\n\n    // 人脸验证状态\n    const faceVerified = ref(false);\n\n    // 处理上传\n    const handleFrontUpload = options => {\n      frontIdCardFiles.value = options.fileList;\n    };\n    const handleBackUpload = options => {\n      backIdCardFiles.value = options.fileList;\n    };\n\n    // 开始人脸验证\n    const startFaceVerification = () => {\n      // 实际项目中应调用人脸识别API\n      message.info('正在调用人脸识别服务...');\n      setTimeout(() => {\n        faceVerified.value = true;\n        message.success('人脸验证通过');\n      }, 2000);\n    };\n\n    // 下一步\n    const nextStep = async () => {\n      if (currentStep.value === 0) {\n        try {\n          await formRef.value?.validate();\n          currentStep.value++;\n        } catch (errors) {\n          console.error(errors);\n        }\n      } else if (currentStep.value === 1) {\n        if (!canProceed.value) {\n          message.warning('请上传身份证正反面照片');\n          return;\n        }\n        currentStep.value++;\n      } else if (currentStep.value === 2) {\n        if (!faceVerified.value) {\n          message.warning('请完成人脸验证');\n          return;\n        }\n        currentStep.value++;\n        stepStatus.value = 'finish';\n      }\n    };\n\n    // 上一步\n    const prevStep = () => {\n      if (currentStep.value > 0) {\n        currentStep.value--;\n      }\n    };\n\n    // 返回仪表盘\n    const goToDashboard = () => {\n      router.push('/dashboard');\n    };\n    const __returned__ = {\n      router,\n      message,\n      userStore,\n      currentStep,\n      stepStatus,\n      formRef,\n      formValue,\n      rules,\n      frontIdCardFiles,\n      backIdCardFiles,\n      canProceed,\n      faceVerified,\n      handleFrontUpload,\n      handleBackUpload,\n      startFaceVerification,\n      nextStep,\n      prevStep,\n      goToDashboard,\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NAvatar() {\n        return NAvatar;\n      },\n      get NModal() {\n        return NModal;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NSpin() {\n        return NSpin;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get ShieldOutline() {\n        return ShieldOutline;\n      },\n      get ShieldCheckmarkOutline() {\n        return ShieldCheckmarkOutline;\n      },\n      get Checkmark() {\n        return Checkmark;\n      },\n      get LogoAlipay() {\n        return LogoAlipay;\n      },\n      get LogoWechat() {\n        return LogoWechat;\n      },\n      get CardOutline() {\n        return CardOutline;\n      },\n      get WalletOutline() {\n        return WalletOutline;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get ArrowForward() {\n        return ArrowForward;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useRouter", "NCard", "NButton", "N<PERSON><PERSON><PERSON>", "NIcon", "NAvatar", "NModal", "NTag", "NSpin", "useMessage", "ShieldOutline", "ShieldCheckmarkOutline", "Checkmark", "LogoAlipay", "LogoWechat", "CardOutline", "WalletOutline", "CheckmarkCircleOutline", "CloseCircleOutline", "ArrowForward", "useUserStore", "getApiClient", "router", "message", "userStore", "currentStep", "step<PERSON>tatus", "formRef", "formValue", "realName", "idNumber", "rules", "required", "trigger", "validator", "rule", "value", "reg", "test", "Error", "frontIdCardFiles", "backIdCardFiles", "canProceed", "length", "faceVerified", "handleFrontUpload", "options", "fileList", "handleBackUpload", "startFaceVerification", "info", "setTimeout", "success", "nextStep", "validate", "errors", "console", "error", "warning", "prevStep", "goToDashboard", "push"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Verification.vue"], "sourcesContent": ["<template>\r\n  <div class=\"verification-container\">\r\n    <h1 class=\"page-title\">实名认证</h1>\r\n\r\n    <!-- 认证状态概览 -->\r\n    <div class=\"verification-overview\">\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level1Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level1Completed ? '#18a058' : '#2080f0'\">\r\n            <shield-checkmark-outline v-if=\"level1Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>一级认证</h3>\r\n          <n-tag v-if=\"level1Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else type=\"info\" size=\"small\">未完成</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过支付宝或微信实名验证，快速完成身份认证</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>基础身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问基础功能</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level1Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel1Verification\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else disabled>已完成</n-button>\r\n        </div>\r\n      </n-card>\r\n\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level2Completed, 'disabled': !level1Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level2Completed ? '#18a058' : (level1Completed ? '#2080f0' : '#d0d0d0')\">\r\n            <shield-checkmark-outline v-if=\"level2Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>二级认证</h3>\r\n          <n-tag v-if=\"level2Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else-if=\"level1Completed\" type=\"warning\" size=\"small\">可进行</n-tag>\r\n          <n-tag v-else type=\"default\" size=\"small\">需完成一级认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过人脸识别进行高级身份验证，免费使用</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>高级身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问所有功能</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>免费使用</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level2Completed && level1Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else-if=\"level2Completed\" disabled>已完成</n-button>\r\n          <n-button v-else disabled>需完成一级认证</n-button>\r\n        </div>\r\n      </n-card>\r\n    </div>\r\n\r\n    <!-- 一级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel1Modal\" preset=\"card\" title=\"一级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level1-content\">\r\n        <n-alert title=\"认证说明\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。\r\n        </n-alert>\r\n\r\n        <div class=\"payment-options\">\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'alipay' }\"\r\n            @click=\"selectedPayment = 'alipay'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#1677ff\">\r\n                <logo-alipay />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>支付宝认证</h4>\r\n              <p>通过支付宝实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.2</span>\r\n                <span class=\"original-price\">¥2.0</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-badge\">\r\n              <n-tag type=\"success\" size=\"small\">推荐</n-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'wechat' }\"\r\n            @click=\"selectedPayment = 'wechat'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#07c160\">\r\n                <logo-wechat />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>微信认证</h4>\r\n              <p>通过微信实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.5</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"verification-process\">\r\n          <h4>认证流程：</h4>\r\n          <div class=\"process-steps\">\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><card-outline /></n-icon>\r\n              <span>选择支付方式</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><wallet-outline /></n-icon>\r\n              <span>完成支付</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><checkmark-circle-outline /></n-icon>\r\n              <span>认证完成</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel1Modal = false\">取消</n-button>\r\n          <n-button\r\n            type=\"primary\"\r\n            @click=\"proceedLevel1Payment\"\r\n            :disabled=\"!selectedPayment\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 二级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel2Modal\" preset=\"card\" title=\"二级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level2-content\">\r\n        <n-alert title=\"人脸识别认证\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          二级认证完全免费，通过人脸识别技术验证您的身份信息。\r\n        </n-alert>\r\n\r\n        <div class=\"face-verification-area\" v-if=\"!faceVerificationStarted\">\r\n          <div class=\"face-preview\">\r\n            <n-avatar size=\"120\" :src=\"userStore.user?.avatar || '/default-avatar.png'\" />\r\n          </div>\r\n          <div class=\"face-instructions\">\r\n            <h4>人脸识别说明：</h4>\r\n            <ul>\r\n              <li>请确保光线充足，面部清晰可见</li>\r\n              <li>请正对摄像头，保持面部居中</li>\r\n              <li>请勿佩戴帽子、墨镜等遮挡物</li>\r\n              <li>整个过程大约需要3-5秒</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"face-verification-camera\" v-else>\r\n          <div class=\"camera-container\">\r\n            <video ref=\"videoRef\" autoplay muted playsinline></video>\r\n            <canvas ref=\"canvasRef\" style=\"display: none;\"></canvas>\r\n            <div class=\"camera-overlay\">\r\n              <div class=\"face-frame\"></div>\r\n            </div>\r\n          </div>\r\n          <div class=\"verification-status\">\r\n            <n-spin v-if=\"faceVerifying\" size=\"small\">\r\n              <template #description>正在进行人脸识别...</template>\r\n            </n-spin>\r\n            <div v-else-if=\"faceVerificationResult\" class=\"verification-result\">\r\n              <n-icon size=\"24\" :color=\"faceVerificationResult.success ? '#18a058' : '#d03050'\">\r\n                <checkmark-circle-outline v-if=\"faceVerificationResult.success\" />\r\n                <close-circle-outline v-else />\r\n              </n-icon>\r\n              <span>{{ faceVerificationResult.message }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel2Modal = false\" :disabled=\"faceVerifying\">取消</n-button>\r\n          <n-button\r\n            v-if=\"!faceVerificationStarted\"\r\n            type=\"primary\"\r\n            @click=\"startFaceVerification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始人脸识别\r\n          </n-button>\r\n          <n-button\r\n            v-else-if=\"faceVerificationResult?.success\"\r\n            type=\"primary\"\r\n            @click=\"completeFaceVerification\"\r\n          >\r\n            完成认证\r\n          </n-button>\r\n          <n-button\r\n            v-else-if=\"faceVerificationResult && !faceVerificationResult.success\"\r\n            type=\"primary\"\r\n            @click=\"retryFaceVerification\"\r\n          >\r\n            重新识别\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport {\r\n  NCard,\r\n  NButton,\r\n  NAlert,\r\n  NIcon,\r\n  NAvatar,\r\n  NModal,\r\n  NTag,\r\n  NSpin,\r\n  useMessage\r\n} from 'naive-ui';\r\nimport {\r\n  ShieldOutline,\r\n  ShieldCheckmarkOutline,\r\n  Checkmark,\r\n  LogoAlipay,\r\n  LogoWechat,\r\n  CardOutline,\r\n  WalletOutline,\r\n  CheckmarkCircleOutline,\r\n  CloseCircleOutline,\r\n  ArrowForward\r\n} from '@vicons/ionicons5';\r\nimport { useUserStore } from '../stores/user';\r\nimport { getApiClient } from '../utils/api';\r\n\r\nconst router = useRouter();\r\nconst message = useMessage();\r\nconst userStore = useUserStore();\r\n\r\n// 步骤状态\r\nconst currentStep = ref(0);\r\nconst stepStatus = ref('process');\r\n\r\n// 表单数据\r\nconst formRef = ref(null);\r\nconst formValue = ref({\r\n  realName: '',\r\n  idNumber: ''\r\n});\r\n\r\n// 表单验证规则\r\nconst rules = {\r\n  realName: {\r\n    required: true,\r\n    message: '请输入真实姓名',\r\n    trigger: ['blur', 'input']\r\n  },\r\n  idNumber: [\r\n    {\r\n      required: true,\r\n      message: '请输入身份证号码',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      validator(rule, value) {\r\n        // 简单的身份证号码验证，实际项目中应使用更严格的验证\r\n        const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n        if (!reg.test(value)) {\r\n          return new Error('请输入正确的身份证号码');\r\n        }\r\n        return true;\r\n      },\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ]\r\n};\r\n\r\n// 上传文件列表\r\nconst frontIdCardFiles = ref([]);\r\nconst backIdCardFiles = ref([]);\r\nconst canProceed = computed(() => frontIdCardFiles.value.length > 0 && backIdCardFiles.value.length > 0);\r\n\r\n// 人脸验证状态\r\nconst faceVerified = ref(false);\r\n\r\n// 处理上传\r\nconst handleFrontUpload = (options) => {\r\n  frontIdCardFiles.value = options.fileList;\r\n};\r\n\r\nconst handleBackUpload = (options) => {\r\n  backIdCardFiles.value = options.fileList;\r\n};\r\n\r\n// 开始人脸验证\r\nconst startFaceVerification = () => {\r\n  // 实际项目中应调用人脸识别API\r\n  message.info('正在调用人脸识别服务...');\r\n  setTimeout(() => {\r\n    faceVerified.value = true;\r\n    message.success('人脸验证通过');\r\n  }, 2000);\r\n};\r\n\r\n// 下一步\r\nconst nextStep = async () => {\r\n  if (currentStep.value === 0) {\r\n    try {\r\n      await formRef.value?.validate();\r\n      currentStep.value++;\r\n    } catch (errors) {\r\n      console.error(errors);\r\n    }\r\n  } else if (currentStep.value === 1) {\r\n    if (!canProceed.value) {\r\n      message.warning('请上传身份证正反面照片');\r\n      return;\r\n    }\r\n    currentStep.value++;\r\n  } else if (currentStep.value === 2) {\r\n    if (!faceVerified.value) {\r\n      message.warning('请完成人脸验证');\r\n      return;\r\n    }\r\n    currentStep.value++;\r\n    stepStatus.value = 'finish';\r\n  }\r\n};\r\n\r\n// 上一步\r\nconst prevStep = () => {\r\n  if (currentStep.value > 0) {\r\n    currentStep.value--;\r\n  }\r\n};\r\n\r\n// 返回仪表盘\r\nconst goToDashboard = () => {\r\n  router.push('/dashboard');\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.verification-container {\r\n  padding: 16px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 24px;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.step-content {\r\n  margin-top: 32px;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.id-upload {\r\n  flex: 1;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: var(--n-text-color-3);\r\n  margin-top: 8px;\r\n}\r\n\r\n.step-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 24px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .upload-area {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n</style> "], "mappings": ";AAyPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAC3D,SAASC,SAAS,QAAQ,YAAY;AACtC,SACEC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,UAAU,QACL,UAAU;AACjB,SACEC,aAAa,EACbC,sBAAsB,EACtBC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,sBAAsB,EACtBC,kBAAkB,EAClBC,YAAY,QACP,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,MAAM,GAAGtB,SAAS,CAAC,CAAC;IAC1B,MAAMuB,OAAO,GAAGd,UAAU,CAAC,CAAC;IAC5B,MAAMe,SAAS,GAAGJ,YAAY,CAAC,CAAC;;IAEhC;IACA,MAAMK,WAAW,GAAG7B,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM8B,UAAU,GAAG9B,GAAG,CAAC,SAAS,CAAC;;IAEjC;IACA,MAAM+B,OAAO,GAAG/B,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMgC,SAAS,GAAGhC,GAAG,CAAC;MACpBiC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;;IAEF;IACA,MAAMC,KAAK,GAAG;MACZF,QAAQ,EAAE;QACRG,QAAQ,EAAE,IAAI;QACdT,OAAO,EAAE,SAAS;QAClBU,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC;MACDH,QAAQ,EAAE,CACR;QACEE,QAAQ,EAAE,IAAI;QACdT,OAAO,EAAE,UAAU;QACnBU,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,EACD;QACEC,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;UACrB;UACA,MAAMC,GAAG,GAAG,0CAA0C;UACtD,IAAI,CAACA,GAAG,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE;YACpB,OAAO,IAAIG,KAAK,CAAC,aAAa,CAAC;UACjC;UACA,OAAO,IAAI;QACb,CAAC;QACDN,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC;IAEL,CAAC;;IAED;IACA,MAAMO,gBAAgB,GAAG5C,GAAG,CAAC,EAAE,CAAC;IAChC,MAAM6C,eAAe,GAAG7C,GAAG,CAAC,EAAE,CAAC;IAC/B,MAAM8C,UAAU,GAAG7C,QAAQ,CAAC,MAAM2C,gBAAgB,CAACJ,KAAK,CAACO,MAAM,GAAG,CAAC,IAAIF,eAAe,CAACL,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;;IAExG;IACA,MAAMC,YAAY,GAAGhD,GAAG,CAAC,KAAK,CAAC;;IAE/B;IACA,MAAMiD,iBAAiB,GAAIC,OAAO,IAAK;MACrCN,gBAAgB,CAACJ,KAAK,GAAGU,OAAO,CAACC,QAAQ;IAC3C,CAAC;IAED,MAAMC,gBAAgB,GAAIF,OAAO,IAAK;MACpCL,eAAe,CAACL,KAAK,GAAGU,OAAO,CAACC,QAAQ;IAC1C,CAAC;;IAED;IACA,MAAME,qBAAqB,GAAGA,CAAA,KAAM;MAClC;MACA1B,OAAO,CAAC2B,IAAI,CAAC,eAAe,CAAC;MAC7BC,UAAU,CAAC,MAAM;QACfP,YAAY,CAACR,KAAK,GAAG,IAAI;QACzBb,OAAO,CAAC6B,OAAO,CAAC,QAAQ,CAAC;MAC3B,CAAC,EAAE,IAAI,CAAC;IACV,CAAC;;IAED;IACA,MAAMC,QAAQ,GAAG,MAAAA,CAAA,KAAY;MAC3B,IAAI5B,WAAW,CAACW,KAAK,KAAK,CAAC,EAAE;QAC3B,IAAI;UACF,MAAMT,OAAO,CAACS,KAAK,EAAEkB,QAAQ,CAAC,CAAC;UAC/B7B,WAAW,CAACW,KAAK,EAAE;QACrB,CAAC,CAAC,OAAOmB,MAAM,EAAE;UACfC,OAAO,CAACC,KAAK,CAACF,MAAM,CAAC;QACvB;MACF,CAAC,MAAM,IAAI9B,WAAW,CAACW,KAAK,KAAK,CAAC,EAAE;QAClC,IAAI,CAACM,UAAU,CAACN,KAAK,EAAE;UACrBb,OAAO,CAACmC,OAAO,CAAC,aAAa,CAAC;UAC9B;QACF;QACAjC,WAAW,CAACW,KAAK,EAAE;MACrB,CAAC,MAAM,IAAIX,WAAW,CAACW,KAAK,KAAK,CAAC,EAAE;QAClC,IAAI,CAACQ,YAAY,CAACR,KAAK,EAAE;UACvBb,OAAO,CAACmC,OAAO,CAAC,SAAS,CAAC;UAC1B;QACF;QACAjC,WAAW,CAACW,KAAK,EAAE;QACnBV,UAAU,CAACU,KAAK,GAAG,QAAQ;MAC7B;IACF,CAAC;;IAED;IACA,MAAMuB,QAAQ,GAAGA,CAAA,KAAM;MACrB,IAAIlC,WAAW,CAACW,KAAK,GAAG,CAAC,EAAE;QACzBX,WAAW,CAACW,KAAK,EAAE;MACrB;IACF,CAAC;;IAED;IACA,MAAMwB,aAAa,GAAGA,CAAA,KAAM;MAC1BtC,MAAM,CAACuC,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}