/**
 * 阿里云API配置
 */

const config = require('./config');

// 阿里云二要素验证API配置
const ALIYUN_ID_VERIFICATION = {
  appKey: '*********',
  appSecret: 'l77vwNhbVtax65GRQz9NnnOxxREz5AZS',
  appCode: 'ac2c8231f12445928b757dd27e67dbce',
  // 正确的API URL（基于CSDN博客示例）
  apiUrl: 'https://idcert.market.alicloudapi.com/idcard',
  method: 'GET', // 使用GET方法，参数在URL中
  timeout: 15000, // 15秒超时
  retryCount: 2   // 重试次数
};

// 获取API配置
const getIdVerificationConfig = () => {
  return {
    ...ALIYUN_ID_VERIFICATION,
    // 在生产环境中可以从环境变量覆盖配置
    appKey: process.env.ALIYUN_APP_KEY || ALIYUN_ID_VERIFICATION.appKey,
    appSecret: process.env.ALIYUN_APP_SECRET || ALIYUN_ID_VERIFICATION.appSecret,
    appCode: process.env.ALIYUN_APP_CODE || ALIYUN_ID_VERIFICATION.appCode
  };
};

// 检查API配置是否完整
const validateConfig = () => {
  const config = getIdVerificationConfig();
  const requiredFields = ['appKey', 'appSecret', 'appCode', 'apiUrl'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`阿里云API配置缺少必要字段: ${field}`);
    }
  }
  
  return true;
};

// 是否使用真实API（生产环境或设置了环境变量）
const shouldUseRealAPI = () => {
  return config.server.env === 'production' || 
         process.env.USE_REAL_API === 'true' ||
         process.env.NODE_ENV === 'production';
};

module.exports = {
  getIdVerificationConfig,
  validateConfig,
  shouldUseRealAPI,
  ALIYUN_ID_VERIFICATION
};
