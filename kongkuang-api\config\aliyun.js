/**
 * 阿里云API配置
 */

const config = require('./config');

// 从config.toml读取阿里云API配置
const getAliyunConfigFromToml = () => {
  try {
    return {
      appKey: config.aliyun_api?.app_key || '*********',
      appSecret: config.aliyun_api?.app_secret || 'l77vwNhbVtax65GRQz9NnnOxxREz5AZS',
      appCode: config.aliyun_api?.app_code || 'ac2c8231f12445928b757dd27e67dbce',
      apiUrl: config.aliyun_api?.id_verification_url || 'https://idcert.market.alicloudapi.com/idcard',
      timeout: config.aliyun_api?.timeout || 15000,
      useRealApi: config.aliyun_api?.use_real_api || false,
      method: 'GET',
      retryCount: 2
    };
  } catch (error) {
    console.warn('读取阿里云配置失败，使用默认配置:', error.message);
    return {
      appKey: '*********',
      appSecret: 'l77vwNhbVtax65GRQz9NnnOxxREz5AZS',
      appCode: 'ac2c8231f12445928b757dd27e67dbce',
      apiUrl: 'https://idcert.market.alicloudapi.com/idcard',
      timeout: 15000,
      useRealApi: false,
      method: 'GET',
      retryCount: 2
    };
  }
};

// 获取API配置
const getIdVerificationConfig = () => {
  const tomlConfig = getAliyunConfigFromToml();

  return {
    ...tomlConfig,
    // 环境变量可以覆盖配置文件
    appKey: process.env.ALIYUN_APP_KEY || tomlConfig.appKey,
    appSecret: process.env.ALIYUN_APP_SECRET || tomlConfig.appSecret,
    appCode: process.env.ALIYUN_APP_CODE || tomlConfig.appCode,
    apiUrl: process.env.ALIYUN_API_URL || tomlConfig.apiUrl
  };
};

// 检查API配置是否完整
const validateConfig = () => {
  const config = getIdVerificationConfig();
  const requiredFields = ['appKey', 'appSecret', 'appCode', 'apiUrl'];
  
  for (const field of requiredFields) {
    if (!config[field]) {
      throw new Error(`阿里云API配置缺少必要字段: ${field}`);
    }
  }
  
  return true;
};

// 是否使用真实API
const shouldUseRealAPI = () => {
  const tomlConfig = getAliyunConfigFromToml();

  return config.server.env === 'production' ||
         process.env.USE_REAL_API === 'true' ||
         process.env.NODE_ENV === 'production' ||
         tomlConfig.useRealApi === true;
};

module.exports = {
  getIdVerificationConfig,
  validateConfig,
  shouldUseRealAPI,
  getAliyunConfigFromToml
};
