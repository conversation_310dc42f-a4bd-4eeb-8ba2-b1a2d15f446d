{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { NCard, NButton, NAlert, NIcon, NAvatar, NModal, NTag, NSpin, NForm, NFormItem, NInput, useMessage } from 'naive-ui';\nimport { ShieldOutline, ShieldCheckmarkOutline, Checkmark, LogoAlipay, LogoWechat, CardOutline, WalletOutline, CheckmarkCircleOutline, CloseCircleOutline, ArrowForward, Person } from '@vicons/ionicons5';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nexport default {\n  __name: 'Verification',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n    const message = useMessage();\n    const userStore = useUserStore();\n    const apiClient = getApiClient();\n\n    // 认证状态\n    const level1Completed = ref(false);\n    const level2Completed = ref(false);\n    const level1Loading = ref(false);\n    const level2Loading = ref(false);\n\n    // 模态框状态\n    const showLevel1Modal = ref(false);\n    const showLevel2Modal = ref(false);\n\n    // 一级认证相关\n    const selectedPayment = ref('');\n\n    // 二级认证相关\n    const level2FormRef = ref(null);\n    const level2Form = ref({\n      realName: '',\n      idNumber: ''\n    });\n    const level2VerificationResult = ref(null);\n\n    // 二级认证表单验证规则\n    const level2Rules = {\n      realName: [{\n        required: true,\n        message: '请输入真实姓名',\n        trigger: ['blur', 'input']\n      }, {\n        min: 2,\n        max: 20,\n        message: '姓名长度应在2-20个字符之间',\n        trigger: ['blur', 'input']\n      }],\n      idNumber: [{\n        required: true,\n        message: '请输入身份证号码',\n        trigger: ['blur', 'input']\n      }, {\n        validator(rule, value) {\n          const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\n          if (!reg.test(value)) {\n            return new Error('请输入正确的身份证号码');\n          }\n          return true;\n        },\n        trigger: ['blur', 'input']\n      }]\n    };\n\n    // 表单验证状态\n    const isLevel2FormValid = computed(() => {\n      return level2Form.value.realName.trim() && level2Form.value.idNumber.trim() && /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(level2Form.value.idNumber);\n    });\n\n    // 检查认证状态\n    const checkVerificationStatus = async () => {\n      try {\n        const response = await apiClient.get('/users/verification-status');\n        level1Completed.value = response.data.level1Completed || false;\n        level2Completed.value = response.data.level2Completed || false;\n      } catch (error) {\n        console.error('获取认证状态失败:', error);\n      }\n    };\n\n    // 开始一级认证\n    const startLevel1Verification = () => {\n      showLevel1Modal.value = true;\n      selectedPayment.value = 'alipay'; // 默认选择支付宝\n    };\n\n    // 处理一级认证支付\n    const proceedLevel1Payment = async () => {\n      if (!selectedPayment.value) {\n        message.warning('请选择支付方式');\n        return;\n      }\n      level1Loading.value = true;\n      try {\n        // 调用支付API\n        const response = await apiClient.post('/verification/level1/payment', {\n          paymentMethod: selectedPayment.value,\n          amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5\n        });\n        if (response.data.success) {\n          // 跳转到支付页面或处理支付\n          window.open(response.data.paymentUrl, '_blank');\n          message.success('支付链接已打开，请完成支付');\n          showLevel1Modal.value = false;\n\n          // 轮询检查支付状态\n          checkPaymentStatus(response.data.orderId);\n        }\n      } catch (error) {\n        message.error(error.response?.data?.message || '发起支付失败');\n      } finally {\n        level1Loading.value = false;\n      }\n    };\n\n    // 检查支付状态\n    const checkPaymentStatus = async orderId => {\n      const maxAttempts = 30; // 最多检查30次，每次间隔2秒\n      let attempts = 0;\n      const checkStatus = async () => {\n        try {\n          const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);\n          if (response.data.status === 'completed') {\n            level1Completed.value = true;\n            message.success('一级认证完成！');\n            return;\n          } else if (response.data.status === 'failed') {\n            message.error('支付失败，请重试');\n            return;\n          }\n          attempts++;\n          if (attempts < maxAttempts) {\n            setTimeout(checkStatus, 2000);\n          }\n        } catch (error) {\n          console.error('检查支付状态失败:', error);\n        }\n      };\n      checkStatus();\n    };\n\n    // 开始二级认证\n    const startLevel2Verification = () => {\n      showLevel2Modal.value = true;\n      level2VerificationResult.value = null;\n      // 重置表单\n      level2Form.value = {\n        realName: '',\n        idNumber: ''\n      };\n    };\n\n    // 提交二级认证\n    const submitLevel2Verification = async () => {\n      try {\n        // 验证表单\n        await level2FormRef.value?.validate();\n        level2Loading.value = true;\n        level2VerificationResult.value = null;\n        const response = await apiClient.post('/verification/level2/verify', {\n          realName: level2Form.value.realName.trim(),\n          idNumber: level2Form.value.idNumber.trim()\n        });\n        if (response.data.success) {\n          level2VerificationResult.value = {\n            success: true,\n            message: response.data.message || '身份验证成功'\n          };\n          message.success('二级认证成功！');\n        } else {\n          level2VerificationResult.value = {\n            success: false,\n            message: response.data.message || '身份验证失败'\n          };\n        }\n      } catch (error) {\n        console.error('二级认证失败:', error);\n        level2VerificationResult.value = {\n          success: false,\n          message: error.response?.data?.message || '身份验证失败，请稍后重试'\n        };\n        message.error(level2VerificationResult.value.message);\n      } finally {\n        level2Loading.value = false;\n      }\n    };\n\n    // 完成二级认证\n    const completeLevel2Verification = () => {\n      level2Completed.value = true;\n      showLevel2Modal.value = false;\n      message.success('二级认证已完成！');\n    };\n\n    // 组件挂载时检查认证状态\n    onMounted(() => {\n      checkVerificationStatus();\n    });\n    const __returned__ = {\n      router,\n      message,\n      userStore,\n      apiClient,\n      level1Completed,\n      level2Completed,\n      level1Loading,\n      level2Loading,\n      showLevel1Modal,\n      showLevel2Modal,\n      selectedPayment,\n      level2FormRef,\n      level2Form,\n      level2VerificationResult,\n      level2Rules,\n      isLevel2FormValid,\n      checkVerificationStatus,\n      startLevel1Verification,\n      proceedLevel1Payment,\n      checkPaymentStatus,\n      startLevel2Verification,\n      submitLevel2Verification,\n      completeLevel2Verification,\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NAvatar() {\n        return NAvatar;\n      },\n      get NModal() {\n        return NModal;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NSpin() {\n        return NSpin;\n      },\n      get NForm() {\n        return NForm;\n      },\n      get NFormItem() {\n        return NFormItem;\n      },\n      get NInput() {\n        return NInput;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get ShieldOutline() {\n        return ShieldOutline;\n      },\n      get ShieldCheckmarkOutline() {\n        return ShieldCheckmarkOutline;\n      },\n      get Checkmark() {\n        return Checkmark;\n      },\n      get LogoAlipay() {\n        return LogoAlipay;\n      },\n      get LogoWechat() {\n        return LogoWechat;\n      },\n      get CardOutline() {\n        return CardOutline;\n      },\n      get WalletOutline() {\n        return WalletOutline;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get ArrowForward() {\n        return ArrowForward;\n      },\n      get Person() {\n        return Person;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useRouter", "NCard", "NButton", "N<PERSON><PERSON><PERSON>", "NIcon", "NAvatar", "NModal", "NTag", "NSpin", "NForm", "NFormItem", "NInput", "useMessage", "ShieldOutline", "ShieldCheckmarkOutline", "Checkmark", "LogoAlipay", "LogoWechat", "CardOutline", "WalletOutline", "CheckmarkCircleOutline", "CloseCircleOutline", "ArrowForward", "Person", "useUserStore", "getApiClient", "router", "message", "userStore", "apiClient", "level1Completed", "level2Completed", "level1Loading", "level2Loading", "showLevel1Modal", "showLevel2Modal", "selectedPayment", "level2FormRef", "level2Form", "realName", "idNumber", "level2VerificationResult", "level2Rules", "required", "trigger", "min", "max", "validator", "rule", "value", "reg", "test", "Error", "isLevel2FormValid", "trim", "checkVerificationStatus", "response", "get", "data", "error", "console", "startLevel1Verification", "proceedLevel1Payment", "warning", "post", "paymentMethod", "amount", "success", "window", "open", "paymentUrl", "checkPaymentStatus", "orderId", "maxAttempts", "attempts", "checkStatus", "status", "setTimeout", "startLevel2Verification", "submitLevel2Verification", "validate", "completeLevel2Verification"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Verification.vue"], "sourcesContent": ["<template>\r\n  <div class=\"verification-container\">\r\n    <h1 class=\"page-title\">实名认证</h1>\r\n\r\n    <!-- 认证状态概览 -->\r\n    <div class=\"verification-overview\">\r\n      <n-card class=\"level-card primary-card\" :class=\"{ 'completed': level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level2Completed ? '#18a058' : '#2080f0'\">\r\n            <shield-checkmark-outline v-if=\"level2Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>二级认证</h3>\r\n          <n-tag v-if=\"level2Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else type=\"info\" size=\"small\">免费认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过姓名和身份证号进行二要素验证，完全免费</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>二要素身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问所有功能</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>完全免费</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else disabled>已完成</n-button>\r\n        </div>\r\n      </n-card>\r\n\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level1Completed, 'disabled': !level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level1Completed ? '#18a058' : (level2Completed ? '#2080f0' : '#d0d0d0')\">\r\n            <shield-checkmark-outline v-if=\"level1Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>一级认证</h3>\r\n          <n-tag v-if=\"level1Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else-if=\"level2Completed\" type=\"warning\" size=\"small\">可进行</n-tag>\r\n          <n-tag v-else type=\"default\" size=\"small\">需完成二级认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过支付宝或微信实名验证，获得更高信任度</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>支付平台验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>更高信任等级</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level1Completed && level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel1Verification\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else-if=\"level1Completed\" disabled>已完成</n-button>\r\n          <n-button v-else disabled>需完成二级认证</n-button>\r\n        </div>\r\n      </n-card>\r\n    </div>\r\n\r\n    <!-- 一级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel1Modal\" preset=\"card\" title=\"一级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level1-content\">\r\n        <n-alert title=\"认证说明\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。\r\n        </n-alert>\r\n\r\n        <div class=\"payment-options\">\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'alipay' }\"\r\n            @click=\"selectedPayment = 'alipay'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#1677ff\">\r\n                <logo-alipay />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>支付宝认证</h4>\r\n              <p>通过支付宝实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.2</span>\r\n                <span class=\"original-price\">¥2.0</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-badge\">\r\n              <n-tag type=\"success\" size=\"small\">推荐</n-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'wechat' }\"\r\n            @click=\"selectedPayment = 'wechat'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#07c160\">\r\n                <logo-wechat />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>微信认证</h4>\r\n              <p>通过微信实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.5</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"verification-process\">\r\n          <h4>认证流程：</h4>\r\n          <div class=\"process-steps\">\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><card-outline /></n-icon>\r\n              <span>选择支付方式</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><wallet-outline /></n-icon>\r\n              <span>完成支付</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><checkmark-circle-outline /></n-icon>\r\n              <span>认证完成</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel1Modal = false\">取消</n-button>\r\n          <n-button\r\n            type=\"primary\"\r\n            @click=\"proceedLevel1Payment\"\r\n            :disabled=\"!selectedPayment\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 二级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel2Modal\" preset=\"card\" title=\"二级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level2-content\">\r\n        <n-alert title=\"二要素身份验证\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          二级认证完全免费，通过姓名和身份证号进行二要素验证。\r\n        </n-alert>\r\n\r\n        <n-form\r\n          ref=\"level2FormRef\"\r\n          :model=\"level2Form\"\r\n          :rules=\"level2Rules\"\r\n          label-placement=\"top\"\r\n          size=\"medium\"\r\n        >\r\n          <n-form-item label=\"真实姓名\" path=\"realName\">\r\n            <n-input\r\n              v-model:value=\"level2Form.realName\"\r\n              placeholder=\"请输入您的真实姓名\"\r\n              :disabled=\"level2Loading\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><person /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n\r\n          <n-form-item label=\"身份证号码\" path=\"idNumber\">\r\n            <n-input\r\n              v-model:value=\"level2Form.idNumber\"\r\n              placeholder=\"请输入您的身份证号码\"\r\n              :disabled=\"level2Loading\"\r\n              maxlength=\"18\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><card-outline /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n        </n-form>\r\n\r\n        <div class=\"verification-instructions\">\r\n          <h4>验证说明：</h4>\r\n          <ul>\r\n            <li>请确保输入的姓名与身份证上的姓名完全一致</li>\r\n            <li>身份证号码必须是18位有效号码</li>\r\n            <li>验证过程通过权威数据源进行核实</li>\r\n            <li>您的个人信息将被严格保密</li>\r\n          </ul>\r\n        </div>\r\n\r\n        <div v-if=\"level2VerificationResult\" class=\"verification-result-display\">\r\n          <n-alert\r\n            :title=\"level2VerificationResult.success ? '验证成功' : '验证失败'\"\r\n            :type=\"level2VerificationResult.success ? 'success' : 'error'\"\r\n          >\r\n            {{ level2VerificationResult.message }}\r\n          </n-alert>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel2Modal = false\" :disabled=\"level2Loading\">取消</n-button>\r\n          <n-button\r\n            v-if=\"!level2VerificationResult?.success\"\r\n            type=\"primary\"\r\n            @click=\"submitLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n            :disabled=\"!isLevel2FormValid\"\r\n          >\r\n            开始验证\r\n          </n-button>\r\n          <n-button\r\n            v-else\r\n            type=\"primary\"\r\n            @click=\"completeLevel2Verification\"\r\n          >\r\n            完成认证\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport {\r\n  NCard,\r\n  NButton,\r\n  NAlert,\r\n  NIcon,\r\n  NAvatar,\r\n  NModal,\r\n  NTag,\r\n  NSpin,\r\n  NForm,\r\n  NFormItem,\r\n  NInput,\r\n  useMessage\r\n} from 'naive-ui';\r\nimport {\r\n  ShieldOutline,\r\n  ShieldCheckmarkOutline,\r\n  Checkmark,\r\n  LogoAlipay,\r\n  LogoWechat,\r\n  CardOutline,\r\n  WalletOutline,\r\n  CheckmarkCircleOutline,\r\n  CloseCircleOutline,\r\n  ArrowForward,\r\n  Person\r\n} from '@vicons/ionicons5';\r\nimport { useUserStore } from '../stores/user';\r\nimport { getApiClient } from '../utils/api';\r\n\r\nconst router = useRouter();\r\nconst message = useMessage();\r\nconst userStore = useUserStore();\r\nconst apiClient = getApiClient();\r\n\r\n// 认证状态\r\nconst level1Completed = ref(false);\r\nconst level2Completed = ref(false);\r\nconst level1Loading = ref(false);\r\nconst level2Loading = ref(false);\r\n\r\n// 模态框状态\r\nconst showLevel1Modal = ref(false);\r\nconst showLevel2Modal = ref(false);\r\n\r\n// 一级认证相关\r\nconst selectedPayment = ref('');\r\n\r\n// 二级认证相关\r\nconst level2FormRef = ref(null);\r\nconst level2Form = ref({\r\n  realName: '',\r\n  idNumber: ''\r\n});\r\nconst level2VerificationResult = ref(null);\r\n\r\n// 二级认证表单验证规则\r\nconst level2Rules = {\r\n  realName: [\r\n    {\r\n      required: true,\r\n      message: '请输入真实姓名',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      min: 2,\r\n      max: 20,\r\n      message: '姓名长度应在2-20个字符之间',\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ],\r\n  idNumber: [\r\n    {\r\n      required: true,\r\n      message: '请输入身份证号码',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      validator(rule, value) {\r\n        const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n        if (!reg.test(value)) {\r\n          return new Error('请输入正确的身份证号码');\r\n        }\r\n        return true;\r\n      },\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ]\r\n};\r\n\r\n// 表单验证状态\r\nconst isLevel2FormValid = computed(() => {\r\n  return level2Form.value.realName.trim() &&\r\n         level2Form.value.idNumber.trim() &&\r\n         /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(level2Form.value.idNumber);\r\n});\r\n\r\n// 检查认证状态\r\nconst checkVerificationStatus = async () => {\r\n  try {\r\n    const response = await apiClient.get('/users/verification-status');\r\n    level1Completed.value = response.data.level1Completed || false;\r\n    level2Completed.value = response.data.level2Completed || false;\r\n  } catch (error) {\r\n    console.error('获取认证状态失败:', error);\r\n  }\r\n};\r\n\r\n// 开始一级认证\r\nconst startLevel1Verification = () => {\r\n  showLevel1Modal.value = true;\r\n  selectedPayment.value = 'alipay'; // 默认选择支付宝\r\n};\r\n\r\n// 处理一级认证支付\r\nconst proceedLevel1Payment = async () => {\r\n  if (!selectedPayment.value) {\r\n    message.warning('请选择支付方式');\r\n    return;\r\n  }\r\n\r\n  level1Loading.value = true;\r\n\r\n  try {\r\n    // 调用支付API\r\n    const response = await apiClient.post('/verification/level1/payment', {\r\n      paymentMethod: selectedPayment.value,\r\n      amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5\r\n    });\r\n\r\n    if (response.data.success) {\r\n      // 跳转到支付页面或处理支付\r\n      window.open(response.data.paymentUrl, '_blank');\r\n      message.success('支付链接已打开，请完成支付');\r\n      showLevel1Modal.value = false;\r\n\r\n      // 轮询检查支付状态\r\n      checkPaymentStatus(response.data.orderId);\r\n    }\r\n  } catch (error) {\r\n    message.error(error.response?.data?.message || '发起支付失败');\r\n  } finally {\r\n    level1Loading.value = false;\r\n  }\r\n};\r\n\r\n// 检查支付状态\r\nconst checkPaymentStatus = async (orderId) => {\r\n  const maxAttempts = 30; // 最多检查30次，每次间隔2秒\r\n  let attempts = 0;\r\n\r\n  const checkStatus = async () => {\r\n    try {\r\n      const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);\r\n\r\n      if (response.data.status === 'completed') {\r\n        level1Completed.value = true;\r\n        message.success('一级认证完成！');\r\n        return;\r\n      } else if (response.data.status === 'failed') {\r\n        message.error('支付失败，请重试');\r\n        return;\r\n      }\r\n\r\n      attempts++;\r\n      if (attempts < maxAttempts) {\r\n        setTimeout(checkStatus, 2000);\r\n      }\r\n    } catch (error) {\r\n      console.error('检查支付状态失败:', error);\r\n    }\r\n  };\r\n\r\n  checkStatus();\r\n};\r\n\r\n// 开始二级认证\r\nconst startLevel2Verification = () => {\r\n  showLevel2Modal.value = true;\r\n  level2VerificationResult.value = null;\r\n  // 重置表单\r\n  level2Form.value = {\r\n    realName: '',\r\n    idNumber: ''\r\n  };\r\n};\r\n\r\n// 提交二级认证\r\nconst submitLevel2Verification = async () => {\r\n  try {\r\n    // 验证表单\r\n    await level2FormRef.value?.validate();\r\n\r\n    level2Loading.value = true;\r\n    level2VerificationResult.value = null;\r\n\r\n    const response = await apiClient.post('/verification/level2/verify', {\r\n      realName: level2Form.value.realName.trim(),\r\n      idNumber: level2Form.value.idNumber.trim()\r\n    });\r\n\r\n    if (response.data.success) {\r\n      level2VerificationResult.value = {\r\n        success: true,\r\n        message: response.data.message || '身份验证成功'\r\n      };\r\n      message.success('二级认证成功！');\r\n    } else {\r\n      level2VerificationResult.value = {\r\n        success: false,\r\n        message: response.data.message || '身份验证失败'\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error('二级认证失败:', error);\r\n    level2VerificationResult.value = {\r\n      success: false,\r\n      message: error.response?.data?.message || '身份验证失败，请稍后重试'\r\n    };\r\n    message.error(level2VerificationResult.value.message);\r\n  } finally {\r\n    level2Loading.value = false;\r\n  }\r\n};\r\n\r\n// 完成二级认证\r\nconst completeLevel2Verification = () => {\r\n  level2Completed.value = true;\r\n  showLevel2Modal.value = false;\r\n  message.success('二级认证已完成！');\r\n};\r\n\r\n// 组件挂载时检查认证状态\r\nonMounted(() => {\r\n  checkVerificationStatus();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.verification-container {\r\n  padding: 16px;\r\n  min-height: 100%;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 24px;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-overview {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 24px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.level-card {\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.level-card:hover {\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.level-card.completed {\r\n  border: 2px solid #18a058;\r\n  background: linear-gradient(135deg, rgba(24, 160, 88, 0.05) 0%, rgba(24, 160, 88, 0.02) 100%);\r\n}\r\n\r\n.level-card.disabled {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.level-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.level-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n.level-description {\r\n  color: var(--n-text-color-2);\r\n  margin-bottom: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.level-features {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 一级认证模态框样式 */\r\n.level1-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-option {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border: 2px solid var(--n-border-color);\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.payment-option:hover {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-hover);\r\n}\r\n\r\n.payment-option.selected {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-suppl);\r\n}\r\n\r\n.payment-icon {\r\n  margin-right: 16px;\r\n}\r\n\r\n.payment-info {\r\n  flex: 1;\r\n}\r\n\r\n.payment-info h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.payment-info p {\r\n  margin: 0 0 8px 0;\r\n  color: var(--n-text-color-2);\r\n  font-size: 14px;\r\n}\r\n\r\n.payment-price {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.price {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: var(--n-error-color);\r\n}\r\n\r\n.original-price {\r\n  font-size: 14px;\r\n  color: var(--n-text-color-3);\r\n  text-decoration: line-through;\r\n}\r\n\r\n.payment-badge {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n}\r\n\r\n.verification-process {\r\n  margin-top: 24px;\r\n}\r\n\r\n.verification-process h4 {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.process-steps {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 16px;\r\n  background-color: var(--n-color-target);\r\n  border-radius: 8px;\r\n}\r\n\r\n.process-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n/* 二级认证模态框样式 */\r\n.level2-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.face-verification-area {\r\n  text-align: center;\r\n  padding: 24px;\r\n}\r\n\r\n.face-preview {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.face-instructions {\r\n  text-align: left;\r\n  max-width: 400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.face-instructions h4 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.face-instructions ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n}\r\n\r\n.face-instructions li {\r\n  margin-bottom: 8px;\r\n  color: var(--n-text-color-2);\r\n  line-height: 1.5;\r\n}\r\n\r\n.face-verification-camera {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  position: relative;\r\n  display: inline-block;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.camera-container video {\r\n  width: 480px;\r\n  height: 360px;\r\n  object-fit: cover;\r\n}\r\n\r\n.camera-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.face-frame {\r\n  width: 200px;\r\n  height: 240px;\r\n  border: 3px solid #2080f0;\r\n  border-radius: 50%;\r\n  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.verification-status {\r\n  margin-top: 16px;\r\n  min-height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.verification-result {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .verification-overview {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n\r\n  .payment-option {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .payment-icon {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .process-steps {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .camera-container video {\r\n    width: 320px;\r\n    height: 240px;\r\n  }\r\n\r\n  .face-frame {\r\n    width: 150px;\r\n    height: 180px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .verification-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .camera-container video {\r\n    width: 280px;\r\n    height: 210px;\r\n  }\r\n}\r\n</style>"], "mappings": "AAkQA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAC3D,SAASC,SAAS,QAAQ,YAAY;AACtC,SACEC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SACEC,aAAa,EACbC,sBAAsB,EACtBC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,sBAAsB,EACtBC,kBAAkB,EAClBC,YAAY,EACZC,MAAM,QACD,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,MAAM,GAAG1B,SAAS,CAAC,CAAC;IAC1B,MAAM2B,OAAO,GAAGf,UAAU,CAAC,CAAC;IAC5B,MAAMgB,SAAS,GAAGJ,YAAY,CAAC,CAAC;IAChC,MAAMK,SAAS,GAAGJ,YAAY,CAAC,CAAC;;IAEhC;IACA,MAAMK,eAAe,GAAGlC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMmC,eAAe,GAAGnC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMoC,aAAa,GAAGpC,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMqC,aAAa,GAAGrC,GAAG,CAAC,KAAK,CAAC;;IAEhC;IACA,MAAMsC,eAAe,GAAGtC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMuC,eAAe,GAAGvC,GAAG,CAAC,KAAK,CAAC;;IAElC;IACA,MAAMwC,eAAe,GAAGxC,GAAG,CAAC,EAAE,CAAC;;IAE/B;IACA,MAAMyC,aAAa,GAAGzC,GAAG,CAAC,IAAI,CAAC;IAC/B,MAAM0C,UAAU,GAAG1C,GAAG,CAAC;MACrB2C,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMC,wBAAwB,GAAG7C,GAAG,CAAC,IAAI,CAAC;;IAE1C;IACA,MAAM8C,WAAW,GAAG;MAClBH,QAAQ,EAAE,CACR;QACEI,QAAQ,EAAE,IAAI;QACdhB,OAAO,EAAE,SAAS;QAClBiB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,EACD;QACEC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,EAAE;QACPnB,OAAO,EAAE,iBAAiB;QAC1BiB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,CACF;MACDJ,QAAQ,EAAE,CACR;QACEG,QAAQ,EAAE,IAAI;QACdhB,OAAO,EAAE,UAAU;QACnBiB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,EACD;QACEG,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;UACrB,MAAMC,GAAG,GAAG,0CAA0C;UACtD,IAAI,CAACA,GAAG,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE;YACpB,OAAO,IAAIG,KAAK,CAAC,aAAa,CAAC;UACjC;UACA,OAAO,IAAI;QACb,CAAC;QACDR,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC;IAEL,CAAC;;IAED;IACA,MAAMS,iBAAiB,GAAGxD,QAAQ,CAAC,MAAM;MACvC,OAAOyC,UAAU,CAACW,KAAK,CAACV,QAAQ,CAACe,IAAI,CAAC,CAAC,IAChChB,UAAU,CAACW,KAAK,CAACT,QAAQ,CAACc,IAAI,CAAC,CAAC,IAChC,0CAA0C,CAACH,IAAI,CAACb,UAAU,CAACW,KAAK,CAACT,QAAQ,CAAC;IACnF,CAAC,CAAC;;IAEF;IACA,MAAMe,uBAAuB,GAAG,MAAAA,CAAA,KAAY;MAC1C,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAM3B,SAAS,CAAC4B,GAAG,CAAC,4BAA4B,CAAC;QAClE3B,eAAe,CAACmB,KAAK,GAAGO,QAAQ,CAACE,IAAI,CAAC5B,eAAe,IAAI,KAAK;QAC9DC,eAAe,CAACkB,KAAK,GAAGO,QAAQ,CAACE,IAAI,CAAC3B,eAAe,IAAI,KAAK;MAChE,CAAC,CAAC,OAAO4B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;;IAED;IACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;MACpC3B,eAAe,CAACe,KAAK,GAAG,IAAI;MAC5Bb,eAAe,CAACa,KAAK,GAAG,QAAQ,CAAC,CAAC;IACpC,CAAC;;IAED;IACA,MAAMa,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAAC1B,eAAe,CAACa,KAAK,EAAE;QAC1BtB,OAAO,CAACoC,OAAO,CAAC,SAAS,CAAC;QAC1B;MACF;MAEA/B,aAAa,CAACiB,KAAK,GAAG,IAAI;MAE1B,IAAI;QACF;QACA,MAAMO,QAAQ,GAAG,MAAM3B,SAAS,CAACmC,IAAI,CAAC,8BAA8B,EAAE;UACpEC,aAAa,EAAE7B,eAAe,CAACa,KAAK;UACpCiB,MAAM,EAAE9B,eAAe,CAACa,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG;QACrD,CAAC,CAAC;QAEF,IAAIO,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;UACzB;UACAC,MAAM,CAACC,IAAI,CAACb,QAAQ,CAACE,IAAI,CAACY,UAAU,EAAE,QAAQ,CAAC;UAC/C3C,OAAO,CAACwC,OAAO,CAAC,eAAe,CAAC;UAChCjC,eAAe,CAACe,KAAK,GAAG,KAAK;;UAE7B;UACAsB,kBAAkB,CAACf,QAAQ,CAACE,IAAI,CAACc,OAAO,CAAC;QAC3C;MACF,CAAC,CAAC,OAAOb,KAAK,EAAE;QACdhC,OAAO,CAACgC,KAAK,CAACA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAE/B,OAAO,IAAI,QAAQ,CAAC;MAC1D,CAAC,SAAS;QACRK,aAAa,CAACiB,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC;;IAED;IACA,MAAMsB,kBAAkB,GAAG,MAAOC,OAAO,IAAK;MAC5C,MAAMC,WAAW,GAAG,EAAE,CAAC,CAAC;MACxB,IAAIC,QAAQ,GAAG,CAAC;MAEhB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;QAC9B,IAAI;UACF,MAAMnB,QAAQ,GAAG,MAAM3B,SAAS,CAAC4B,GAAG,CAAC,uCAAuCe,OAAO,EAAE,CAAC;UAEtF,IAAIhB,QAAQ,CAACE,IAAI,CAACkB,MAAM,KAAK,WAAW,EAAE;YACxC9C,eAAe,CAACmB,KAAK,GAAG,IAAI;YAC5BtB,OAAO,CAACwC,OAAO,CAAC,SAAS,CAAC;YAC1B;UACF,CAAC,MAAM,IAAIX,QAAQ,CAACE,IAAI,CAACkB,MAAM,KAAK,QAAQ,EAAE;YAC5CjD,OAAO,CAACgC,KAAK,CAAC,UAAU,CAAC;YACzB;UACF;UAEAe,QAAQ,EAAE;UACV,IAAIA,QAAQ,GAAGD,WAAW,EAAE;YAC1BI,UAAU,CAACF,WAAW,EAAE,IAAI,CAAC;UAC/B;QACF,CAAC,CAAC,OAAOhB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACnC;MACF,CAAC;MAEDgB,WAAW,CAAC,CAAC;IACf,CAAC;;IAED;IACA,MAAMG,uBAAuB,GAAGA,CAAA,KAAM;MACpC3C,eAAe,CAACc,KAAK,GAAG,IAAI;MAC5BR,wBAAwB,CAACQ,KAAK,GAAG,IAAI;MACrC;MACAX,UAAU,CAACW,KAAK,GAAG;QACjBV,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;;IAED;IACA,MAAMuC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;MAC3C,IAAI;QACF;QACA,MAAM1C,aAAa,CAACY,KAAK,EAAE+B,QAAQ,CAAC,CAAC;QAErC/C,aAAa,CAACgB,KAAK,GAAG,IAAI;QAC1BR,wBAAwB,CAACQ,KAAK,GAAG,IAAI;QAErC,MAAMO,QAAQ,GAAG,MAAM3B,SAAS,CAACmC,IAAI,CAAC,6BAA6B,EAAE;UACnEzB,QAAQ,EAAED,UAAU,CAACW,KAAK,CAACV,QAAQ,CAACe,IAAI,CAAC,CAAC;UAC1Cd,QAAQ,EAAEF,UAAU,CAACW,KAAK,CAACT,QAAQ,CAACc,IAAI,CAAC;QAC3C,CAAC,CAAC;QAEF,IAAIE,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;UACzB1B,wBAAwB,CAACQ,KAAK,GAAG;YAC/BkB,OAAO,EAAE,IAAI;YACbxC,OAAO,EAAE6B,QAAQ,CAACE,IAAI,CAAC/B,OAAO,IAAI;UACpC,CAAC;UACDA,OAAO,CAACwC,OAAO,CAAC,SAAS,CAAC;QAC5B,CAAC,MAAM;UACL1B,wBAAwB,CAACQ,KAAK,GAAG;YAC/BkB,OAAO,EAAE,KAAK;YACdxC,OAAO,EAAE6B,QAAQ,CAACE,IAAI,CAAC/B,OAAO,IAAI;UACpC,CAAC;QACH;MACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BlB,wBAAwB,CAACQ,KAAK,GAAG;UAC/BkB,OAAO,EAAE,KAAK;UACdxC,OAAO,EAAEgC,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAE/B,OAAO,IAAI;QAC5C,CAAC;QACDA,OAAO,CAACgC,KAAK,CAAClB,wBAAwB,CAACQ,KAAK,CAACtB,OAAO,CAAC;MACvD,CAAC,SAAS;QACRM,aAAa,CAACgB,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC;;IAED;IACA,MAAMgC,0BAA0B,GAAGA,CAAA,KAAM;MACvClD,eAAe,CAACkB,KAAK,GAAG,IAAI;MAC5Bd,eAAe,CAACc,KAAK,GAAG,KAAK;MAC7BtB,OAAO,CAACwC,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC;;IAED;IACArE,SAAS,CAAC,MAAM;MACdyD,uBAAuB,CAAC,CAAC;IAC3B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}