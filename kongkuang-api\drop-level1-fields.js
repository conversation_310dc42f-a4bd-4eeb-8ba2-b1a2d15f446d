const { sequelize } = require('./config/db');

async function dropLevel1Fields() {
  try {
    console.log('正在连接数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    console.log('正在删除一级认证字段...');
    
    // 检查并删除一级认证字段
    const fieldsToCheck = ['level1_verified', 'level1_verified_at'];
    
    for (const field of fieldsToCheck) {
      try {
        // 先检查字段是否存在
        const [results] = await sequelize.query(`
          SELECT COLUMN_NAME 
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_SCHEMA = DATABASE() 
          AND TABLE_NAME = 'users' 
          AND COLUMN_NAME = '${field}'
        `);
        
        if (results.length > 0) {
          // 字段存在，删除它
          await sequelize.query(`ALTER TABLE users DROP COLUMN ${field}`);
          console.log(`✓ 删除字段成功: ${field}`);
        } else {
          console.log(`- 字段不存在，跳过: ${field}`);
        }
      } catch (error) {
        console.error(`删除字段失败: ${field}`, error.message);
      }
    }
    
    console.log('一级认证字段清理完成');
    process.exit(0);
  } catch (error) {
    console.error('清理失败:', error);
    process.exit(1);
  }
}

dropLevel1Fields();
