{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, resolveDynamicComponent as _resolveDynamicComponent, openBlock as _openBlock, createBlock as _createBlock, withCtx as _withCtx, createElementVNode as _createElementVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"theme-switch\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_error_handler = _resolveComponent(\"error-handler\");\n  const _component_app_layout = _resolveComponent(\"app-layout\");\n  const _component_router_view = _resolveComponent(\"router-view\");\n  const _component_dark_mode_icon = _resolveComponent(\"dark-mode-icon\");\n  const _component_light_mode_icon = _resolveComponent(\"light-mode-icon\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_loading_bar_provider = _resolveComponent(\"n-loading-bar-provider\");\n  const _component_n_dialog_provider = _resolveComponent(\"n-dialog-provider\");\n  const _component_n_message_provider = _resolveComponent(\"n-message-provider\");\n  const _component_n_config_provider = _resolveComponent(\"n-config-provider\");\n  return _openBlock(), _createBlock(_component_n_config_provider, {\n    theme: _ctx.currentTheme,\n    \"theme-overrides\": _ctx.themeOverrides\n  }, {\n    default: _withCtx(() => [_createVNode(_component_n_message_provider, null, {\n      default: _withCtx(() => [_createVNode(_component_n_dialog_provider, null, {\n        default: _withCtx(() => [_createVNode(_component_n_loading_bar_provider, null, {\n          default: _withCtx(() => [_createCommentVNode(\" 全局错误处理组件 \"), _createVNode(_component_error_handler, {\n            ref: \"errorHandlerRef\"\n          }, null, 512 /* NEED_PATCH */), _createVNode(_component_router_view, null, {\n            default: _withCtx(({\n              Component,\n              route\n            }) => [_ctx.isAuthRoute(route) ? (_openBlock(), _createBlock(_resolveDynamicComponent(Component), {\n              key: 0\n            })) : (_openBlock(), _createBlock(_component_app_layout, {\n              key: 1\n            }, {\n              default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(Component)))]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */))]),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" 主题切换按钮 \"), _createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_n_button, {\n            circle: \"\",\n            onClick: _ctx.toggleTheme\n          }, {\n            icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n              default: _withCtx(() => [_ctx.isDarkMode ? (_openBlock(), _createBlock(_component_dark_mode_icon, {\n                key: 0\n              })) : (_openBlock(), _createBlock(_component_light_mode_icon, {\n                key: 1\n              }))]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"onClick\"])])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"theme\", \"theme-overrides\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_n_config_provider", "theme", "_ctx", "currentTheme", "themeOverrides", "_createVNode", "_component_n_message_provider", "_component_n_dialog_provider", "_component_n_loading_bar_provider", "_createCommentVNode", "_component_error_handler", "ref", "_component_router_view", "Component", "route", "isAuthRoute", "_resolveDynamicComponent", "key", "_component_app_layout", "_createElementVNode", "_hoisted_1", "_component_n_button", "circle", "onClick", "toggleTheme", "icon", "_withCtx", "_component_n_icon", "isDarkMode", "_component_dark_mode_icon", "_component_light_mode_icon"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\App.vue"], "sourcesContent": ["<template>\n  <n-config-provider :theme=\"currentTheme\" :theme-overrides=\"themeOverrides\">\n    <n-message-provider>\n      <n-dialog-provider>\n        <n-loading-bar-provider>\n          <!-- 全局错误处理组件 -->\n          <error-handler ref=\"errorHandlerRef\" />\n        \n        <router-view v-slot=\"{ Component, route }\">\n          <template v-if=\"isAuthRoute(route)\">\n            <component :is=\"Component\" />\n          </template>\n          <app-layout v-else>\n            <component :is=\"Component\" />\n          </app-layout>\n        </router-view>\n        \n        <!-- 主题切换按钮 -->\n        <div class=\"theme-switch\">\n          <n-button circle @click=\"toggleTheme\">\n            <template #icon>\n              <n-icon>\n                <dark-mode-icon v-if=\"isDarkMode\" />\n                <light-mode-icon v-else />\n              </n-icon>\n            </template>\n          </n-button>\n        </div>\n        </n-loading-bar-provider>\n      </n-dialog-provider>\n    </n-message-provider>\n  </n-config-provider>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, onMounted, provide } from 'vue'\nimport { \n  NConfigProvider, \n  NMessageProvider, \n  NLoadingBarProvider,\n  NButton,\n  NIcon,\n  darkTheme\n} from 'naive-ui'\nimport AppLayout from './components/AppLayout.vue'\nimport PageScrollbar from './components/PageScrollbar.vue'\nimport { SunnyOutline as LightModeIcon, MoonOutline as DarkModeIcon } from '@vicons/ionicons5'\nimport ErrorHandler from './components/ErrorHandler.vue'\n\nexport default defineComponent({\n  name: 'App',\n  components: {\n    NConfigProvider,\n    NMessageProvider,\n    NLoadingBarProvider,\n    NButton,\n    NIcon,\n    AppLayout,\n    PageScrollbar,\n    LightModeIcon,\n    DarkModeIcon,\n    ErrorHandler\n  },\n  setup() {\n    const themeMode = ref(localStorage.getItem('theme') || 'system')\n    const isDarkSystem = ref(window.matchMedia('(prefers-color-scheme: dark)').matches)\n\n    const isDarkMode = computed(() => {\n      if (themeMode.value === 'system') return isDarkSystem.value\n      return themeMode.value === 'dark'\n    })\n\n    const currentTheme = computed(() => (isDarkMode.value ? darkTheme : null))\n\n    onMounted(() => {\n      const mql = window.matchMedia('(prefers-color-scheme: dark)')\n      mql.addEventListener('change', (e) => {\n        isDarkSystem.value = e.matches\n      })\n    })\n\n    const setTheme = (mode) => {\n      themeMode.value = mode\n      localStorage.setItem('theme', mode)\n    }\n\n    const toggleTheme = () => {\n      if (themeMode.value === 'system') {\n        setTheme(isDarkSystem.value ? 'light' : 'dark')\n      } else {\n        setTheme(themeMode.value === 'dark' ? 'light' : 'dark')\n      }\n    }\n\n    const baseThemeOverrides = {\n      common: {\n        borderRadius: '12px',\n        borderRadiusSmall: '8px',\n        primaryColor: '#2080f0',\n        primaryColorHover: '#4098fc',\n        primaryColorPressed: '#1060c9',\n        primaryColorSuppl: '#4098fc',\n      },\n      Button: {\n        borderRadius: '8px',\n        borderRadiusSmall: '6px',\n      },\n      Card: {\n        borderRadius: '16px',\n      },\n      Input: {\n        borderRadius: '8px',\n      },\n      Menu: {\n        borderRadius: '16px',\n        itemBorderRadius: '16px',\n      },\n    }\n\n    const themeOverrides = computed(() => {\n      if (isDarkMode.value) {\n        return {\n          ...baseThemeOverrides,\n          common: {\n          ...baseThemeOverrides.common,\n          bodyColor: '#101014',\n          cardColor: '#2a2a30', // 深色模式下卡片为灰色\n          textColor1: 'rgba(255, 255, 255, 0.9)',\n          textColor2: 'rgba(255, 255, 255, 0.7)',\n          textColor3: 'rgba(255, 255, 255, 0.5)',\n          dividerColor: 'rgba(255, 255, 255, 0.12)',\n          hoverColor: 'rgba(255, 255, 255, 0.08)',\n        },\n          Layout: {\n            color: '#101014',\n            headerColor: '#1a1a1f',\n            footerColor: 'transparent',\n            siderColor: '#1a1a1f',\n          },\n          Menu: {\n            itemTextColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemTextColorHoverHorizontal: 'rgba(255, 255, 255, 1)',\n            itemTextColorActiveHorizontal: '#2080f0',\n            itemIconColorActiveHorizontal: '#2080f0',\n            itemIconColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemIconColorHoverHorizontal: 'rgba(255, 255, 255, 1)'\n          },\n          Card: {\n            color: '#2a2a30', // 确保深色模式下卡片为灰色\n            colorModal: '#2a2a30',\n            colorPopover: '#2a2a30',\n            colorEmbedded: '#2a2a30',\n            colorTarget: '#2a2a30'\n          },\n        }\n      }\n      return {\n        ...baseThemeOverrides,\n        common: {\n          ...baseThemeOverrides.common,\n          bodyColor: '#f5f7fa',\n          cardColor: '#ffffff', // 浅色模式下卡片为纯白色\n          textColor1: 'rgba(0, 0, 0, 0.9)',\n          textColor2: 'rgba(0, 0, 0, 0.7)',\n          textColor3: 'rgba(0, 0, 0, 0.45)',\n          dividerColor: 'rgba(0, 0, 0, 0.09)',\n          hoverColor: 'rgba(0, 0, 0, 0.04)',\n        },\n        Layout: {\n          color: '#f5f7fa',\n          headerColor: '#ffffff',\n          footerColor: 'transparent',\n          siderColor: '#ffffff',\n        },\n        Card: {\n          color: '#ffffff', // 确保浅色模式下卡片为纯白色\n          colorModal: '#ffffff',\n          colorPopover: '#ffffff',\n          colorEmbedded: '#ffffff',\n          colorTarget: '#ffffff'\n        },\n      }\n    })\n    \n    const isAuthRoute = (route) => {\n      const authRoutes = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email']\n      return authRoutes.includes(route.path)\n    }\n    \n    const errorHandlerRef = ref(null)\n    \n    provide('$$app', {\n      setTheme,\n      themeMode,\n    })\n    \n    return {\n      isDarkMode,\n      currentTheme,\n      toggleTheme,\n      themeOverrides,\n      isAuthRoute,\n      errorHandlerRef\n    }\n  }\n})\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',\n    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n}\n\n#app {\n  height: 100%;\n}\n\n* {\n  box-sizing: border-box;\n}\n\n/* 主题切换按钮 */\n.theme-switch {\n  position: fixed;\n  right: 24px;\n  bottom: 24px;\n  z-index: 1000;\n}\n\n.theme-switch .n-button {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n/* 全局强制覆盖导航菜单样式 */\n.n-menu-item-content {\n  border-radius: 16px !important;\n}\n\n.n-menu-item-content::before {\n  border-radius: 16px !important;\n}\n\n.n-menu-item:hover .n-menu-item-content,\n.n-menu-item--selected .n-menu-item-content {\n  border-radius: 16px !important;\n}\n</style>\n"], "mappings": ";;EAkBaA,KAAK,EAAC;AAAc;;;;;;;;;;;;;uBAjB/BC,YAAA,CA8BoBC,4BAAA;IA9BAC,KAAK,EAAEC,IAAA,CAAAC,YAAY;IAAG,iBAAe,EAAED,IAAA,CAAAE;;sBACzD,MA4BqB,CA5BrBC,YAAA,CA4BqBC,6BAAA;wBA3BnB,MA0BoB,CA1BpBD,YAAA,CA0BoBE,4BAAA;0BAzBlB,MAwByB,CAxBzBF,YAAA,CAwByBG,iCAAA;4BAvBvB,MAAiB,CAAjBC,mBAAA,cAAiB,EACjBJ,YAAA,CAAuCK,wBAAA;YAAxBC,GAAG,EAAC;UAAiB,gCAEtCN,YAAA,CAOcO,sBAAA;8BANZ,CAEW;cAHUC,SAAS;cAAEC;YAAK,OACrBZ,IAAA,CAAAa,WAAW,CAACD,KAAK,K,cAC/Bf,YAAA,CAA6BiB,wBAAA,CAAbH,SAAS;cAAAI,GAAA;YAAA,O,cAE3BlB,YAAA,CAEamB,qBAAA;cAAAD,GAAA;YAAA;gCADX,MAA6B,E,cAA7BlB,YAAA,CAA6BiB,wBAAA,CAAbH,SAAS,I;;;;cAI7BJ,mBAAA,YAAe,EACfU,mBAAA,CASM,OATNC,UASM,GARJf,YAAA,CAOWgB,mBAAA;YAPDC,MAAM,EAAN,EAAM;YAAEC,OAAK,EAAErB,IAAA,CAAAsB;;YACZC,IAAI,EAAAC,QAAA,CACb,MAGS,CAHTrB,YAAA,CAGSsB,iBAAA;gCAFP,MAAoC,CAAdzB,IAAA,CAAA0B,UAAU,I,cAAhC7B,YAAA,CAAoC8B,yBAAA;gBAAAZ,GAAA;cAAA,O,cACpClB,YAAA,CAA0B+B,0BAAA;gBAAAb,GAAA;cAAA,I", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}