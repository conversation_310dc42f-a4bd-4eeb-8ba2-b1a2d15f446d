const { VerificationCode } = require('./models');
const { connectDB } = require('./config/db');

async function getLatestCode() {
  try {
    await connectDB();
    
    const latestCode = await VerificationCode.findOne({
      where: {
        contact: '<EMAIL>',
        contact_type: 'email',
        used: false
      },
      order: [['created_at', 'DESC']]
    });
    
    if (latestCode) {
      console.log('最新验证码:', latestCode.code);
      console.log('过期时间:', latestCode.expires_at);
    } else {
      console.log('没有找到验证码');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('查询验证码失败:', error);
    process.exit(1);
  }
}

getLatestCode();
