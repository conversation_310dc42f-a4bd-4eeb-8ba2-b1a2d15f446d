{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, normalizeClass as _normalizeClass, normalizeStyle as _normalizeStyle, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createBlock as _createBlock, renderList as _renderList, Fragment as _Fragment, resolveDynamicComponent as _resolveDynamicComponent } from \"vue\";\nconst _hoisted_1 = {\n  class: \"security-container\"\n};\nconst _hoisted_2 = {\n  class: \"security-content\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"password-strength\"\n};\nconst _hoisted_4 = {\n  class: \"password-strength-bar\"\n};\nconst _hoisted_5 = {\n  class: \"password-strength-text\"\n};\nconst _hoisted_6 = {\n  class: \"form-actions\"\n};\nconst _hoisted_7 = {\n  class: \"security-item\"\n};\nconst _hoisted_8 = {\n  class: \"security-status\"\n};\nconst _hoisted_9 = {\n  class: \"security-item\"\n};\nconst _hoisted_10 = {\n  class: \"security-status\"\n};\nconst _hoisted_11 = {\n  class: \"security-item\"\n};\nconst _hoisted_12 = {\n  class: \"security-status\"\n};\nconst _hoisted_13 = {\n  class: \"device-list\"\n};\nconst _hoisted_14 = {\n  class: \"device-icon\"\n};\nconst _hoisted_15 = {\n  class: \"device-info\"\n};\nconst _hoisted_16 = {\n  class: \"device-name\"\n};\nconst _hoisted_17 = {\n  class: \"device-meta\"\n};\nconst _hoisted_18 = {\n  class: \"device-actions\"\n};\nconst _hoisted_19 = {\n  key: 1,\n  class: \"app-list\"\n};\nconst _hoisted_20 = {\n  class: \"app-info\"\n};\nconst _hoisted_21 = {\n  class: \"app-name\"\n};\nconst _hoisted_22 = {\n  class: \"app-scopes\"\n};\nconst _hoisted_23 = {\n  class: \"app-actions\"\n};\nconst _hoisted_24 = {\n  class: \"theme-options\"\n};\nconst _hoisted_25 = {\n  class: \"theme-option\"\n};\nconst _hoisted_26 = {\n  class: \"theme-option\"\n};\nconst _hoisted_27 = {\n  class: \"theme-option\"\n};\nconst _hoisted_28 = {\n  class: \"danger-item\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_n_input = _resolveComponent(\"n-input\");\n  const _component_n_form_item = _resolveComponent(\"n-form-item\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_form = _resolveComponent(\"n-form\");\n  const _component_n_card = _resolveComponent(\"n-card\");\n  const _component_n_thing = _resolveComponent(\"n-thing\");\n  const _component_n_list_item = _resolveComponent(\"n-list-item\");\n  const _component_n_list = _resolveComponent(\"n-list\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_tag = _resolveComponent(\"n-tag\");\n  const _component_n_empty = _resolveComponent(\"n-empty\");\n  const _component_n_radio = _resolveComponent(\"n-radio\");\n  const _component_n_space = _resolveComponent(\"n-space\");\n  const _component_n_radio_group = _resolveComponent(\"n-radio-group\");\n  const _component_n_spin = _resolveComponent(\"n-spin\");\n  const _component_n_modal = _resolveComponent(\"n-modal\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_n_spin, {\n    show: _ctx.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 修改密码卡片 \"), _createVNode(_component_n_card, {\n      title: \"修改密码\",\n      class: \"security-card\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_form, {\n        ref: \"passwordFormRef\",\n        model: _ctx.passwordForm,\n        rules: _ctx.passwordRules,\n        \"label-placement\": \"left\",\n        \"label-width\": \"120px\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_form_item, {\n          path: \"oldPassword\",\n          label: \"当前密码\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_input, {\n            value: _ctx.passwordForm.oldPassword,\n            \"onUpdate:value\": _cache[0] || (_cache[0] = $event => _ctx.passwordForm.oldPassword = $event),\n            type: \"password\",\n            placeholder: \"请输入当前密码\",\n            \"show-password-on\": \"click\"\n          }, null, 8 /* PROPS */, [\"value\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_form_item, {\n          path: \"newPassword\",\n          label: \"新密码\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_input, {\n            value: _ctx.passwordForm.newPassword,\n            \"onUpdate:value\": [_cache[1] || (_cache[1] = $event => _ctx.passwordForm.newPassword = $event), _ctx.updatePasswordStrength],\n            type: \"password\",\n            placeholder: \"请输入新密码\",\n            \"show-password-on\": \"click\"\n          }, null, 8 /* PROPS */, [\"value\", \"onUpdate:value\"]), _ctx.passwordForm.newPassword ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", {\n            class: _normalizeClass([\"password-strength-progress\", _ctx.passwordStrengthClass]),\n            style: _normalizeStyle({\n              width: `${_ctx.passwordStrength}%`\n            })\n          }, null, 6 /* CLASS, STYLE */)]), _createElementVNode(\"div\", _hoisted_5, [_cache[7] || (_cache[7] = _createElementVNode(\"span\", {\n            class: \"password-strength-label\"\n          }, \"密码强度\", -1 /* CACHED */)), _createElementVNode(\"span\", {\n            class: _normalizeClass([\"password-strength-value\", _ctx.passwordStrengthClass])\n          }, _toDisplayString(_ctx.passwordStrengthText), 3 /* TEXT, CLASS */)])])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_form_item, {\n          path: \"confirmPassword\",\n          label: \"确认新密码\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_n_input, {\n            value: _ctx.passwordForm.confirmPassword,\n            \"onUpdate:value\": _cache[2] || (_cache[2] = $event => _ctx.passwordForm.confirmPassword = $event),\n            type: \"password\",\n            placeholder: \"请再次输入新密码\",\n            \"show-password-on\": \"click\"\n          }, null, 8 /* PROPS */, [\"value\"])]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_n_button, {\n          type: \"primary\",\n          loading: _ctx.submittingPassword,\n          onClick: _ctx.handleChangePassword\n        }, {\n          default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\" 修改密码 \")])),\n          _: 1 /* STABLE */,\n          __: [8]\n        }, 8 /* PROPS */, [\"loading\", \"onClick\"])])]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 账户安全卡片 \"), _createVNode(_component_n_card, {\n      title: \"账户安全\",\n      class: \"security-card\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_list, null, {\n        default: _withCtx(() => [_createVNode(_component_n_list_item, null, {\n          default: _withCtx(() => [_createVNode(_component_n_thing, {\n            title: \"邮箱验证\",\n            \"content-style\": \"margin-top: 10px;\"\n          }, {\n            description: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"span\", _hoisted_8, _toDisplayString(_ctx.user.emailVerified ? '已验证' : '未验证'), 1 /* TEXT */), !_ctx.user.emailVerified ? (_openBlock(), _createBlock(_component_n_button, {\n              key: 0,\n              size: \"small\",\n              type: \"primary\",\n              ghost: \"\",\n              onClick: _ctx.handleVerifyEmail\n            }, {\n              default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\" 验证邮箱 \")])),\n              _: 1 /* STABLE */,\n              __: [9]\n            }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_list_item, null, {\n          default: _withCtx(() => [_createVNode(_component_n_thing, {\n            title: \"手机验证\",\n            \"content-style\": \"margin-top: 10px;\"\n          }, {\n            description: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"span\", _hoisted_10, _toDisplayString(_ctx.user.phoneVerified ? '已验证' : '未验证'), 1 /* TEXT */), !_ctx.user.phoneVerified ? (_openBlock(), _createBlock(_component_n_button, {\n              key: 0,\n              size: \"small\",\n              type: \"primary\",\n              ghost: \"\",\n              onClick: _ctx.handleVerifyPhone\n            }, {\n              default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\" 验证手机 \")])),\n              _: 1 /* STABLE */,\n              __: [10]\n            }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_n_list_item, null, {\n          default: _withCtx(() => [_createVNode(_component_n_thing, {\n            title: \"两步验证\",\n            \"content-style\": \"margin-top: 10px;\"\n          }, {\n            description: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"span\", _hoisted_12, _toDisplayString(_ctx.user.twoFactorEnabled ? '已启用' : '未启用'), 1 /* TEXT */), !_ctx.user.twoFactorEnabled ? (_openBlock(), _createBlock(_component_n_button, {\n              key: 0,\n              size: \"small\",\n              type: \"primary\",\n              ghost: \"\",\n              onClick: _ctx.handleSetupTwoFactor\n            }, {\n              default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 设置 \")])),\n              _: 1 /* STABLE */,\n              __: [11]\n            }, 8 /* PROPS */, [\"onClick\"])) : (_openBlock(), _createBlock(_component_n_button, {\n              key: 1,\n              size: \"small\",\n              type: \"error\",\n              ghost: \"\",\n              onClick: _ctx.handleDisableTwoFactor\n            }, {\n              default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 禁用 \")])),\n              _: 1 /* STABLE */,\n              __: [12]\n            }, 8 /* PROPS */, [\"onClick\"]))])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 登录设备卡片 \"), _createVNode(_component_n_card, {\n      title: \"登录设备\",\n      class: \"security-card\"\n    }, {\n      action: _withCtx(() => [_createVNode(_component_n_button, {\n        block: \"\",\n        type: \"warning\",\n        onClick: _ctx.handleLogoutAllDevices\n      }, {\n        default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\" 退出所有其他设备 \")])),\n        _: 1 /* STABLE */,\n        __: [15]\n      }, 8 /* PROPS */, [\"onClick\"])]),\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.devices, (device, index) => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: index,\n          class: \"device-item\"\n        }, [_createElementVNode(\"div\", _hoisted_14, [_createVNode(_component_n_icon, {\n          size: \"24\"\n        }, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.getDeviceIcon(device.type))))]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */)]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString(device.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"span\", null, _toDisplayString(device.location), 1 /* TEXT */), _createElementVNode(\"span\", null, _toDisplayString(device.lastActive), 1 /* TEXT */), device.current ? (_openBlock(), _createBlock(_component_n_tag, {\n          key: 0,\n          type: \"success\",\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [...(_cache[13] || (_cache[13] = [_createTextVNode(\"当前设备\")]))]),\n          _: 1 /* STABLE */,\n          __: [13]\n        })) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_18, [!device.current ? (_openBlock(), _createBlock(_component_n_button, {\n          key: 0,\n          type: \"error\",\n          text: \"\",\n          onClick: $event => _ctx.handleLogoutDevice(device.id)\n        }, {\n          default: _withCtx(() => [...(_cache[14] || (_cache[14] = [_createTextVNode(\" 退出登录 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [14]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n      }), 128 /* KEYED_FRAGMENT */))])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 授权应用卡片 \"), _createVNode(_component_n_card, {\n      title: \"已授权的应用\",\n      class: \"security-card\"\n    }, {\n      default: _withCtx(() => [_ctx.authorizedApps.length === 0 ? (_openBlock(), _createBlock(_component_n_empty, {\n        key: 0,\n        description: \"暂无授权应用\"\n      })) : (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.authorizedApps, app => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: app.id,\n          class: \"app-item\"\n        }, [_createElementVNode(\"div\", {\n          class: \"app-icon\",\n          style: _normalizeStyle({\n            backgroundColor: app.icon_color + '20'\n          })\n        }, [_createVNode(_component_n_icon, {\n          size: \"20\",\n          color: app.icon_color\n        }, {\n          default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(_ctx.getAppIcon(app.icon))))]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])], 4 /* STYLE */), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, _toDisplayString(app.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(app.scopes, scope => {\n          return _openBlock(), _createBlock(_component_n_tag, {\n            key: scope,\n            size: \"small\",\n            class: \"scope-tag\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.getScopeName(scope)), 1 /* TEXT */)]),\n            _: 2 /* DYNAMIC */\n          }, 1024 /* DYNAMIC_SLOTS */);\n        }), 128 /* KEYED_FRAGMENT */))])]), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_n_button, {\n          type: \"error\",\n          text: \"\",\n          onClick: $event => _ctx.handleRevokeAccess(app.id)\n        }, {\n          default: _withCtx(() => [...(_cache[16] || (_cache[16] = [_createTextVNode(\" 撤销授权 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [16]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])]);\n      }), 128 /* KEYED_FRAGMENT */))]))]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 主题设置卡片 \"), _createVNode(_component_n_card, {\n      title: \"主题设置\",\n      class: \"security-card\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_createVNode(_component_n_radio_group, {\n        value: _ctx.themeMode,\n        \"onUpdate:value\": [_cache[3] || (_cache[3] = $event => _ctx.themeMode = $event), _ctx.setTheme],\n        class: \"theme-radio-group\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_space, {\n          justify: \"space-around\",\n          align: \"center\",\n          class: \"theme-space\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_25, [_cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n            class: \"theme-preview system-theme\"\n          }, null, -1 /* CACHED */)), _createVNode(_component_n_radio, {\n            value: \"system\"\n          }, {\n            default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"跟随系统\")])),\n            _: 1 /* STABLE */,\n            __: [17]\n          })]), _createElementVNode(\"div\", _hoisted_26, [_cache[20] || (_cache[20] = _createElementVNode(\"div\", {\n            class: \"theme-preview light-theme\"\n          }, null, -1 /* CACHED */)), _createVNode(_component_n_radio, {\n            value: \"light\"\n          }, {\n            default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"亮色模式\")])),\n            _: 1 /* STABLE */,\n            __: [19]\n          })]), _createElementVNode(\"div\", _hoisted_27, [_cache[22] || (_cache[22] = _createElementVNode(\"div\", {\n            class: \"theme-preview dark-theme\"\n          }, null, -1 /* CACHED */)), _createVNode(_component_n_radio, {\n            value: \"dark\"\n          }, {\n            default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"暗色模式\")])),\n            _: 1 /* STABLE */,\n            __: [21]\n          })])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"value\", \"onUpdate:value\"])])]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 危险区域卡片 \"), _createVNode(_component_n_card, {\n      title: \"危险区域\",\n      class: \"security-card danger-card\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_space, {\n        vertical: \"\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_28, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n          class: \"danger-info\"\n        }, [_createElementVNode(\"div\", {\n          class: \"danger-title\"\n        }, \"删除账户\"), _createElementVNode(\"div\", {\n          class: \"danger-description\"\n        }, \" 删除账户将永久移除您的所有数据，此操作不可逆。 \")], -1 /* CACHED */)), _createVNode(_component_n_button, {\n          type: \"error\",\n          onClick: _ctx.showDeleteConfirm\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 删除账户 \")])),\n          _: 1 /* STABLE */,\n          __: [23]\n        }, 8 /* PROPS */, [\"onClick\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"]), _createCommentVNode(\" 删除账户确认对话框 \"), _createVNode(_component_n_modal, {\n    show: _ctx.showDeleteModal,\n    \"onUpdate:show\": _cache[5] || (_cache[5] = $event => _ctx.showDeleteModal = $event),\n    preset: \"dialog\",\n    title: \"删除账户\",\n    \"positive-text\": \"确认删除\",\n    \"negative-text\": \"取消\",\n    type: \"error\",\n    onPositiveClick: _ctx.handleDeleteAccount,\n    onNegativeClick: _cache[6] || (_cache[6] = $event => _ctx.showDeleteModal = false)\n  }, {\n    default: _withCtx(() => [_cache[25] || (_cache[25] = _createElementVNode(\"p\", null, \"您确定要删除账户吗？此操作不可逆，您的所有数据将被永久删除。\", -1 /* CACHED */)), _createVNode(_component_n_input, {\n      value: _ctx.deleteConfirmText,\n      \"onUpdate:value\": _cache[4] || (_cache[4] = $event => _ctx.deleteConfirmText = $event),\n      placeholder: \"请输入您的密码以确认删除\",\n      type: \"password\"\n    }, null, 8 /* PROPS */, [\"value\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\", \"onPositiveClick\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_n_spin", "show", "_ctx", "loading", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_component_n_card", "title", "_component_n_form", "ref", "model", "passwordForm", "rules", "passwordRules", "_component_n_form_item", "path", "label", "_component_n_input", "value", "oldPassword", "$event", "type", "placeholder", "newPassword", "updatePasswordStrength", "_hoisted_3", "_hoisted_4", "_normalizeClass", "passwordStrengthClass", "style", "_normalizeStyle", "width", "passwordStrength", "_hoisted_5", "passwordStrengthText", "confirmPassword", "_hoisted_6", "_component_n_button", "submittingPassword", "onClick", "handleChangePassword", "_cache", "_component_n_list", "_component_n_list_item", "_component_n_thing", "description", "_withCtx", "_hoisted_7", "_hoisted_8", "_toDisplayString", "user", "emailVerified", "_createBlock", "size", "ghost", "handleVerifyEmail", "_hoisted_9", "_hoisted_10", "phoneVerified", "handleVerifyPhone", "_hoisted_11", "_hoisted_12", "twoFactorEnabled", "handleSetupTwoFactor", "handleDisableTwoFactor", "action", "block", "handleLogoutAllDevices", "_hoisted_13", "_Fragment", "_renderList", "devices", "device", "index", "key", "_hoisted_14", "_component_n_icon", "_resolveDynamicComponent", "getDeviceIcon", "_hoisted_15", "_hoisted_16", "name", "_hoisted_17", "location", "lastActive", "current", "_component_n_tag", "_hoisted_18", "text", "handleLogoutDevice", "id", "authorizedApps", "length", "_component_n_empty", "_hoisted_19", "app", "backgroundColor", "icon_color", "color", "getAppIcon", "icon", "_hoisted_20", "_hoisted_21", "_hoisted_22", "scopes", "scope", "getScopeName", "_hoisted_23", "handleRevokeAccess", "_hoisted_24", "_component_n_radio_group", "themeMode", "setTheme", "_component_n_space", "justify", "align", "_hoisted_25", "_component_n_radio", "_hoisted_26", "_hoisted_27", "vertical", "_hoisted_28", "showDeleteConfirm", "_component_n_modal", "showDeleteModal", "preset", "onPositiveClick", "handleDeleteAccount", "onNegativeClick", "default", "deleteConfirmText"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Security.vue"], "sourcesContent": ["<template>\r\n  <div class=\"security-container\">\r\n    <n-spin :show=\"loading\">\r\n      <div class=\"security-content\">\r\n        <!-- 修改密码卡片 -->\r\n        <n-card title=\"修改密码\" class=\"security-card\">\r\n          <n-form\r\n            ref=\"passwordFormRef\"\r\n            :model=\"passwordForm\"\r\n            :rules=\"passwordRules\"\r\n            label-placement=\"left\"\r\n            label-width=\"120px\"\r\n          >\r\n            <n-form-item path=\"oldPassword\" label=\"当前密码\">\r\n              <n-input\r\n                v-model:value=\"passwordForm.oldPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请输入当前密码\"\r\n                show-password-on=\"click\"\r\n              />\r\n            </n-form-item>\r\n            \r\n            <n-form-item path=\"newPassword\" label=\"新密码\">\r\n              <n-input\r\n                v-model:value=\"passwordForm.newPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请输入新密码\"\r\n                show-password-on=\"click\"\r\n                @update:value=\"updatePasswordStrength\"\r\n              />\r\n              <div class=\"password-strength\" v-if=\"passwordForm.newPassword\">\r\n                <div class=\"password-strength-bar\">\r\n                  <div \r\n                    class=\"password-strength-progress\" \r\n                    :class=\"passwordStrengthClass\"\r\n                    :style=\"{ width: `${passwordStrength}%` }\"\r\n                  ></div>\r\n                </div>\r\n                <div class=\"password-strength-text\">\r\n                  <span class=\"password-strength-label\">密码强度</span>\r\n                  <span class=\"password-strength-value\" :class=\"passwordStrengthClass\">{{ passwordStrengthText }}</span>\r\n                </div>\r\n              </div>\r\n            </n-form-item>\r\n            \r\n            <n-form-item path=\"confirmPassword\" label=\"确认新密码\">\r\n              <n-input\r\n                v-model:value=\"passwordForm.confirmPassword\"\r\n                type=\"password\"\r\n                placeholder=\"请再次输入新密码\"\r\n                show-password-on=\"click\"\r\n              />\r\n            </n-form-item>\r\n            \r\n            <div class=\"form-actions\">\r\n              <n-button type=\"primary\" :loading=\"submittingPassword\" @click=\"handleChangePassword\">\r\n                修改密码\r\n              </n-button>\r\n            </div>\r\n          </n-form>\r\n        </n-card>\r\n        \r\n        <!-- 账户安全卡片 -->\r\n        <n-card title=\"账户安全\" class=\"security-card\">\r\n          <n-list>\r\n            <n-list-item>\r\n              <n-thing title=\"邮箱验证\" content-style=\"margin-top: 10px;\">\r\n                <template #description>\r\n                  <div class=\"security-item\">\r\n                    <span class=\"security-status\">{{ user.emailVerified ? '已验证' : '未验证' }}</span>\r\n                    <n-button\r\n                      size=\"small\"\r\n                      type=\"primary\"\r\n                      ghost\r\n                      v-if=\"!user.emailVerified\"\r\n                      @click=\"handleVerifyEmail\"\r\n                    >\r\n                      验证邮箱\r\n                    </n-button>\r\n                  </div>\r\n                </template>\r\n              </n-thing>\r\n            </n-list-item>\r\n            \r\n            <n-list-item>\r\n              <n-thing title=\"手机验证\" content-style=\"margin-top: 10px;\">\r\n                <template #description>\r\n                  <div class=\"security-item\">\r\n                    <span class=\"security-status\">{{ user.phoneVerified ? '已验证' : '未验证' }}</span>\r\n                    <n-button\r\n                      size=\"small\"\r\n                      type=\"primary\"\r\n                      ghost\r\n                      v-if=\"!user.phoneVerified\"\r\n                      @click=\"handleVerifyPhone\"\r\n                    >\r\n                      验证手机\r\n                    </n-button>\r\n                  </div>\r\n                </template>\r\n              </n-thing>\r\n            </n-list-item>\r\n            \r\n            <n-list-item>\r\n              <n-thing title=\"两步验证\" content-style=\"margin-top: 10px;\">\r\n                <template #description>\r\n                  <div class=\"security-item\">\r\n                    <span class=\"security-status\">{{ user.twoFactorEnabled ? '已启用' : '未启用' }}</span>\r\n                    <n-button\r\n                      size=\"small\"\r\n                      type=\"primary\"\r\n                      ghost\r\n                      v-if=\"!user.twoFactorEnabled\"\r\n                      @click=\"handleSetupTwoFactor\"\r\n                    >\r\n                      设置\r\n                    </n-button>\r\n                    <n-button\r\n                      size=\"small\"\r\n                      type=\"error\"\r\n                      ghost\r\n                      v-else\r\n                      @click=\"handleDisableTwoFactor\"\r\n                    >\r\n                      禁用\r\n                    </n-button>\r\n                  </div>\r\n                </template>\r\n              </n-thing>\r\n            </n-list-item>\r\n          </n-list>\r\n        </n-card>\r\n        \r\n        <!-- 登录设备卡片 -->\r\n        <n-card title=\"登录设备\" class=\"security-card\">\r\n          <div class=\"device-list\">\r\n            <div v-for=\"(device, index) in devices\" :key=\"index\" class=\"device-item\">\r\n              <div class=\"device-icon\">\r\n                <n-icon size=\"24\">\r\n                  <component :is=\"getDeviceIcon(device.type)\" />\r\n                </n-icon>\r\n              </div>\r\n              <div class=\"device-info\">\r\n                <div class=\"device-name\">{{ device.name }}</div>\r\n                <div class=\"device-meta\">\r\n                  <span>{{ device.location }}</span>\r\n                  <span>{{ device.lastActive }}</span>\r\n                  <n-tag v-if=\"device.current\" type=\"success\" size=\"small\">当前设备</n-tag>\r\n                </div>\r\n              </div>\r\n              <div class=\"device-actions\">\r\n                <n-button\r\n                  v-if=\"!device.current\"\r\n                  type=\"error\"\r\n                  text\r\n                  @click=\"handleLogoutDevice(device.id)\"\r\n                >\r\n                  退出登录\r\n                </n-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          <template #action>\r\n            <n-button block type=\"warning\" @click=\"handleLogoutAllDevices\">\r\n              退出所有其他设备\r\n            </n-button>\r\n          </template>\r\n        </n-card>\r\n        \r\n        <!-- 授权应用卡片 -->\r\n        <n-card title=\"已授权的应用\" class=\"security-card\">\r\n          <n-empty v-if=\"authorizedApps.length === 0\" description=\"暂无授权应用\" />\r\n          \r\n          <div v-else class=\"app-list\">\r\n            <div v-for=\"app in authorizedApps\" :key=\"app.id\" class=\"app-item\">\r\n              <div class=\"app-icon\" :style=\"{ backgroundColor: app.icon_color + '20' }\">\r\n                <n-icon size=\"20\" :color=\"app.icon_color\">\r\n                  <component :is=\"getAppIcon(app.icon)\" />\r\n                </n-icon>\r\n              </div>\r\n              <div class=\"app-info\">\r\n                <div class=\"app-name\">{{ app.name }}</div>\r\n                <div class=\"app-scopes\">\r\n                  <n-tag v-for=\"scope in app.scopes\" :key=\"scope\" size=\"small\" class=\"scope-tag\">\r\n                    {{ getScopeName(scope) }}\r\n                  </n-tag>\r\n                </div>\r\n              </div>\r\n              <div class=\"app-actions\">\r\n                <n-button type=\"error\" text @click=\"handleRevokeAccess(app.id)\">\r\n                  撤销授权\r\n                </n-button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </n-card>\r\n        \r\n        <!-- 主题设置卡片 -->\r\n        <n-card title=\"主题设置\" class=\"security-card\">\r\n          <div class=\"theme-options\">\r\n            <n-radio-group v-model:value=\"themeMode\" @update:value=\"setTheme\" class=\"theme-radio-group\">\r\n              <n-space justify=\"space-around\" align=\"center\" class=\"theme-space\">\r\n                <div class=\"theme-option\">\r\n                  <div class=\"theme-preview system-theme\"></div>\r\n                  <n-radio value=\"system\">跟随系统</n-radio>\r\n                </div>\r\n                <div class=\"theme-option\">\r\n                  <div class=\"theme-preview light-theme\"></div>\r\n                  <n-radio value=\"light\">亮色模式</n-radio>\r\n                </div>\r\n                <div class=\"theme-option\">\r\n                  <div class=\"theme-preview dark-theme\"></div>\r\n                  <n-radio value=\"dark\">暗色模式</n-radio>\r\n                </div>\r\n              </n-space>\r\n            </n-radio-group>\r\n          </div>\r\n        </n-card>\r\n        \r\n        <!-- 危险区域卡片 -->\r\n        <n-card title=\"危险区域\" class=\"security-card danger-card\">\r\n          <n-space vertical>\r\n            <div class=\"danger-item\">\r\n              <div class=\"danger-info\">\r\n                <div class=\"danger-title\">删除账户</div>\r\n                <div class=\"danger-description\">\r\n                  删除账户将永久移除您的所有数据，此操作不可逆。\r\n                </div>\r\n              </div>\r\n              <n-button type=\"error\" @click=\"showDeleteConfirm\">\r\n                删除账户\r\n              </n-button>\r\n            </div>\r\n          </n-space>\r\n        </n-card>\r\n      </div>\r\n    </n-spin>\r\n    \r\n    <!-- 删除账户确认对话框 -->\r\n    <n-modal\r\n      v-model:show=\"showDeleteModal\"\r\n      preset=\"dialog\"\r\n      title=\"删除账户\"\r\n      positive-text=\"确认删除\"\r\n      negative-text=\"取消\"\r\n      type=\"error\"\r\n      @positive-click=\"handleDeleteAccount\"\r\n      @negative-click=\"showDeleteModal = false\"\r\n    >\r\n      <template #default>\r\n        <p>您确定要删除账户吗？此操作不可逆，您的所有数据将被永久删除。</p>\r\n        <n-input\r\n          v-model:value=\"deleteConfirmText\"\r\n          placeholder=\"请输入您的密码以确认删除\"\r\n          type=\"password\"\r\n        />\r\n      </template>\r\n    </n-modal>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { defineComponent, ref, reactive, onMounted, inject, computed } from 'vue'\r\nimport { \r\n  NCard, \r\n  NForm,\r\n  NFormItem,\r\n  NInput,\r\n  NButton,\r\n  NList,\r\n  NListItem,\r\n  NThing,\r\n  NTag,\r\n  NIcon,\r\n  NEmpty,\r\n  NSpace,\r\n  NSpin,\r\n  NModal,\r\n  useMessage,\r\n  NRadioGroup,\r\n  NRadio\r\n} from 'naive-ui'\r\nimport { \r\n  Desktop,\r\n  Phone,\r\n  Tablet,\r\n  Laptop,\r\n  Apps,\r\n  Briefcase,\r\n  Chatbubbles,\r\n  Cloud,\r\n  Globe,\r\n  Grid,\r\n  Layers,\r\n  PieChart,\r\n  Server\r\n} from '@vicons/ionicons5'\r\nimport { useUserStore } from '../stores/user'\r\nimport { useRouter } from 'vue-router'\r\nimport axios from 'axios'\r\n\r\nexport default defineComponent({\r\n  name: 'SecurityPage',\r\n  components: {\r\n    NCard,\r\n    NForm,\r\n    NFormItem,\r\n    NInput,\r\n    NButton,\r\n    NList,\r\n    NListItem,\r\n    NThing,\r\n    NTag,\r\n    NIcon,\r\n    NEmpty,\r\n    NSpace,\r\n    NSpin,\r\n    NModal,\r\n    Desktop,\r\n    Phone,\r\n    Tablet,\r\n    Laptop,\r\n    NRadioGroup,\r\n    NRadio\r\n  },\r\n  setup() {\r\n    const router = useRouter()\r\n    const message = useMessage()\r\n    const userStore = useUserStore()\r\n    const loading = ref(false)\r\n    const submittingPassword = ref(false)\r\n    const showDeleteModal = ref(false)\r\n    const deleteConfirmText = ref('')\r\n    const passwordStrength = ref(0)\r\n    \r\n    const passwordFormRef = ref(null)\r\n    \r\n    const user = reactive({\r\n      id: '',\r\n      username: '',\r\n      email: '',\r\n      status: '',\r\n      emailVerified: false,\r\n      phoneVerified: false,\r\n      twoFactorEnabled: false\r\n    })\r\n    \r\n    const passwordForm = reactive({\r\n      oldPassword: '',\r\n      newPassword: '',\r\n      confirmPassword: ''\r\n    })\r\n    \r\n    const passwordRules = {\r\n      oldPassword: [\r\n        { required: true, message: '请输入当前密码', trigger: 'blur' }\r\n      ],\r\n      newPassword: [\r\n        { required: true, message: '请输入新密码', trigger: 'blur' },\r\n        { min: 8, message: '密码长度不能少于8个字符', trigger: 'blur' }\r\n      ],\r\n      confirmPassword: [\r\n        { required: true, message: '请确认新密码', trigger: 'blur' },\r\n        {\r\n          validator: (rule, value) => value === passwordForm.newPassword,\r\n          message: '两次输入的密码不一致',\r\n          trigger: 'blur'\r\n        }\r\n      ]\r\n    }\r\n    \r\n    // 计算密码强度\r\n    const updatePasswordStrength = (password) => {\r\n      if (!password) {\r\n        passwordStrength.value = 0\r\n        return\r\n      }\r\n      \r\n      let score = 0\r\n      \r\n      // 长度评分\r\n      if (password.length >= 8) score += 10\r\n      if (password.length >= 12) score += 10\r\n      if (password.length >= 16) score += 10\r\n      \r\n      // 复杂度评分\r\n      if (/[a-z]/.test(password)) score += 10\r\n      if (/[A-Z]/.test(password)) score += 15\r\n      if (/[0-9]/.test(password)) score += 10\r\n      if (/[^a-zA-Z0-9]/.test(password)) score += 15\r\n      \r\n      // 多样性评分\r\n      const uniqueChars = new Set(password.split('')).size\r\n      score += Math.min(uniqueChars * 2, 20)\r\n      \r\n      // 设置最终强度\r\n      passwordStrength.value = Math.min(score, 100)\r\n    }\r\n    \r\n    // 密码强度文本\r\n    const passwordStrengthText = computed(() => {\r\n      if (passwordStrength.value >= 80) return '很强'\r\n      if (passwordStrength.value >= 60) return '强'\r\n      if (passwordStrength.value >= 40) return '中等'\r\n      if (passwordStrength.value > 0) return '弱'\r\n      return '无'\r\n    })\r\n    \r\n    // 密码强度样式类\r\n    const passwordStrengthClass = computed(() => {\r\n      if (passwordStrength.value >= 80) return 'strength-very-strong'\r\n      if (passwordStrength.value >= 60) return 'strength-strong'\r\n      if (passwordStrength.value >= 40) return 'strength-medium'\r\n      return 'strength-weak'\r\n    })\r\n    \r\n    // 登录设备列表\r\n    const devices = ref([\r\n      {\r\n        id: '1',\r\n        name: '当前浏览器',\r\n        type: 'desktop',\r\n        location: '中国，北京',\r\n        lastActive: '当前在线',\r\n        current: true\r\n      },\r\n      {\r\n        id: '2',\r\n        name: 'Chrome浏览器',\r\n        type: 'desktop',\r\n        location: '中国，上海',\r\n        lastActive: '2天前',\r\n        current: false\r\n      },\r\n      {\r\n        id: '3',\r\n        name: 'iPhone 13',\r\n        type: 'phone',\r\n        location: '中国，广州',\r\n        lastActive: '1周前',\r\n        current: false\r\n      }\r\n    ])\r\n    \r\n    // 已授权的应用\r\n    const authorizedApps = ref([\r\n      {\r\n        id: '1',\r\n        name: '示例应用',\r\n        icon: 'apps',\r\n        icon_color: '#2080f0',\r\n        scopes: ['profile', 'email'],\r\n        authorized_at: '2023-01-01'\r\n      }\r\n    ])\r\n    \r\n    // 主题设置\r\n    const app = inject('$$app') || {};\r\n    const themeMode = app.themeMode || ref('system')\r\n    const setTheme = app.setTheme || (()=>{})\r\n    \r\n    // 获取设备图标\r\n    const getDeviceIcon = (type) => {\r\n      const icons = {\r\n        desktop: Desktop,\r\n        phone: Phone,\r\n        tablet: Tablet,\r\n        laptop: Laptop\r\n      }\r\n      return icons[type] || Desktop\r\n    }\r\n    \r\n    // 获取应用图标\r\n    const getAppIcon = (iconName) => {\r\n      const iconMap = {\r\n        'apps': Apps,\r\n        'briefcase': Briefcase,\r\n        'chat': Chatbubbles,\r\n        'cloud': Cloud,\r\n        'globe': Globe,\r\n        'grid': Grid,\r\n        'layers': Layers,\r\n        'chart': PieChart,\r\n        'server': Server\r\n      }\r\n      return iconMap[iconName] || Apps\r\n    }\r\n    \r\n    // 获取授权范围名称\r\n    const getScopeName = (scope) => {\r\n      const scopeNames = {\r\n        'profile': '基本信息',\r\n        'email': '邮箱',\r\n        'phone': '电话',\r\n        'address': '地址'\r\n      }\r\n      return scopeNames[scope] || scope\r\n    }\r\n    \r\n    // 加载用户安全信息\r\n    const loadSecurityInfo = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await userStore.fetchUserProfile()\r\n        const userData = response.user\r\n        \r\n        // 更新用户数据\r\n        Object.assign(user, {\r\n          id: userData.id,\r\n          username: userData.username,\r\n          email: userData.email,\r\n          status: userData.status,\r\n          emailVerified: userData.status === 'active',\r\n          phoneVerified: !!userData.phone_verified,\r\n          twoFactorEnabled: !!userData.two_factor_enabled\r\n        })\r\n        \r\n        // 加载设备列表\r\n        // TODO: 实现加载设备列表的API\r\n        \r\n        // 加载已授权应用\r\n        // TODO: 实现加载已授权应用的API\r\n      } catch (error) {\r\n        message.error('获取安全信息失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 修改密码\r\n    const handleChangePassword = () => {\r\n      passwordFormRef.value?.validate(async (errors) => {\r\n        if (!errors) {\r\n          submittingPassword.value = true\r\n          try {\r\n            await userStore.updatePassword({\r\n              old_password: passwordForm.oldPassword,\r\n              new_password: passwordForm.newPassword\r\n            })\r\n            \r\n            message.success('密码修改成功')\r\n            \r\n            // 清空表单\r\n            passwordForm.oldPassword = ''\r\n            passwordForm.newPassword = ''\r\n            passwordForm.confirmPassword = ''\r\n            passwordStrength.value = 0\r\n          } catch (error) {\r\n            message.error(error.response?.data?.message || '密码修改失败')\r\n          } finally {\r\n            submittingPassword.value = false\r\n          }\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 验证邮箱\r\n    const handleVerifyEmail = async () => {\r\n      try {\r\n        await axios.post('/auth/send-verification-email')\r\n        message.success('验证邮件已发送，请查收')\r\n      } catch (error) {\r\n        message.error(error.response?.data?.message || '发送验证邮件失败')\r\n      }\r\n    }\r\n    \r\n    // 验证手机\r\n    const handleVerifyPhone = async () => {\r\n      message.info('手机验证功能暂未实现')\r\n    }\r\n    \r\n    // 设置两步验证\r\n    const handleSetupTwoFactor = async () => {\r\n      message.info('两步验证功能暂未实现')\r\n    }\r\n    \r\n    // 禁用两步验证\r\n    const handleDisableTwoFactor = async () => {\r\n      message.info('两步验证功能暂未实现')\r\n    }\r\n    \r\n    // 退出设备\r\n    const handleLogoutDevice = async (deviceId) => {\r\n      message.info(`退出设备 ${deviceId} 功能暂未实现`)\r\n    }\r\n    \r\n    // 退出所有其他设备\r\n    const handleLogoutAllDevices = async () => {\r\n      message.info('退出所有其他设备功能暂未实现')\r\n    }\r\n    \r\n    // 撤销应用授权\r\n    const handleRevokeAccess = async (appId) => {\r\n      message.info(`撤销应用 ${appId} 授权功能暂未实现`)\r\n    }\r\n    \r\n    // 显示删除确认对话框\r\n    const showDeleteConfirm = () => {\r\n      showDeleteModal.value = true\r\n      deleteConfirmText.value = ''\r\n    }\r\n    \r\n    // 删除账户\r\n    const handleDeleteAccount = async () => {\r\n      if (!deleteConfirmText.value) {\r\n        message.error('请输入密码以确认删除')\r\n        return\r\n      }\r\n      \r\n      try {\r\n        await axios.post('/users/delete-account', {\r\n          password: deleteConfirmText.value\r\n        })\r\n        \r\n        message.success('账户已删除')\r\n        userStore.logout()\r\n        router.push('/')\r\n      } catch (error) {\r\n        message.error(error.response?.data?.message || '删除账户失败')\r\n      }\r\n    }\r\n    \r\n    onMounted(() => {\r\n      loadSecurityInfo()\r\n    })\r\n    \r\n    return {\r\n      loading,\r\n      user,\r\n      passwordForm,\r\n      passwordFormRef,\r\n      passwordRules,\r\n      devices,\r\n      authorizedApps,\r\n      submittingPassword,\r\n      showDeleteModal,\r\n      deleteConfirmText,\r\n      passwordStrength,\r\n      passwordStrengthText,\r\n      passwordStrengthClass,\r\n      updatePasswordStrength,\r\n      getDeviceIcon,\r\n      getAppIcon,\r\n      getScopeName,\r\n      handleChangePassword,\r\n      handleVerifyEmail,\r\n      handleVerifyPhone,\r\n      handleSetupTwoFactor,\r\n      handleDisableTwoFactor,\r\n      handleLogoutDevice,\r\n      handleLogoutAllDevices,\r\n      handleRevokeAccess,\r\n      showDeleteConfirm,\r\n      handleDeleteAccount,\r\n      themeMode,\r\n      setTheme\r\n    }\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.security-container {\r\n  width: 100%;\r\n}\r\n\r\n.security-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 24px;\r\n}\r\n\r\n.security-card {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.form-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 24px;\r\n}\r\n\r\n/* 密码强度指示器 */\r\n.password-strength {\r\n  margin-top: 8px;\r\n}\r\n\r\n.password-strength-bar {\r\n  height: 4px;\r\n  border-radius: 2px;\r\n  background-color: #f0f0f0;\r\n  overflow: hidden;\r\n  margin-top: 4px;\r\n}\r\n\r\n.password-strength-progress {\r\n  height: 100%;\r\n  transition: width 0.3s, background-color 0.3s;\r\n}\r\n\r\n.password-strength-text {\r\n  font-size: 12px;\r\n  margin-top: 4px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n\r\n.password-strength-label {\r\n  color: var(--text-color-3);\r\n}\r\n\r\n.password-strength-value {\r\n  font-weight: 500;\r\n}\r\n\r\n.strength-weak {\r\n  color: #f56c6c;\r\n  background-color: #f56c6c;\r\n}\r\n\r\n.strength-medium {\r\n  color: #e6a23c;\r\n  background-color: #e6a23c;\r\n}\r\n\r\n.strength-strong {\r\n  color: #2080f0;\r\n  background-color: #2080f0;\r\n}\r\n\r\n.strength-very-strong {\r\n  color: #67c23a;\r\n  background-color: #67c23a;\r\n}\r\n\r\n.security-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.security-status {\r\n  color: var(--text-color-2);\r\n}\r\n\r\n.device-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n}\r\n\r\n.device-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  background-color: var(--hover-color);\r\n}\r\n\r\n.device-icon {\r\n  width: 48px;\r\n  height: 48px;\r\n  border-radius: 8px;\r\n  background-color: rgba(32, 128, 240, 0.1);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.device-info {\r\n  flex: 1;\r\n}\r\n\r\n.device-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.device-meta {\r\n  font-size: 14px;\r\n  color: var(--text-color-3);\r\n  display: flex;\r\n  gap: 12px;\r\n  align-items: center;\r\n}\r\n\r\n.app-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 12px;\r\n}\r\n\r\n.app-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 16px;\r\n  padding: 12px;\r\n  border-radius: 8px;\r\n  background-color: var(--hover-color);\r\n}\r\n\r\n.app-icon {\r\n  width: 40px;\r\n  height: 40px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.app-info {\r\n  flex: 1;\r\n}\r\n\r\n.app-name {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.app-scopes {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 8px;\r\n}\r\n\r\n.scope-tag {\r\n  margin: 0;\r\n}\r\n\r\n.danger-card {\r\n  border: 1px solid rgba(245, 108, 108, 0.2);\r\n}\r\n\r\n.danger-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 16px 0;\r\n}\r\n\r\n.danger-info {\r\n  flex: 1;\r\n}\r\n\r\n.danger-title {\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n  margin-bottom: 4px;\r\n  color: #f56c6c;\r\n}\r\n\r\n.danger-description {\r\n  font-size: 14px;\r\n  color: var(--text-color-3);\r\n}\r\n\r\n.theme-options {\r\n  padding: 16px 0;\r\n}\r\n\r\n.theme-radio-group {\r\n  width: 100%;\r\n}\r\n\r\n.theme-space {\r\n  width: 100%;\r\n}\r\n\r\n.theme-option {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.theme-preview {\r\n  width: 80px;\r\n  height: 60px;\r\n  border-radius: 8px;\r\n  border: 1px solid var(--divider-color);\r\n  overflow: hidden;\r\n  position: relative;\r\n}\r\n\r\n.theme-preview::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 16px;\r\n}\r\n\r\n.light-theme::before {\r\n  background-color: #fff;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.light-theme {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.dark-theme::before {\r\n  background-color: #333;\r\n  border-bottom: 1px solid #444;\r\n}\r\n\r\n.dark-theme {\r\n  background-color: #18181c;\r\n}\r\n\r\n.system-theme {\r\n  background: linear-gradient(to right, #f5f7fa 50%, #18181c 50%);\r\n}\r\n\r\n.system-theme::before {\r\n  background: linear-gradient(to right, #fff 50%, #333 50%);\r\n  border-bottom: 1px solid #ddd;\r\n}\r\n\r\n/* 深色模式适配 */\r\n:deep(.n-config-provider.n-config-provider--dark) .device-item {\r\n  background-color: rgba(255, 255, 255, 0.08);\r\n}\r\n\r\n:deep(.n-config-provider.n-config-provider--dark) .app-item {\r\n  background-color: rgba(255, 255, 255, 0.08);\r\n}\r\n\r\n:deep(.n-config-provider.n-config-provider--dark) .device-icon {\r\n  background-color: rgba(32, 128, 240, 0.2);\r\n}\r\n\r\n:deep(.n-config-provider.n-config-provider--dark) .danger-card {\r\n  border: 1px solid rgba(245, 108, 108, 0.3);\r\n}\r\n\r\n:deep(.n-config-provider.n-config-provider--dark) .danger-title {\r\n  color: #f56c6c;\r\n}\r\n\r\n:deep(.n-config-provider.n-config-provider--dark) .password-strength-bar {\r\n  background-color: rgba(255, 255, 255, 0.12);\r\n}\r\n\r\n/* 表单样式优化 */\r\n:deep(.n-form-item-label) {\r\n  font-weight: 500;\r\n  color: var(--text-color-2);\r\n}\r\n\r\n:deep(.n-input) {\r\n  border-radius: 8px;\r\n}\r\n\r\n:deep(.n-input-wrapper) {\r\n  border-radius: 8px;\r\n}\r\n\r\n/* 响应式调整 */\r\n@media (max-width: 768px) {\r\n  .theme-space {\r\n    flex-direction: column;\r\n    gap: 24px;\r\n  }\r\n  \r\n  .danger-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 16px;\r\n  }\r\n  \r\n  .security-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 12px;\r\n  }\r\n  \r\n  .device-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .device-actions {\r\n    align-self: flex-end;\r\n  }\r\n  \r\n  .app-item {\r\n    flex-direction: column;\r\n    align-items: flex-start;\r\n  }\r\n  \r\n  .app-actions {\r\n    align-self: flex-end;\r\n  }\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAEtBA,KAAK,EAAC;AAAkB;;;EA2BhBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAuB;;EAO7BA,KAAK,EAAC;AAAwB;;EAgBlCA,KAAK,EAAC;AAAc;;EAcdA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAiB;;EAkB1BA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAiB;;EAkB1BA,KAAK,EAAC;AAAe;;EAClBA,KAAK,EAAC;AAAiB;;EA4BlCA,KAAK,EAAC;AAAa;;EAEfA,KAAK,EAAC;AAAa;;EAKnBA,KAAK,EAAC;AAAa;;EACjBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAgB;;;EAwBnBA,KAAK,EAAC;;;EAOTA,KAAK,EAAC;AAAU;;EACdA,KAAK,EAAC;AAAU;;EAChBA,KAAK,EAAC;AAAY;;EAMpBA,KAAK,EAAC;AAAa;;EAWvBA,KAAK,EAAC;AAAe;;EAGfA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAYxBA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;;;;;uBA9NlCC,mBAAA,CAkQM,OAlQNC,UAkQM,GAjQJC,YAAA,CA2OSC,iBAAA;IA3OAC,IAAI,EAAEC,IAAA,CAAAC;EAAO;sBACpB,MAyOM,CAzONC,mBAAA,CAyOM,OAzONC,UAyOM,GAxOJC,mBAAA,YAAe,EACfP,YAAA,CAuDSQ,iBAAA;MAvDDC,KAAK,EAAC,MAAM;MAACZ,KAAK,EAAC;;wBACzB,MAqDS,CArDTG,YAAA,CAqDSU,iBAAA;QApDPC,GAAG,EAAC,iBAAiB;QACpBC,KAAK,EAAET,IAAA,CAAAU,YAAY;QACnBC,KAAK,EAAEX,IAAA,CAAAY,aAAa;QACrB,iBAAe,EAAC,MAAM;QACtB,aAAW,EAAC;;0BAEZ,MAOc,CAPdf,YAAA,CAOcgB,sBAAA;UAPDC,IAAI,EAAC,aAAa;UAACC,KAAK,EAAC;;4BACpC,MAKE,CALFlB,YAAA,CAKEmB,kBAAA;YAJQC,KAAK,EAAEjB,IAAA,CAAAU,YAAY,CAACQ,WAAW;kEAAxBlB,IAAA,CAAAU,YAAY,CAACQ,WAAW,GAAAC,MAAA;YACvCC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,SAAS;YACrB,kBAAgB,EAAC;;;YAIrBxB,YAAA,CAqBcgB,sBAAA;UArBDC,IAAI,EAAC,aAAa;UAACC,KAAK,EAAC;;4BACpC,MAME,CANFlB,YAAA,CAMEmB,kBAAA;YALQC,KAAK,EAAEjB,IAAA,CAAAU,YAAY,CAACY,WAAW;mEAAxBtB,IAAA,CAAAU,YAAY,CAACY,WAAW,GAAAH,MAAA,GAIxBnB,IAAA,CAAAuB,sBAAsB,C;YAHrCH,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,QAAQ;YACpB,kBAAgB,EAAC;gEAGkBrB,IAAA,CAAAU,YAAY,CAACY,WAAW,I,cAA7D3B,mBAAA,CAYM,OAZN6B,UAYM,GAXJtB,mBAAA,CAMM,OANNuB,UAMM,GALJvB,mBAAA,CAIO;YAHLR,KAAK,EAAAgC,eAAA,EAAC,4BAA4B,EAC1B1B,IAAA,CAAA2B,qBAAqB;YAC5BC,KAAK,EAAAC,eAAA;cAAAC,KAAA,KAAc9B,IAAA,CAAA+B,gBAAgB;YAAA;4CAGxC7B,mBAAA,CAGM,OAHN8B,UAGM,G,0BAFJ9B,mBAAA,CAAiD;YAA3CR,KAAK,EAAC;UAAyB,GAAC,MAAI,qBAC1CQ,mBAAA,CAAsG;YAAhGR,KAAK,EAAAgC,eAAA,EAAC,yBAAyB,EAAS1B,IAAA,CAAA2B,qBAAqB;8BAAK3B,IAAA,CAAAiC,oBAAoB,wB;;YAKlGpC,YAAA,CAOcgB,sBAAA;UAPDC,IAAI,EAAC,iBAAiB;UAACC,KAAK,EAAC;;4BACxC,MAKE,CALFlB,YAAA,CAKEmB,kBAAA;YAJQC,KAAK,EAAEjB,IAAA,CAAAU,YAAY,CAACwB,eAAe;kEAA5BlC,IAAA,CAAAU,YAAY,CAACwB,eAAe,GAAAf,MAAA;YAC3CC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC,UAAU;YACtB,kBAAgB,EAAC;;;YAIrBnB,mBAAA,CAIM,OAJNiC,UAIM,GAHJtC,YAAA,CAEWuC,mBAAA;UAFDhB,IAAI,EAAC,SAAS;UAAEnB,OAAO,EAAED,IAAA,CAAAqC,kBAAkB;UAAGC,OAAK,EAAEtC,IAAA,CAAAuC;;4BAAsB,MAErFC,MAAA,QAAAA,MAAA,O,iBAFqF,QAErF,E;;;;;;;QAKNpC,mBAAA,YAAe,EACfP,YAAA,CAoESQ,iBAAA;MApEDC,KAAK,EAAC,MAAM;MAACZ,KAAK,EAAC;;wBACzB,MAkES,CAlETG,YAAA,CAkES4C,iBAAA;0BAjEP,MAiBc,CAjBd5C,YAAA,CAiBc6C,sBAAA;4BAhBZ,MAeU,CAfV7C,YAAA,CAeU8C,kBAAA;YAfDrC,KAAK,EAAC,MAAM;YAAC,eAAa,EAAC;;YACvBsC,WAAW,EAAAC,QAAA,CACpB,MAWM,CAXN3C,mBAAA,CAWM,OAXN4C,UAWM,GAVJ5C,mBAAA,CAA6E,QAA7E6C,UAA6E,EAAAC,gBAAA,CAA5ChD,IAAA,CAAAiD,IAAI,CAACC,aAAa,kC,CAK1ClD,IAAA,CAAAiD,IAAI,CAACC,aAAa,I,cAJ3BC,YAAA,CAQWf,mBAAA;;cAPTgB,IAAI,EAAC,OAAO;cACZhC,IAAI,EAAC,SAAS;cACdiC,KAAK,EAAL,EAAK;cAEJf,OAAK,EAAEtC,IAAA,CAAAsD;;gCACT,MAEDd,MAAA,QAAAA,MAAA,O,iBAFC,QAED,E;;;;;;;YAMR3C,YAAA,CAiBc6C,sBAAA;4BAhBZ,MAeU,CAfV7C,YAAA,CAeU8C,kBAAA;YAfDrC,KAAK,EAAC,MAAM;YAAC,eAAa,EAAC;;YACvBsC,WAAW,EAAAC,QAAA,CACpB,MAWM,CAXN3C,mBAAA,CAWM,OAXNqD,UAWM,GAVJrD,mBAAA,CAA6E,QAA7EsD,WAA6E,EAAAR,gBAAA,CAA5ChD,IAAA,CAAAiD,IAAI,CAACQ,aAAa,kC,CAK1CzD,IAAA,CAAAiD,IAAI,CAACQ,aAAa,I,cAJ3BN,YAAA,CAQWf,mBAAA;;cAPTgB,IAAI,EAAC,OAAO;cACZhC,IAAI,EAAC,SAAS;cACdiC,KAAK,EAAL,EAAK;cAEJf,OAAK,EAAEtC,IAAA,CAAA0D;;gCACT,MAEDlB,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;;;;YAMR3C,YAAA,CA0Bc6C,sBAAA;4BAzBZ,MAwBU,CAxBV7C,YAAA,CAwBU8C,kBAAA;YAxBDrC,KAAK,EAAC,MAAM;YAAC,eAAa,EAAC;;YACvBsC,WAAW,EAAAC,QAAA,CACpB,MAoBM,CApBN3C,mBAAA,CAoBM,OApBNyD,WAoBM,GAnBJzD,mBAAA,CAAgF,QAAhF0D,WAAgF,EAAAZ,gBAAA,CAA/ChD,IAAA,CAAAiD,IAAI,CAACY,gBAAgB,kC,CAK7C7D,IAAA,CAAAiD,IAAI,CAACY,gBAAgB,I,cAJ9BV,YAAA,CAQWf,mBAAA;;cAPTgB,IAAI,EAAC,OAAO;cACZhC,IAAI,EAAC,SAAS;cACdiC,KAAK,EAAL,EAAK;cAEJf,OAAK,EAAEtC,IAAA,CAAA8D;;gCACT,MAEDtB,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;6DACAW,YAAA,CAQWf,mBAAA;;cAPTgB,IAAI,EAAC,OAAO;cACZhC,IAAI,EAAC,OAAO;cACZiC,KAAK,EAAL,EAAK;cAEJf,OAAK,EAAEtC,IAAA,CAAA+D;;gCACT,MAEDvB,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;;;;;;;;;QAQZpC,mBAAA,YAAe,EACfP,YAAA,CAkCSQ,iBAAA;MAlCDC,KAAK,EAAC,MAAM;MAACZ,KAAK,EAAC;;MA6BdsE,MAAM,EAAAnB,QAAA,CACf,MAEW,CAFXhD,YAAA,CAEWuC,mBAAA;QAFD6B,KAAK,EAAL,EAAK;QAAC7C,IAAI,EAAC,SAAS;QAAEkB,OAAK,EAAEtC,IAAA,CAAAkE;;0BAAwB,MAE/D1B,MAAA,SAAAA,MAAA,Q,iBAF+D,YAE/D,E;;;;wBA/BF,MA0BM,CA1BNtC,mBAAA,CA0BM,OA1BNiE,WA0BM,I,kBAzBJxE,mBAAA,CAwBMyE,SAAA,QAAAC,WAAA,CAxByBrE,IAAA,CAAAsE,OAAO,GAAzBC,MAAM,EAAEC,KAAK;6BAA1B7E,mBAAA,CAwBM;UAxBmC8E,GAAG,EAAED,KAAK;UAAE9E,KAAK,EAAC;YACzDQ,mBAAA,CAIM,OAJNwE,WAIM,GAHJ7E,YAAA,CAES8E,iBAAA;UAFDvB,IAAI,EAAC;QAAI;4BACf,MAA8C,E,cAA9CD,YAAA,CAA8CyB,wBAAA,CAA9B5E,IAAA,CAAA6E,aAAa,CAACN,MAAM,CAACnD,IAAI,K;;wCAG7ClB,mBAAA,CAOM,OAPN4E,WAOM,GANJ5E,mBAAA,CAAgD,OAAhD6E,WAAgD,EAAA/B,gBAAA,CAApBuB,MAAM,CAACS,IAAI,kBACvC9E,mBAAA,CAIM,OAJN+E,WAIM,GAHJ/E,mBAAA,CAAkC,cAAA8C,gBAAA,CAAzBuB,MAAM,CAACW,QAAQ,kBACxBhF,mBAAA,CAAoC,cAAA8C,gBAAA,CAA3BuB,MAAM,CAACY,UAAU,kBACbZ,MAAM,CAACa,OAAO,I,cAA3BjC,YAAA,CAAqEkC,gBAAA;;UAAxCjE,IAAI,EAAC,SAAS;UAACgC,IAAI,EAAC;;4BAAQ,MAAI,KAAAZ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;qDAGjEtC,mBAAA,CASM,OATNoF,WASM,G,CAPKf,MAAM,CAACa,OAAO,I,cADvBjC,YAAA,CAOWf,mBAAA;;UALThB,IAAI,EAAC,OAAO;UACZmE,IAAI,EAAJ,EAAI;UACHjD,OAAK,EAAAnB,MAAA,IAAEnB,IAAA,CAAAwF,kBAAkB,CAACjB,MAAM,CAACkB,EAAE;;4BACrC,MAED,KAAAjD,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;;;QAYRpC,mBAAA,YAAe,EACfP,YAAA,CAyBSQ,iBAAA;MAzBDC,KAAK,EAAC,QAAQ;MAACZ,KAAK,EAAC;;wBANxB,MACyB,CAMbM,IAAA,CAAA0F,cAAc,CAACC,MAAM,U,cAApCxC,YAAA,CAAmEyC,kBAAA;;QAAvBhD,WAAW,EAAC;2BAExDjD,mBAAA,CAqBM,OArBNkG,WAqBM,I,kBApBJlG,mBAAA,CAmBMyE,SAAA,QAAAC,WAAA,CAnBarE,IAAA,CAAA0F,cAAc,EAArBI,GAAG;6BAAfnG,mBAAA,CAmBM;UAnB8B8E,GAAG,EAAEqB,GAAG,CAACL,EAAE;UAAE/F,KAAK,EAAC;YACrDQ,mBAAA,CAIM;UAJDR,KAAK,EAAC,UAAU;UAAEkC,KAAK,EAAAC,eAAA;YAAAkE,eAAA,EAAqBD,GAAG,CAACE,UAAU;UAAA;YAC7DnG,YAAA,CAES8E,iBAAA;UAFDvB,IAAI,EAAC,IAAI;UAAE6C,KAAK,EAAEH,GAAG,CAACE;;4BAC5B,MAAwC,E,cAAxC7C,YAAA,CAAwCyB,wBAAA,CAAxB5E,IAAA,CAAAkG,UAAU,CAACJ,GAAG,CAACK,IAAI,K;;yEAGvCjG,mBAAA,CAOM,OAPNkG,WAOM,GANJlG,mBAAA,CAA0C,OAA1CmG,WAA0C,EAAArD,gBAAA,CAAjB8C,GAAG,CAACd,IAAI,kBACjC9E,mBAAA,CAIM,OAJNoG,WAIM,I,kBAHJ3G,mBAAA,CAEQyE,SAAA,QAAAC,WAAA,CAFeyB,GAAG,CAACS,MAAM,EAAnBC,KAAK;+BAAnBrD,YAAA,CAEQkC,gBAAA;YAF4BZ,GAAG,EAAE+B,KAAK;YAAEpD,IAAI,EAAC,OAAO;YAAC1D,KAAK,EAAC;;8BACjE,MAAyB,C,kCAAtBM,IAAA,CAAAyG,YAAY,CAACD,KAAK,kB;;;4CAI3BtG,mBAAA,CAIM,OAJNwG,WAIM,GAHJ7G,YAAA,CAEWuC,mBAAA;UAFDhB,IAAI,EAAC,OAAO;UAACmE,IAAI,EAAJ,EAAI;UAAEjD,OAAK,EAAAnB,MAAA,IAAEnB,IAAA,CAAA2G,kBAAkB,CAACb,GAAG,CAACL,EAAE;;4BAAG,MAEhE,KAAAjD,MAAA,SAAAA,MAAA,Q,iBAFgE,QAEhE,E;;;;;;QAMRpC,mBAAA,YAAe,EACfP,YAAA,CAmBSQ,iBAAA;MAnBDC,KAAK,EAAC,MAAM;MAACZ,KAAK,EAAC;;wBACzB,MAiBM,CAjBNQ,mBAAA,CAiBM,OAjBN0G,WAiBM,GAhBJ/G,YAAA,CAegBgH,wBAAA;QAfO5F,KAAK,EAAEjB,IAAA,CAAA8G,SAAS;+DAAT9G,IAAA,CAAA8G,SAAS,GAAA3F,MAAA,GAAiBnB,IAAA,CAAA+G,QAAQ,C;QAAErH,KAAK,EAAC;;0BACtE,MAaU,CAbVG,YAAA,CAaUmH,kBAAA;UAbDC,OAAO,EAAC,cAAc;UAACC,KAAK,EAAC,QAAQ;UAACxH,KAAK,EAAC;;4BACnD,MAGM,CAHNQ,mBAAA,CAGM,OAHNiH,WAGM,G,4BAFJjH,mBAAA,CAA8C;YAAzCR,KAAK,EAAC;UAA4B,4BACvCG,YAAA,CAAsCuH,kBAAA;YAA7BnG,KAAK,EAAC;UAAQ;8BAAC,MAAIuB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;gBAE9BtC,mBAAA,CAGM,OAHNmH,WAGM,G,4BAFJnH,mBAAA,CAA6C;YAAxCR,KAAK,EAAC;UAA2B,4BACtCG,YAAA,CAAqCuH,kBAAA;YAA5BnG,KAAK,EAAC;UAAO;8BAAC,MAAIuB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;gBAE7BtC,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAA4C;YAAvCR,KAAK,EAAC;UAA0B,4BACrCG,YAAA,CAAoCuH,kBAAA;YAA3BnG,KAAK,EAAC;UAAM;8BAAC,MAAIuB,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;;;;;QAOpCpC,mBAAA,YAAe,EACfP,YAAA,CAcSQ,iBAAA;MAdDC,KAAK,EAAC,MAAM;MAACZ,KAAK,EAAC;;wBACzB,MAYU,CAZVG,YAAA,CAYUmH,kBAAA;QAZDO,QAAQ,EAAR;MAAQ;0BACf,MAUM,CAVNrH,mBAAA,CAUM,OAVNsH,WAUM,G,4BATJtH,mBAAA,CAKM;UALDR,KAAK,EAAC;QAAa,IACtBQ,mBAAA,CAAoC;UAA/BR,KAAK,EAAC;QAAc,GAAC,MAAI,GAC9BQ,mBAAA,CAEM;UAFDR,KAAK,EAAC;QAAoB,GAAC,2BAEhC,E,qBAEFG,YAAA,CAEWuC,mBAAA;UAFDhB,IAAI,EAAC,OAAO;UAAEkB,OAAK,EAAEtC,IAAA,CAAAyH;;4BAAmB,MAElDjF,MAAA,SAAAA,MAAA,Q,iBAFkD,QAElD,E;;;;;;;;;+BAOVpC,mBAAA,eAAkB,EAClBP,YAAA,CAkBU6H,kBAAA;IAjBA3H,IAAI,EAAEC,IAAA,CAAA2H,eAAe;yDAAf3H,IAAA,CAAA2H,eAAe,GAAAxG,MAAA;IAC7ByG,MAAM,EAAC,QAAQ;IACftH,KAAK,EAAC,MAAM;IACZ,eAAa,EAAC,MAAM;IACpB,eAAa,EAAC,IAAI;IAClBc,IAAI,EAAC,OAAO;IACXyG,eAAc,EAAE7H,IAAA,CAAA8H,mBAAmB;IACnCC,eAAc,EAAAvF,MAAA,QAAAA,MAAA,MAAArB,MAAA,IAAEnB,IAAA,CAAA2H,eAAe;;IAErBK,OAAO,EAAAnF,QAAA,CAChB,MAAqC,C,4BAArC3C,mBAAA,CAAqC,WAAlC,gCAA8B,qBACjCL,YAAA,CAIEmB,kBAAA;MAHQC,KAAK,EAAEjB,IAAA,CAAAiI,iBAAiB;4DAAjBjI,IAAA,CAAAiI,iBAAiB,GAAA9G,MAAA;MAChCE,WAAW,EAAC,cAAc;MAC1BD,IAAI,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}