const axios = require('axios');
const aliyunConfig = require('./config/aliyun');

async function testAliyunAPI() {
  try {
    console.log('=== 阿里云二要素验证API测试 ===\n');
    
    // 1. 验证配置
    console.log('1. 验证API配置...');
    const apiConfig = aliyunConfig.getIdVerificationConfig();
    
    console.log('API配置信息:');
    console.log('- AppKey:', apiConfig.appKey);
    console.log('- AppCode:', apiConfig.appCode.substring(0, 8) + '...');
    console.log('- API URL:', apiConfig.apiUrl);
    console.log('- 超时时间:', apiConfig.timeout + 'ms');
    console.log('- 使用真实API:', aliyunConfig.shouldUseRealAPI());
    
    try {
      aliyunConfig.validateConfig();
      console.log('✓ 配置验证通过\n');
    } catch (error) {
      console.error('✗ 配置验证失败:', error.message);
      return;
    }
    
    // 2. 测试API连接
    console.log('2. 测试API连接...');
    
    // 使用测试数据（这些是公开的测试数据，不会泄露真实信息）
    const testDataSets = [
      {
        name: '张三',
        idcard: '110101199003077777',
        desc: '标准测试数据'
      },
      {
        realname: '张三',
        idcard: '110101199003077777',
        desc: '使用realname字段'
      }
    ];

    // 尝试不同的API URL
    const urlsToTest = aliyunConfig.ALIYUN_ID_VERIFICATION.apiUrls;

    let success = false;

    for (let urlIndex = 0; urlIndex < urlsToTest.length && !success; urlIndex++) {
      const currentUrl = urlsToTest[urlIndex];
      console.log(`\n尝试API URL ${urlIndex + 1}/${urlsToTest.length}: ${currentUrl}`);

      for (let dataIndex = 0; dataIndex < testDataSets.length && !success; dataIndex++) {
        const testData = testDataSets[dataIndex];
        console.log(`\n测试数据格式 ${dataIndex + 1} (${testData.desc}):`);
        console.log('- 参数:', Object.keys(testData).filter(k => k !== 'desc').join(', '));

        const requestData = { ...testData };
        delete requestData.desc;

        const startTime = Date.now();

        try {
          const response = await axios.post(currentUrl, requestData, {
            headers: {
              'Authorization': `APPCODE ${apiConfig.appCode}`,
              'Content-Type': 'application/json; charset=UTF-8'
            },
            timeout: apiConfig.timeout
          });
      
          const endTime = Date.now();
          const responseTime = endTime - startTime;

          console.log('\n✓ API调用成功');
          console.log('- 响应时间:', responseTime + 'ms');
          console.log('- HTTP状态:', response.status);
          console.log('- 响应数据:', JSON.stringify(response.data, null, 2));

          // 解析响应结果
          if (response.data) {
            const { code, message, data } = response.data;

            console.log('\n响应解析:');
            console.log('- 业务代码:', code);
            console.log('- 业务消息:', message);

            if (data) {
              console.log('- 验证结果:', data.result === '1' ? '通过' : '不通过');
              console.log('- 结果描述:', data.desc);
            }

            if (code === 200) {
              console.log('✓ API调用业务成功');
              success = true;

              // 更新配置文件中的正确URL
              console.log(`\n找到可用的API配置:`);
              console.log(`- URL: ${currentUrl}`);
              console.log(`- 参数格式: ${Object.keys(requestData).join(', ')}`);

            } else {
              console.log('✗ API调用业务失败');
            }
          }
      
        } catch (error) {
          const endTime = Date.now();
          const responseTime = endTime - startTime;

          console.log('\n✗ API调用失败');
          console.log('- 响应时间:', responseTime + 'ms');

          if (error.response) {
            console.log('- HTTP状态:', error.response.status);
            console.log('- 错误响应:', JSON.stringify(error.response.data, null, 2));

            // 分析常见错误
            if (error.response.status === 400) {
              console.log('分析: 请求参数错误，尝试下一个参数格式');
            } else if (error.response.status === 401) {
              console.log('分析: 认证失败，请检查AppCode是否正确');
            } else if (error.response.status === 403) {
              console.log('分析: 权限不足，请检查API权限配置');
            } else if (error.response.status === 404) {
              console.log('分析: API路径不存在，尝试下一个URL');
            } else if (error.response.status === 429) {
              console.log('分析: 请求频率过高，请稍后重试');
            } else if (error.response.status >= 500) {
              console.log('分析: 服务器错误，尝试下一个URL');
            }
          } else if (error.code === 'ECONNABORTED') {
            console.log('- 错误类型: 请求超时');
            console.log('分析: 网络连接超时，尝试下一个URL');
          } else {
            console.log('- 错误类型:', error.code || error.name);
            console.log('- 错误信息:', error.message);
          }
        }
      }
    }

    if (!success) {
      console.log('\n✗ 所有API URL和参数格式都测试失败');
      console.log('建议: 请检查API文档或联系服务提供商');
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
testAliyunAPI().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('测试失败:', error);
  process.exit(1);
});
