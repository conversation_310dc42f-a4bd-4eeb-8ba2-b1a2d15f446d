{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { defineComponent, ref, computed, h } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { NLayout, NLayoutHeader, NLayoutContent, NMenu, NBreadcrumb, NBreadcrumbItem, NAvatar, NDropdown, NIcon, NButton, NSpace, NDrawer, NDrawerContent, useMessage } from 'naive-ui';\nimport { HomeOutline, PersonOutline, ShieldOutline, AppsOutline, LogOutOutline, ChevronDown, MenuOutline, IdCardOutline } from '@vicons/ionicons5';\nimport { useUserStore } from '../stores/user';\nfunction renderIcon(icon) {\n  return () => h(NIcon, null, {\n    default: () => h(icon)\n  });\n}\nexport default defineComponent({\n  name: 'AppLayout',\n  components: {\n    NLayout,\n    NLayoutHeader,\n    NLayoutContent,\n    NMenu,\n    NBreadcrumb,\n    NBreadcrumbItem,\n    NAvatar,\n    NDropdown,\n    NIcon,\n    NButton,\n    NSpace,\n    NDrawer,\n    NDrawerContent,\n    ChevronDown,\n    MenuOutline,\n    IdCardOutline\n  },\n  setup() {\n    const router = useRouter();\n    const route = useRoute();\n    const message = useMessage();\n    const userStore = useUserStore();\n    const mobileMenuVisible = ref(false);\n\n    // 用户头像和用户名\n    const userAvatar = computed(() => userStore.user?.avatar || '');\n    const username = computed(() => userStore.user?.nickname || userStore.user?.username || '用户');\n\n    // 当前激活的菜单项\n    const activeKey = computed(() => {\n      const path = route.path;\n      if (path.startsWith('/dashboard')) return 'dashboard';\n      if (path.startsWith('/profile')) return 'profile';\n      if (path.startsWith('/security')) return 'security';\n      if (path.startsWith('/applications')) return 'applications';\n      if (path.startsWith('/verification')) return 'verification';\n      return '';\n    });\n\n    // 面包屑导航\n    const breadcrumbItems = computed(() => {\n      const path = route.path;\n      const items = ['首页'];\n      if (path.startsWith('/dashboard')) {\n        items.push('仪表盘');\n      } else if (path.startsWith('/profile')) {\n        items.push('个人资料');\n      } else if (path.startsWith('/security')) {\n        items.push('安全设置');\n      } else if (path.startsWith('/applications')) {\n        items.push('应用管理');\n        if (path.includes('/create')) {\n          items.push('创建应用');\n        } else if (path.match(/\\/applications\\/[^/]+$/)) {\n          items.push('应用详情');\n        }\n      } else if (path.startsWith('/verification')) {\n        items.push('实名认证');\n      }\n      return items;\n    });\n\n    // 顶部菜单选项\n    const menuOptions = [{\n      label: '仪表盘',\n      key: 'dashboard',\n      icon: renderIcon(HomeOutline)\n    }, {\n      label: '个人资料',\n      key: 'profile',\n      icon: renderIcon(PersonOutline)\n    }, {\n      label: '安全设置',\n      key: 'security',\n      icon: renderIcon(ShieldOutline)\n    }, {\n      label: '实名认证',\n      key: 'verification',\n      icon: renderIcon(IdCardOutline)\n    }, {\n      label: '应用管理',\n      key: 'applications',\n      icon: renderIcon(AppsOutline)\n    }];\n\n    // 用户下拉菜单选项\n    const userMenuOptions = [{\n      label: userStore.user?.username || '用户',\n      key: 'username',\n      disabled: true\n    }, {\n      type: 'divider',\n      key: 'd1'\n    }, {\n      label: '个人资料',\n      key: 'profile',\n      icon: renderIcon(PersonOutline)\n    }, {\n      label: '安全设置',\n      key: 'security',\n      icon: renderIcon(ShieldOutline)\n    }, {\n      type: 'divider',\n      key: 'd2'\n    }, {\n      label: '退出登录',\n      key: 'logout',\n      icon: renderIcon(LogOutOutline)\n    }];\n\n    // 处理菜单点击\n    const handleMenuUpdate = key => {\n      switch (key) {\n        case 'dashboard':\n          router.push('/dashboard');\n          break;\n        case 'profile':\n          router.push('/profile');\n          break;\n        case 'security':\n          router.push('/security');\n          break;\n        case 'applications':\n          router.push('/applications');\n          break;\n        case 'verification':\n          router.push('/verification');\n          break;\n      }\n    };\n\n    // 处理移动端菜单点击\n    const handleMobileMenuSelect = key => {\n      handleMenuUpdate(key);\n      mobileMenuVisible.value = false;\n    };\n\n    // 处理用户菜单选择\n    const handleUserMenuSelect = key => {\n      switch (key) {\n        case 'profile':\n          router.push('/profile');\n          break;\n        case 'security':\n          router.push('/security');\n          break;\n        case 'logout':\n          handleLogout();\n          break;\n      }\n    };\n\n    // 退出登录\n    const handleLogout = async () => {\n      try {\n        await userStore.logout();\n        message.success('退出登录成功');\n        router.push('/login');\n      } catch (error) {\n        message.error('退出登录失败');\n      }\n    };\n    return {\n      userAvatar,\n      username,\n      activeKey,\n      breadcrumbItems,\n      menuOptions,\n      userMenuOptions,\n      handleMenuUpdate,\n      handleUserMenuSelect,\n      mobileMenuVisible,\n      handleMobileMenuSelect\n    };\n  }\n});", "map": {"version": 3, "names": ["defineComponent", "ref", "computed", "h", "useRouter", "useRoute", "NLayout", "NLayoutHeader", "NLayoutContent", "NMenu", "NBreadcrumb", "NBreadcrumbItem", "NAvatar", "NDropdown", "NIcon", "NButton", "NSpace", "NDrawer", "NDrawerContent", "useMessage", "HomeOutline", "PersonOutline", "ShieldOutline", "AppsOutline", "LogOutOutline", "ChevronDown", "MenuOutline", "IdCardOutline", "useUserStore", "renderIcon", "icon", "default", "name", "components", "setup", "router", "route", "message", "userStore", "mobileMenuVisible", "userAvatar", "user", "avatar", "username", "nickname", "active<PERSON><PERSON>", "path", "startsWith", "breadcrumbItems", "items", "push", "includes", "match", "menuOptions", "label", "key", "userMenuOptions", "disabled", "type", "handleMenuUpdate", "handleMobileMenuSelect", "value", "handleUserMenuSelect", "handleLogout", "logout", "success", "error"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\AppLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"app-layout\">\n    <n-layout>\n      <n-layout-header bordered class=\"layout-header\">\n        <div class=\"header-content\">\n          <div class=\"header-left\">\n            <div class=\"logo-container\">\n              <h2 class=\"logo-text\">空旷账号中心</h2>\n            </div>\n            <n-button\n              quaternary\n              circle\n              size=\"medium\"\n              class=\"mobile-menu-button\"\n              @click=\"mobileMenuVisible = true\"\n            >\n              <template #icon\n                ><n-icon><menu-outline /></n-icon\n              ></template>\n            </n-button>\n          </div>\n          \n          <div class=\"header-center\">\n            <n-menu\n              mode=\"horizontal\"\n              :options=\"menuOptions\"\n              :value=\"activeKey\"\n              @update:value=\"handleMenuUpdate\"\n              class=\"horizontal-menu\"\n              :inline-theme=\"false\"\n              :inverted=\"false\"\n              style=\"--n-item-border-radius: 16px;\"\n            />\n          </div>\n          \n          <div class=\"header-right\">\n            <n-space :size=\"16\">\n              <n-dropdown\n                :options=\"userMenuOptions\"\n                @select=\"handleUserMenuSelect\"\n                trigger=\"click\"\n              >\n                <div class=\"user-dropdown\">\n                  <n-avatar\n                    round\n                    size=\"medium\"\n                    :src=\"userAvatar || '/default-avatar.png'\"\n                    class=\"user-avatar\"\n                  />\n                  <span class=\"username\">{{ username }}</span>\n                  <n-icon class=\"dropdown-icon\"><chevron-down /></n-icon>\n                </div>\n              </n-dropdown>\n            </n-space>\n          </div>\n        </div>\n      </n-layout-header>\n\n      <n-layout-content\n        class=\"layout-main\"\n        :native-scrollbar=\"true\"\n        content-style=\"min-height: calc(100vh - 64px); padding: 0;\"\n      >\n        <n-scrollbar style=\"max-height: calc(100vh - 64px);\">\n          <div class=\"main-content-wrapper\">\n            <div class=\"sub-header\">\n              <n-breadcrumb>\n                <n-breadcrumb-item\n                  v-for=\"(item, index) in breadcrumbItems\"\n                  :key=\"index\"\n                >\n                  {{ item }}\n                </n-breadcrumb-item>\n              </n-breadcrumb>\n            </div>\n            <slot></slot>\n          </div>\n        </n-scrollbar>\n      </n-layout-content>\n    </n-layout>\n\n    <!-- 移动端菜单抽屉 -->\n    <n-drawer v-model:show=\"mobileMenuVisible\" :width=\"280\" placement=\"left\">\n      <n-drawer-content title=\"菜单\">\n        <n-menu\n          :options=\"menuOptions\"\n          :value=\"activeKey\"\n          @update:value=\"handleMobileMenuSelect\"\n        />\n      </n-drawer-content>\n    </n-drawer>\n  </div>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, h } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport {\n  NLayout,\n  NLayoutHeader,\n  NLayoutContent,\n  NMenu,\n  NBreadcrumb,\n  NBreadcrumbItem,\n  NAvatar,\n  NDropdown,\n  NIcon,\n  NButton,\n  NSpace,\n  NDrawer,\n  NDrawerContent,\n  useMessage\n} from 'naive-ui'\nimport {\n  HomeOutline,\n  PersonOutline,\n  ShieldOutline,\n  AppsOutline,\n  LogOutOutline,\n  ChevronDown,\n  MenuOutline,\n  IdCardOutline\n} from '@vicons/ionicons5'\nimport { useUserStore } from '../stores/user'\n\nfunction renderIcon(icon) {\n  return () => h(NIcon, null, { default: () => h(icon) })\n}\n\nexport default defineComponent({\n  name: 'AppLayout',\n  components: {\n    NLayout,\n    NLayoutHeader,\n    NLayoutContent,\n    NMenu,\n    NBreadcrumb,\n    NBreadcrumbItem,\n    NAvatar,\n    NDropdown,\n    NIcon,\n    NButton,\n    NSpace,\n    NDrawer,\n    NDrawerContent,\n    ChevronDown,\n    MenuOutline,\n    IdCardOutline\n  },\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    const message = useMessage()\n    const userStore = useUserStore()\n    const mobileMenuVisible = ref(false)\n    \n    // 用户头像和用户名\n    const userAvatar = computed(() => userStore.user?.avatar || '')\n    const username = computed(() => userStore.user?.nickname || userStore.user?.username || '用户')\n    \n    // 当前激活的菜单项\n    const activeKey = computed(() => {\n      const path = route.path\n      if (path.startsWith('/dashboard')) return 'dashboard'\n      if (path.startsWith('/profile')) return 'profile'\n      if (path.startsWith('/security')) return 'security'\n      if (path.startsWith('/applications')) return 'applications'\n      if (path.startsWith('/verification')) return 'verification'\n      return ''\n    })\n    \n    // 面包屑导航\n    const breadcrumbItems = computed(() => {\n      const path = route.path\n      const items = ['首页']\n      \n      if (path.startsWith('/dashboard')) {\n        items.push('仪表盘')\n      } else if (path.startsWith('/profile')) {\n        items.push('个人资料')\n      } else if (path.startsWith('/security')) {\n        items.push('安全设置')\n      } else if (path.startsWith('/applications')) {\n        items.push('应用管理')\n        if (path.includes('/create')) {\n          items.push('创建应用')\n        } else if (path.match(/\\/applications\\/[^/]+$/)) {\n          items.push('应用详情')\n        }\n      } else if (path.startsWith('/verification')) {\n        items.push('实名认证')\n      }\n      \n      return items\n    })\n    \n    // 顶部菜单选项\n    const menuOptions = [\n      {\n        label: '仪表盘',\n        key: 'dashboard',\n        icon: renderIcon(HomeOutline)\n      },\n      {\n        label: '个人资料',\n        key: 'profile',\n        icon: renderIcon(PersonOutline)\n      },\n      {\n        label: '安全设置',\n        key: 'security',\n        icon: renderIcon(ShieldOutline)\n      },\n      {\n        label: '实名认证',\n        key: 'verification',\n        icon: renderIcon(IdCardOutline)\n      },\n      {\n        label: '应用管理',\n        key: 'applications',\n        icon: renderIcon(AppsOutline)\n      }\n    ]\n    \n    // 用户下拉菜单选项\n    const userMenuOptions = [\n      {\n        label: userStore.user?.username || '用户',\n        key: 'username',\n        disabled: true\n      },\n      {\n        type: 'divider',\n        key: 'd1'\n      },\n      {\n        label: '个人资料',\n        key: 'profile',\n        icon: renderIcon(PersonOutline)\n      },\n      {\n        label: '安全设置',\n        key: 'security',\n        icon: renderIcon(ShieldOutline)\n      },\n      {\n        type: 'divider',\n        key: 'd2'\n      },\n      {\n        label: '退出登录',\n        key: 'logout',\n        icon: renderIcon(LogOutOutline)\n      }\n    ]\n    \n    // 处理菜单点击\n    const handleMenuUpdate = (key) => {\n      switch (key) {\n        case 'dashboard':\n          router.push('/dashboard')\n          break\n        case 'profile':\n          router.push('/profile')\n          break\n        case 'security':\n          router.push('/security')\n          break\n        case 'applications':\n          router.push('/applications')\n          break\n        case 'verification':\n          router.push('/verification')\n          break\n      }\n    }\n    \n    // 处理移动端菜单点击\n    const handleMobileMenuSelect = (key) => {\n      handleMenuUpdate(key)\n      mobileMenuVisible.value = false\n    }\n    \n    // 处理用户菜单选择\n    const handleUserMenuSelect = (key) => {\n      switch (key) {\n        case 'profile':\n          router.push('/profile')\n          break\n        case 'security':\n          router.push('/security')\n          break\n        case 'logout':\n          handleLogout()\n          break\n      }\n    }\n    \n    // 退出登录\n    const handleLogout = async () => {\n      try {\n        await userStore.logout()\n        message.success('退出登录成功')\n        router.push('/login')\n      } catch (error) {\n        message.error('退出登录失败')\n      }\n    }\n    \n    return {\n      userAvatar,\n      username,\n      activeKey,\n      breadcrumbItems,\n      menuOptions,\n      userMenuOptions,\n      handleMenuUpdate,\n      handleUserMenuSelect,\n      mobileMenuVisible,\n      handleMobileMenuSelect\n    }\n  }\n})\n</script>\n\n<style scoped>\n.app-layout {\n  height: 100%;\n  overflow: hidden;\n}\n\n.layout-header {\n  padding: 0 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  z-index: 100;\n  position: relative;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 64px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n  margin-right: 10px;\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  margin-right: 10px;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n  color: var(--text-color-1);\n  margin: 0;\n  padding: 0;\n  white-space: nowrap;\n}\n\n.mobile-menu-button {\n  display: none;\n}\n\n.header-center {\n  flex-grow: 1;\n  display: flex;\n  justify-content: center;\n  margin-left: -40px; /* 向左偏移，使菜单更靠近Logo */\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.horizontal-menu {\n  display: flex;\n  justify-content: center;\n}\n\n.user-dropdown {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 8px;\n  transition: background-color 0.2s;\n}\n\n.user-dropdown:hover {\n  background-color: var(--hover-color);\n}\n\n.user-avatar {\n  margin-right: 8px;\n  transition: transform 0.2s ease;\n}\n\n.username {\n  font-size: 14px;\n  font-weight: 500;\n  margin-right: 4px;\n  color: var(--text-color-1);\n}\n\n.dropdown-icon {\n  font-size: 16px;\n  color: var(--text-color-3);\n}\n\n.layout-main {\n  background-color: var(--body-color);\n}\n\n.main-content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n}\n\n.sub-header {\n  margin-bottom: 24px;\n}\n\n/* 导航菜单样式 */\n:deep(.n-menu .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item-content-header) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content::before) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item:hover .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n/* 强制覆盖导航菜单样式 */\n:deep(.n-menu-item-content) {\n  border-radius: 16px !important;\n  padding: 0 16px !important;\n}\n\n:deep(.n-menu-item-content::before) {\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item-content-header) {\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item:hover .n-menu-item-content),\n:deep(.n-menu-item.n-menu-item--selected .n-menu-item-content) {\n  background-color: var(--n-item-color-active) !important;\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item) {\n  margin: 0 4px !important;\n}\n\n\n@media (max-width: 992px) {\n  .main-content-wrapper {\n    padding: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .mobile-menu-button {\n    display: block;\n  }\n\n  .header-center {\n    display: none;\n  }\n  \n  .logo-text {\n    font-size: 18px;\n  }\n}\n\n@media (max-width: 576px) {\n  .username {\n    display: none;\n  }\n\n  .dropdown-icon {\n    display: none;\n  }\n\n  .user-dropdown {\n    padding: 0;\n  }\n\n  .main-content-wrapper {\n    padding: 12px;\n  }\n\n  .layout-header {\n    padding: 0 16px;\n  }\n  \n  .logo-text {\n    font-size: 16px;\n  }\n}\n</style>"], "mappings": ";AA+FA,SAASA,eAAe,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,CAAA,QAAS,KAAI;AACtD,SAASC,SAAS,EAAEC,QAAO,QAAS,YAAW;AAC/C,SACEC,OAAO,EACPC,aAAa,EACbC,cAAc,EACdC,KAAK,EACLC,WAAW,EACXC,eAAe,EACfC,OAAO,EACPC,SAAS,EACTC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,cAAc,EACdC,UAAS,QACJ,UAAS;AAChB,SACEC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,aAAa,EACbC,WAAW,EACXC,WAAW,EACXC,aAAY,QACP,mBAAkB;AACzB,SAASC,YAAW,QAAS,gBAAe;AAE5C,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,OAAO,MAAM3B,CAAC,CAACW,KAAK,EAAE,IAAI,EAAE;IAAEiB,OAAO,EAAEA,CAAA,KAAM5B,CAAC,CAAC2B,IAAI;EAAE,CAAC;AACxD;AAEA,eAAe9B,eAAe,CAAC;EAC7BgC,IAAI,EAAE,WAAW;EACjBC,UAAU,EAAE;IACV3B,OAAO;IACPC,aAAa;IACbC,cAAc;IACdC,KAAK;IACLC,WAAW;IACXC,eAAe;IACfC,OAAO;IACPC,SAAS;IACTC,KAAK;IACLC,OAAO;IACPC,MAAM;IACNC,OAAO;IACPC,cAAc;IACdO,WAAW;IACXC,WAAW;IACXC;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAI/B,SAAS,CAAC;IACzB,MAAMgC,KAAI,GAAI/B,QAAQ,CAAC;IACvB,MAAMgC,OAAM,GAAIlB,UAAU,CAAC;IAC3B,MAAMmB,SAAQ,GAAIV,YAAY,CAAC;IAC/B,MAAMW,iBAAgB,GAAItC,GAAG,CAAC,KAAK;;IAEnC;IACA,MAAMuC,UAAS,GAAItC,QAAQ,CAAC,MAAMoC,SAAS,CAACG,IAAI,EAAEC,MAAK,IAAK,EAAE;IAC9D,MAAMC,QAAO,GAAIzC,QAAQ,CAAC,MAAMoC,SAAS,CAACG,IAAI,EAAEG,QAAO,IAAKN,SAAS,CAACG,IAAI,EAAEE,QAAO,IAAK,IAAI;;IAE5F;IACA,MAAME,SAAQ,GAAI3C,QAAQ,CAAC,MAAM;MAC/B,MAAM4C,IAAG,GAAIV,KAAK,CAACU,IAAG;MACtB,IAAIA,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE,OAAO,WAAU;MACpD,IAAID,IAAI,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE,OAAO,SAAQ;MAChD,IAAID,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE,OAAO,UAAS;MAClD,IAAID,IAAI,CAACC,UAAU,CAAC,eAAe,CAAC,EAAE,OAAO,cAAa;MAC1D,IAAID,IAAI,CAACC,UAAU,CAAC,eAAe,CAAC,EAAE,OAAO,cAAa;MAC1D,OAAO,EAAC;IACV,CAAC;;IAED;IACA,MAAMC,eAAc,GAAI9C,QAAQ,CAAC,MAAM;MACrC,MAAM4C,IAAG,GAAIV,KAAK,CAACU,IAAG;MACtB,MAAMG,KAAI,GAAI,CAAC,IAAI;MAEnB,IAAIH,IAAI,CAACC,UAAU,CAAC,YAAY,CAAC,EAAE;QACjCE,KAAK,CAACC,IAAI,CAAC,KAAK;MAClB,OAAO,IAAIJ,IAAI,CAACC,UAAU,CAAC,UAAU,CAAC,EAAE;QACtCE,KAAK,CAACC,IAAI,CAAC,MAAM;MACnB,OAAO,IAAIJ,IAAI,CAACC,UAAU,CAAC,WAAW,CAAC,EAAE;QACvCE,KAAK,CAACC,IAAI,CAAC,MAAM;MACnB,OAAO,IAAIJ,IAAI,CAACC,UAAU,CAAC,eAAe,CAAC,EAAE;QAC3CE,KAAK,CAACC,IAAI,CAAC,MAAM;QACjB,IAAIJ,IAAI,CAACK,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC5BF,KAAK,CAACC,IAAI,CAAC,MAAM;QACnB,OAAO,IAAIJ,IAAI,CAACM,KAAK,CAAC,wBAAwB,CAAC,EAAE;UAC/CH,KAAK,CAACC,IAAI,CAAC,MAAM;QACnB;MACF,OAAO,IAAIJ,IAAI,CAACC,UAAU,CAAC,eAAe,CAAC,EAAE;QAC3CE,KAAK,CAACC,IAAI,CAAC,MAAM;MACnB;MAEA,OAAOD,KAAI;IACb,CAAC;;IAED;IACA,MAAMI,WAAU,GAAI,CAClB;MACEC,KAAK,EAAE,KAAK;MACZC,GAAG,EAAE,WAAW;MAChBzB,IAAI,EAAED,UAAU,CAACT,WAAW;IAC9B,CAAC,EACD;MACEkC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdzB,IAAI,EAAED,UAAU,CAACR,aAAa;IAChC,CAAC,EACD;MACEiC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,UAAU;MACfzB,IAAI,EAAED,UAAU,CAACP,aAAa;IAChC,CAAC,EACD;MACEgC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,cAAc;MACnBzB,IAAI,EAAED,UAAU,CAACF,aAAa;IAChC,CAAC,EACD;MACE2B,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,cAAc;MACnBzB,IAAI,EAAED,UAAU,CAACN,WAAW;IAC9B,EACF;;IAEA;IACA,MAAMiC,eAAc,GAAI,CACtB;MACEF,KAAK,EAAEhB,SAAS,CAACG,IAAI,EAAEE,QAAO,IAAK,IAAI;MACvCY,GAAG,EAAE,UAAU;MACfE,QAAQ,EAAE;IACZ,CAAC,EACD;MACEC,IAAI,EAAE,SAAS;MACfH,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdzB,IAAI,EAAED,UAAU,CAACR,aAAa;IAChC,CAAC,EACD;MACEiC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,UAAU;MACfzB,IAAI,EAAED,UAAU,CAACP,aAAa;IAChC,CAAC,EACD;MACEoC,IAAI,EAAE,SAAS;MACfH,GAAG,EAAE;IACP,CAAC,EACD;MACED,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,QAAQ;MACbzB,IAAI,EAAED,UAAU,CAACL,aAAa;IAChC,EACF;;IAEA;IACA,MAAMmC,gBAAe,GAAKJ,GAAG,IAAK;MAChC,QAAQA,GAAG;QACT,KAAK,WAAW;UACdpB,MAAM,CAACe,IAAI,CAAC,YAAY;UACxB;QACF,KAAK,SAAS;UACZf,MAAM,CAACe,IAAI,CAAC,UAAU;UACtB;QACF,KAAK,UAAU;UACbf,MAAM,CAACe,IAAI,CAAC,WAAW;UACvB;QACF,KAAK,cAAc;UACjBf,MAAM,CAACe,IAAI,CAAC,eAAe;UAC3B;QACF,KAAK,cAAc;UACjBf,MAAM,CAACe,IAAI,CAAC,eAAe;UAC3B;MACJ;IACF;;IAEA;IACA,MAAMU,sBAAqB,GAAKL,GAAG,IAAK;MACtCI,gBAAgB,CAACJ,GAAG;MACpBhB,iBAAiB,CAACsB,KAAI,GAAI,KAAI;IAChC;;IAEA;IACA,MAAMC,oBAAmB,GAAKP,GAAG,IAAK;MACpC,QAAQA,GAAG;QACT,KAAK,SAAS;UACZpB,MAAM,CAACe,IAAI,CAAC,UAAU;UACtB;QACF,KAAK,UAAU;UACbf,MAAM,CAACe,IAAI,CAAC,WAAW;UACvB;QACF,KAAK,QAAQ;UACXa,YAAY,CAAC;UACb;MACJ;IACF;;IAEA;IACA,MAAMA,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B,IAAI;QACF,MAAMzB,SAAS,CAAC0B,MAAM,CAAC;QACvB3B,OAAO,CAAC4B,OAAO,CAAC,QAAQ;QACxB9B,MAAM,CAACe,IAAI,CAAC,QAAQ;MACtB,EAAE,OAAOgB,KAAK,EAAE;QACd7B,OAAO,CAAC6B,KAAK,CAAC,QAAQ;MACxB;IACF;IAEA,OAAO;MACL1B,UAAU;MACVG,QAAQ;MACRE,SAAS;MACTG,eAAe;MACfK,WAAW;MACXG,eAAe;MACfG,gBAAgB;MAChBG,oBAAoB;MACpBvB,iBAAiB;MACjBqB;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}