/**
 * 邮件发送工具函数
 */
const nodemailer = require('nodemailer');
const config = require('../config/config');

// 创建邮件传输对象
const createTransporter = () => {
  const mailConfig = config.mail;
  
  return nodemailer.createTransport({
    host: mailConfig.host,
    port: mailConfig.port,
    secure: mailConfig.secure,
    auth: {
      user: mailConfig.user,
      pass: mailConfig.pass
    }
  });
};

/**
 * 发送邮件
 * @param {string} to - 收件人邮箱
 * @param {string} subject - 邮件主题
 * @param {string} html - 邮件内容(HTML格式)
 * @returns {Promise<object>} - 发送结果
 */
const sendEmail = async (to, subject, html) => {
  try {
    const mailConfig = config.mail;

    // 开发环境模拟邮件发送
    if (config.server.env === 'development' &&
        (mailConfig.host === 'smtp.example.com' ||
         mailConfig.user === '<EMAIL>')) {
      console.log(`[开发模式] 模拟发送邮件到 ${to}`);
      console.log(`主题: ${subject}`);
      console.log(`内容: ${html.substring(0, 100)}...`);
      return {
        success: true,
        messageId: 'dev_' + Date.now(),
        message: '邮件发送成功（开发模式）'
      };
    }

    const transporter = createTransporter();

    const mailOptions = {
      from: config.mail.from,
      to,
      subject,
      html
    };

    const info = await transporter.sendMail(mailOptions);

    return {
      success: true,
      messageId: info.messageId,
      message: '邮件发送成功'
    };
  } catch (error) {
    console.error('邮件发送错误:', error);
    return {
      success: false,
      error: error.message || '邮件发送失败'
    };
  }
};

/**
 * 发送验证码邮件
 * @param {string} to - 收件人邮箱
 * @param {string} code - 验证码
 * @param {string} type - 验证码类型 (register/login/reset)
 * @returns {Promise<object>} - 发送结果
 */
const sendVerificationEmail = async (to, code, type = 'register') => {
  let subject = '';
  let content = '';
  
  // 根据不同类型构建不同的主题和内容
  switch (type) {
    case 'register':
      subject = '【空旷账户中心】注册验证码';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #1890ff;">空旷账户中心</h2>
          <p>您好！</p>
          <p>感谢您注册空旷账户中心。请使用以下验证码完成注册流程：</p>
          <div style="background-color: #f5f5f5; padding: 10px; margin: 15px 0; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${code}
          </div>
          <p>验证码有效期为${config.verification_code.expire_minutes}分钟，请勿将验证码泄露给他人。</p>
          <p>如果这不是您的操作，请忽略此邮件。</p>
          <p>此邮件由系统自动发送，请勿回复。</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
            <p>空旷账户中心 © ${new Date().getFullYear()}</p>
          </div>
        </div>
      `;
      break;
      
    case 'login':
      subject = '【空旷账户中心】登录验证码';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #1890ff;">空旷账户中心</h2>
          <p>您好！</p>
          <p>您正在登录空旷账户中心。请使用以下验证码完成登录：</p>
          <div style="background-color: #f5f5f5; padding: 10px; margin: 15px 0; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${code}
          </div>
          <p>验证码有效期为${config.verification_code.expire_minutes}分钟，请勿将验证码泄露给他人。</p>
          <p>如果这不是您的操作，请立即修改密码。</p>
          <p>此邮件由系统自动发送，请勿回复。</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
            <p>空旷账户中心 © ${new Date().getFullYear()}</p>
          </div>
        </div>
      `;
      break;
      
    case 'reset_password':
      subject = '【空旷账户中心】重置密码验证码';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #1890ff;">空旷账户中心</h2>
          <p>您好！</p>
          <p>您正在重置空旷账户中心的密码。请使用以下验证码完成重置：</p>
          <div style="background-color: #f5f5f5; padding: 10px; margin: 15px 0; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${code}
          </div>
          <p>验证码有效期为${config.verification_code.expire_minutes}分钟，请勿将验证码泄露给他人。</p>
          <p>如果这不是您的操作，请忽略此邮件，并确保您的账户安全。</p>
          <p>此邮件由系统自动发送，请勿回复。</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
            <p>空旷账户中心 © ${new Date().getFullYear()}</p>
          </div>
        </div>
      `;
      break;
      
    default:
      subject = '【空旷账户中心】验证码';
      content = `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #1890ff;">空旷账户中心</h2>
          <p>您好！</p>
          <p>您的验证码是：</p>
          <div style="background-color: #f5f5f5; padding: 10px; margin: 15px 0; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${code}
          </div>
          <p>验证码有效期为${config.verification_code.expire_minutes}分钟，请勿将验证码泄露给他人。</p>
          <p>如果这不是您的操作，请忽略此邮件。</p>
          <p>此邮件由系统自动发送，请勿回复。</p>
          <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
            <p>空旷账户中心 © ${new Date().getFullYear()}</p>
          </div>
        </div>
      `;
  }
  
  return await sendEmail(to, subject, content);
};

/**
 * 发送邮箱验证链接邮件
 * @param {string} to - 收件人邮箱
 * @param {string} token - 验证令牌
 * @param {string} username - 用户名
 * @returns {Promise<object>} - 发送结果
 */
const sendEmailVerification = async (to, token, username) => {
  const verificationLink = `${config.frontend.url}/verify-email?token=${token}`;
  
  const subject = '【空旷账户中心】验证您的邮箱';
  const content = `
    <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
      <h2 style="color: #1890ff;">空旷账户中心</h2>
      <p>您好，${username}！</p>
      <p>请点击下面的按钮验证您的邮箱地址：</p>
      <div style="margin: 25px 0;">
        <a href="${verificationLink}" style="background-color: #1890ff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block;">
          验证邮箱
        </a>
      </div>
      <p>或者，您可以复制以下链接到浏览器地址栏：</p>
      <p style="word-break: break-all; color: #666;">
        ${verificationLink}
      </p>
      <p>此链接有效期为24小时。</p>
      <p>如果这不是您的操作，请忽略此邮件。</p>
      <p>此邮件由系统自动发送，请勿回复。</p>
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
        <p>空旷账户中心 © ${new Date().getFullYear()}</p>
      </div>
    </div>
  `;
  
  return await sendEmail(to, subject, content);
};

module.exports = {
  sendEmail,
  sendVerificationEmail,
  sendEmailVerification
}; 