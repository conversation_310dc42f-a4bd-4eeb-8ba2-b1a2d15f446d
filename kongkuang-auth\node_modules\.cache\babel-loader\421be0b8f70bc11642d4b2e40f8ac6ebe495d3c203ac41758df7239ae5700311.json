{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, provide } from 'vue';\nimport { NConfigProvider, NMessageProvider, NLoadingBarProvider, NButton, NIcon, darkTheme } from 'naive-ui';\nimport AppLayout from './components/AppLayout.vue';\nimport PageScrollbar from './components/PageScrollbar.vue';\nimport { SunnyOutline as LightModeIcon, MoonOutline as DarkModeIcon } from '@vicons/ionicons5';\nimport ErrorHandler from './components/ErrorHandler.vue';\nexport default defineComponent({\n  name: 'App',\n  components: {\n    NConfigProvider,\n    NMessageProvider,\n    NLoadingBarProvider,\n    NButton,\n    NIcon,\n    AppLayout,\n    PageScrollbar,\n    LightModeIcon,\n    DarkModeIcon,\n    ErrorHandler\n  },\n  setup() {\n    const themeMode = ref(localStorage.getItem('theme') || 'system');\n    const isDarkSystem = ref(window.matchMedia('(prefers-color-scheme: dark)').matches);\n    const isDarkMode = computed(() => {\n      if (themeMode.value === 'system') return isDarkSystem.value;\n      return themeMode.value === 'dark';\n    });\n    const currentTheme = computed(() => isDarkMode.value ? darkTheme : null);\n    onMounted(() => {\n      const mql = window.matchMedia('(prefers-color-scheme: dark)');\n      mql.addEventListener('change', e => {\n        isDarkSystem.value = e.matches;\n      });\n    });\n    const setTheme = mode => {\n      themeMode.value = mode;\n      localStorage.setItem('theme', mode);\n    };\n    const toggleTheme = () => {\n      if (themeMode.value === 'system') {\n        setTheme(isDarkSystem.value ? 'light' : 'dark');\n      } else {\n        setTheme(themeMode.value === 'dark' ? 'light' : 'dark');\n      }\n    };\n    const baseThemeOverrides = {\n      common: {\n        borderRadius: '12px',\n        borderRadiusSmall: '8px',\n        primaryColor: '#2080f0',\n        primaryColorHover: '#4098fc',\n        primaryColorPressed: '#1060c9',\n        primaryColorSuppl: '#4098fc'\n      },\n      Button: {\n        borderRadius: '8px',\n        borderRadiusSmall: '6px'\n      },\n      Card: {\n        borderRadius: '16px'\n      },\n      Input: {\n        borderRadius: '8px'\n      },\n      Menu: {\n        borderRadius: '16px',\n        itemBorderRadius: '16px'\n      }\n    };\n    const themeOverrides = computed(() => {\n      if (isDarkMode.value) {\n        return {\n          ...baseThemeOverrides,\n          common: {\n            ...baseThemeOverrides.common,\n            bodyColor: '#101014',\n            cardColor: '#2a2a30',\n            // 深色模式下卡片为灰色\n            textColor1: 'rgba(255, 255, 255, 0.9)',\n            textColor2: 'rgba(255, 255, 255, 0.7)',\n            textColor3: 'rgba(255, 255, 255, 0.5)',\n            dividerColor: 'rgba(255, 255, 255, 0.12)',\n            hoverColor: 'rgba(255, 255, 255, 0.08)'\n          },\n          Layout: {\n            color: '#101014',\n            headerColor: '#1a1a1f',\n            footerColor: 'transparent',\n            siderColor: '#1a1a1f'\n          },\n          Menu: {\n            itemTextColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemTextColorHoverHorizontal: 'rgba(255, 255, 255, 1)',\n            itemTextColorActiveHorizontal: '#2080f0',\n            itemIconColorActiveHorizontal: '#2080f0',\n            itemIconColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemIconColorHoverHorizontal: 'rgba(255, 255, 255, 1)'\n          },\n          Card: {\n            color: '#2a2a30',\n            // 确保深色模式下卡片为灰色\n            colorModal: '#2a2a30',\n            colorPopover: '#2a2a30',\n            colorEmbedded: '#2a2a30',\n            colorTarget: '#2a2a30'\n          }\n        };\n      }\n      return {\n        ...baseThemeOverrides,\n        common: {\n          ...baseThemeOverrides.common,\n          bodyColor: '#f5f7fa',\n          cardColor: '#ffffff',\n          // 浅色模式下卡片为纯白色\n          textColor1: 'rgba(0, 0, 0, 0.9)',\n          textColor2: 'rgba(0, 0, 0, 0.7)',\n          textColor3: 'rgba(0, 0, 0, 0.45)',\n          dividerColor: 'rgba(0, 0, 0, 0.09)',\n          hoverColor: 'rgba(0, 0, 0, 0.04)'\n        },\n        Layout: {\n          color: '#f5f7fa',\n          headerColor: '#ffffff',\n          footerColor: 'transparent',\n          siderColor: '#ffffff'\n        },\n        Card: {\n          color: '#ffffff',\n          // 确保浅色模式下卡片为纯白色\n          colorModal: '#ffffff',\n          colorPopover: '#ffffff',\n          colorEmbedded: '#ffffff',\n          colorTarget: '#ffffff'\n        }\n      };\n    });\n    const isAuthRoute = route => {\n      const authRoutes = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email'];\n      return authRoutes.includes(route.path);\n    };\n    const errorHandlerRef = ref(null);\n    provide('$$app', {\n      setTheme,\n      themeMode\n    });\n    return {\n      isDarkMode,\n      currentTheme,\n      toggleTheme,\n      themeOverrides,\n      isAuthRoute,\n      errorHandlerRef\n    };\n  }\n});", "map": {"version": 3, "names": ["defineComponent", "ref", "computed", "onMounted", "provide", "NConfigProvider", "NMessageProvider", "NLoadingBar<PERSON>rovider", "NButton", "NIcon", "darkTheme", "AppLayout", "PageScrollbar", "SunnyOutline", "LightModeIcon", "MoonOutline", "DarkModeIcon", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "components", "setup", "themeMode", "localStorage", "getItem", "isDarkSystem", "window", "matchMedia", "matches", "isDarkMode", "value", "currentTheme", "mql", "addEventListener", "e", "setTheme", "mode", "setItem", "toggleTheme", "baseThemeOverrides", "common", "borderRadius", "borderRadiusSmall", "primaryColor", "primaryColorHover", "primaryColorPressed", "primaryColorSuppl", "<PERSON><PERSON>", "Card", "Input", "<PERSON><PERSON>", "itemBorderRadius", "themeOverrides", "bodyColor", "cardColor", "textColor1", "textColor2", "textColor3", "dividerColor", "hoverColor", "Layout", "color", "headerColor", "footerColor", "siderColor", "itemTextColorHorizontal", "itemTextColorHoverHorizontal", "itemTextColorActiveHorizontal", "itemIconColorActiveHorizontal", "itemIconColorHorizontal", "itemIconColorHoverHorizontal", "colorModal", "colorPopover", "colorEmbedded", "colorTarget", "isAuthRoute", "route", "authRoutes", "includes", "path", "errorHandlerRef"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\App.vue"], "sourcesContent": ["<template>\n  <n-config-provider :theme=\"currentTheme\" :theme-overrides=\"themeOverrides\">\n    <n-message-provider>\n      <n-dialog-provider>\n        <n-loading-bar-provider>\n          <!-- 全局错误处理组件 -->\n          <error-handler ref=\"errorHandlerRef\" />\n        \n        <router-view v-slot=\"{ Component, route }\">\n          <template v-if=\"isAuthRoute(route)\">\n            <component :is=\"Component\" />\n          </template>\n          <app-layout v-else>\n            <component :is=\"Component\" />\n          </app-layout>\n        </router-view>\n        \n        <!-- 主题切换按钮 -->\n        <div class=\"theme-switch\">\n          <n-button circle @click=\"toggleTheme\">\n            <template #icon>\n              <n-icon>\n                <dark-mode-icon v-if=\"isDarkMode\" />\n                <light-mode-icon v-else />\n              </n-icon>\n            </template>\n          </n-button>\n        </div>\n        </n-loading-bar-provider>\n      </n-dialog-provider>\n    </n-message-provider>\n  </n-config-provider>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, onMounted, provide } from 'vue'\nimport { \n  NConfigProvider, \n  NMessageProvider, \n  NLoadingBarProvider,\n  NButton,\n  NIcon,\n  darkTheme\n} from 'naive-ui'\nimport AppLayout from './components/AppLayout.vue'\nimport PageScrollbar from './components/PageScrollbar.vue'\nimport { SunnyOutline as LightModeIcon, MoonOutline as DarkModeIcon } from '@vicons/ionicons5'\nimport ErrorHandler from './components/ErrorHandler.vue'\n\nexport default defineComponent({\n  name: 'App',\n  components: {\n    NConfigProvider,\n    NMessageProvider,\n    NLoadingBarProvider,\n    NButton,\n    NIcon,\n    AppLayout,\n    PageScrollbar,\n    LightModeIcon,\n    DarkModeIcon,\n    ErrorHandler\n  },\n  setup() {\n    const themeMode = ref(localStorage.getItem('theme') || 'system')\n    const isDarkSystem = ref(window.matchMedia('(prefers-color-scheme: dark)').matches)\n\n    const isDarkMode = computed(() => {\n      if (themeMode.value === 'system') return isDarkSystem.value\n      return themeMode.value === 'dark'\n    })\n\n    const currentTheme = computed(() => (isDarkMode.value ? darkTheme : null))\n\n    onMounted(() => {\n      const mql = window.matchMedia('(prefers-color-scheme: dark)')\n      mql.addEventListener('change', (e) => {\n        isDarkSystem.value = e.matches\n      })\n    })\n\n    const setTheme = (mode) => {\n      themeMode.value = mode\n      localStorage.setItem('theme', mode)\n    }\n\n    const toggleTheme = () => {\n      if (themeMode.value === 'system') {\n        setTheme(isDarkSystem.value ? 'light' : 'dark')\n      } else {\n        setTheme(themeMode.value === 'dark' ? 'light' : 'dark')\n      }\n    }\n\n    const baseThemeOverrides = {\n      common: {\n        borderRadius: '12px',\n        borderRadiusSmall: '8px',\n        primaryColor: '#2080f0',\n        primaryColorHover: '#4098fc',\n        primaryColorPressed: '#1060c9',\n        primaryColorSuppl: '#4098fc',\n      },\n      Button: {\n        borderRadius: '8px',\n        borderRadiusSmall: '6px',\n      },\n      Card: {\n        borderRadius: '16px',\n      },\n      Input: {\n        borderRadius: '8px',\n      },\n      Menu: {\n        borderRadius: '16px',\n        itemBorderRadius: '16px',\n      },\n    }\n\n    const themeOverrides = computed(() => {\n      if (isDarkMode.value) {\n        return {\n          ...baseThemeOverrides,\n          common: {\n          ...baseThemeOverrides.common,\n          bodyColor: '#101014',\n          cardColor: '#2a2a30', // 深色模式下卡片为灰色\n          textColor1: 'rgba(255, 255, 255, 0.9)',\n          textColor2: 'rgba(255, 255, 255, 0.7)',\n          textColor3: 'rgba(255, 255, 255, 0.5)',\n          dividerColor: 'rgba(255, 255, 255, 0.12)',\n          hoverColor: 'rgba(255, 255, 255, 0.08)',\n        },\n          Layout: {\n            color: '#101014',\n            headerColor: '#1a1a1f',\n            footerColor: 'transparent',\n            siderColor: '#1a1a1f',\n          },\n          Menu: {\n            itemTextColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemTextColorHoverHorizontal: 'rgba(255, 255, 255, 1)',\n            itemTextColorActiveHorizontal: '#2080f0',\n            itemIconColorActiveHorizontal: '#2080f0',\n            itemIconColorHorizontal: 'rgba(255, 255, 255, 0.9)',\n            itemIconColorHoverHorizontal: 'rgba(255, 255, 255, 1)'\n          },\n          Card: {\n            color: '#2a2a30', // 确保深色模式下卡片为灰色\n            colorModal: '#2a2a30',\n            colorPopover: '#2a2a30',\n            colorEmbedded: '#2a2a30',\n            colorTarget: '#2a2a30'\n          },\n        }\n      }\n      return {\n        ...baseThemeOverrides,\n        common: {\n          ...baseThemeOverrides.common,\n          bodyColor: '#f5f7fa',\n          cardColor: '#ffffff', // 浅色模式下卡片为纯白色\n          textColor1: 'rgba(0, 0, 0, 0.9)',\n          textColor2: 'rgba(0, 0, 0, 0.7)',\n          textColor3: 'rgba(0, 0, 0, 0.45)',\n          dividerColor: 'rgba(0, 0, 0, 0.09)',\n          hoverColor: 'rgba(0, 0, 0, 0.04)',\n        },\n        Layout: {\n          color: '#f5f7fa',\n          headerColor: '#ffffff',\n          footerColor: 'transparent',\n          siderColor: '#ffffff',\n        },\n        Card: {\n          color: '#ffffff', // 确保浅色模式下卡片为纯白色\n          colorModal: '#ffffff',\n          colorPopover: '#ffffff',\n          colorEmbedded: '#ffffff',\n          colorTarget: '#ffffff'\n        },\n      }\n    })\n    \n    const isAuthRoute = (route) => {\n      const authRoutes = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email']\n      return authRoutes.includes(route.path)\n    }\n    \n    const errorHandlerRef = ref(null)\n    \n    provide('$$app', {\n      setTheme,\n      themeMode,\n    })\n    \n    return {\n      isDarkMode,\n      currentTheme,\n      toggleTheme,\n      themeOverrides,\n      isAuthRoute,\n      errorHandlerRef\n    }\n  }\n})\n</script>\n\n<style>\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100%;\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',\n    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n}\n\n#app {\n  height: 100%;\n}\n\n* {\n  box-sizing: border-box;\n}\n\n/* 主题切换按钮 */\n.theme-switch {\n  position: fixed;\n  right: 24px;\n  bottom: 24px;\n  z-index: 1000;\n}\n\n.theme-switch .n-button {\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n}\n\n/* 全局强制覆盖导航菜单样式 */\n.n-menu-item-content {\n  border-radius: 16px !important;\n}\n\n.n-menu-item-content::before {\n  border-radius: 16px !important;\n}\n\n.n-menu-item:hover .n-menu-item-content,\n.n-menu-item--selected .n-menu-item-content {\n  border-radius: 16px !important;\n}\n</style>\n"], "mappings": "AAmCA,SAASA,eAAe,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,OAAM,QAAS,KAAI;AACvE,SACEC,eAAe,EACfC,gBAAgB,EAChBC,mBAAmB,EACnBC,OAAO,EACPC,KAAK,EACLC,SAAQ,QACH,UAAS;AAChB,OAAOC,SAAQ,MAAO,4BAA2B;AACjD,OAAOC,aAAY,MAAO,gCAA+B;AACzD,SAASC,YAAW,IAAKC,aAAa,EAAEC,WAAU,IAAKC,YAAW,QAAS,mBAAkB;AAC7F,OAAOC,YAAW,MAAO,+BAA8B;AAEvD,eAAejB,eAAe,CAAC;EAC7BkB,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVd,eAAe;IACfC,gBAAgB;IAChBC,mBAAmB;IACnBC,OAAO;IACPC,KAAK;IACLE,SAAS;IACTC,aAAa;IACbE,aAAa;IACbE,YAAY;IACZC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,SAAQ,GAAIpB,GAAG,CAACqB,YAAY,CAACC,OAAO,CAAC,OAAO,KAAK,QAAQ;IAC/D,MAAMC,YAAW,GAAIvB,GAAG,CAACwB,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;IAElF,MAAMC,UAAS,GAAI1B,QAAQ,CAAC,MAAM;MAChC,IAAImB,SAAS,CAACQ,KAAI,KAAM,QAAQ,EAAE,OAAOL,YAAY,CAACK,KAAI;MAC1D,OAAOR,SAAS,CAACQ,KAAI,KAAM,MAAK;IAClC,CAAC;IAED,MAAMC,YAAW,GAAI5B,QAAQ,CAAC,MAAO0B,UAAU,CAACC,KAAI,GAAInB,SAAQ,GAAI,IAAK;IAEzEP,SAAS,CAAC,MAAM;MACd,MAAM4B,GAAE,GAAIN,MAAM,CAACC,UAAU,CAAC,8BAA8B;MAC5DK,GAAG,CAACC,gBAAgB,CAAC,QAAQ,EAAGC,CAAC,IAAK;QACpCT,YAAY,CAACK,KAAI,GAAII,CAAC,CAACN,OAAM;MAC/B,CAAC;IACH,CAAC;IAED,MAAMO,QAAO,GAAKC,IAAI,IAAK;MACzBd,SAAS,CAACQ,KAAI,GAAIM,IAAG;MACrBb,YAAY,CAACc,OAAO,CAAC,OAAO,EAAED,IAAI;IACpC;IAEA,MAAME,WAAU,GAAIA,CAAA,KAAM;MACxB,IAAIhB,SAAS,CAACQ,KAAI,KAAM,QAAQ,EAAE;QAChCK,QAAQ,CAACV,YAAY,CAACK,KAAI,GAAI,OAAM,GAAI,MAAM;MAChD,OAAO;QACLK,QAAQ,CAACb,SAAS,CAACQ,KAAI,KAAM,MAAK,GAAI,OAAM,GAAI,MAAM;MACxD;IACF;IAEA,MAAMS,kBAAiB,GAAI;MACzBC,MAAM,EAAE;QACNC,YAAY,EAAE,MAAM;QACpBC,iBAAiB,EAAE,KAAK;QACxBC,YAAY,EAAE,SAAS;QACvBC,iBAAiB,EAAE,SAAS;QAC5BC,mBAAmB,EAAE,SAAS;QAC9BC,iBAAiB,EAAE;MACrB,CAAC;MACDC,MAAM,EAAE;QACNN,YAAY,EAAE,KAAK;QACnBC,iBAAiB,EAAE;MACrB,CAAC;MACDM,IAAI,EAAE;QACJP,YAAY,EAAE;MAChB,CAAC;MACDQ,KAAK,EAAE;QACLR,YAAY,EAAE;MAChB,CAAC;MACDS,IAAI,EAAE;QACJT,YAAY,EAAE,MAAM;QACpBU,gBAAgB,EAAE;MACpB;IACF;IAEA,MAAMC,cAAa,GAAIjD,QAAQ,CAAC,MAAM;MACpC,IAAI0B,UAAU,CAACC,KAAK,EAAE;QACpB,OAAO;UACL,GAAGS,kBAAkB;UACrBC,MAAM,EAAE;YACR,GAAGD,kBAAkB,CAACC,MAAM;YAC5Ba,SAAS,EAAE,SAAS;YACpBC,SAAS,EAAE,SAAS;YAAE;YACtBC,UAAU,EAAE,0BAA0B;YACtCC,UAAU,EAAE,0BAA0B;YACtCC,UAAU,EAAE,0BAA0B;YACtCC,YAAY,EAAE,2BAA2B;YACzCC,UAAU,EAAE;UACd,CAAC;UACCC,MAAM,EAAE;YACNC,KAAK,EAAE,SAAS;YAChBC,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE,aAAa;YAC1BC,UAAU,EAAE;UACd,CAAC;UACDd,IAAI,EAAE;YACJe,uBAAuB,EAAE,0BAA0B;YACnDC,4BAA4B,EAAE,wBAAwB;YACtDC,6BAA6B,EAAE,SAAS;YACxCC,6BAA6B,EAAE,SAAS;YACxCC,uBAAuB,EAAE,0BAA0B;YACnDC,4BAA4B,EAAE;UAChC,CAAC;UACDtB,IAAI,EAAE;YACJa,KAAK,EAAE,SAAS;YAAE;YAClBU,UAAU,EAAE,SAAS;YACrBC,YAAY,EAAE,SAAS;YACvBC,aAAa,EAAE,SAAS;YACxBC,WAAW,EAAE;UACf;QACF;MACF;MACA,OAAO;QACL,GAAGnC,kBAAkB;QACrBC,MAAM,EAAE;UACN,GAAGD,kBAAkB,CAACC,MAAM;UAC5Ba,SAAS,EAAE,SAAS;UACpBC,SAAS,EAAE,SAAS;UAAE;UACtBC,UAAU,EAAE,oBAAoB;UAChCC,UAAU,EAAE,oBAAoB;UAChCC,UAAU,EAAE,qBAAqB;UACjCC,YAAY,EAAE,qBAAqB;UACnCC,UAAU,EAAE;QACd,CAAC;QACDC,MAAM,EAAE;UACNC,KAAK,EAAE,SAAS;UAChBC,WAAW,EAAE,SAAS;UACtBC,WAAW,EAAE,aAAa;UAC1BC,UAAU,EAAE;QACd,CAAC;QACDhB,IAAI,EAAE;UACJa,KAAK,EAAE,SAAS;UAAE;UAClBU,UAAU,EAAE,SAAS;UACrBC,YAAY,EAAE,SAAS;UACvBC,aAAa,EAAE,SAAS;UACxBC,WAAW,EAAE;QACf;MACF;IACF,CAAC;IAED,MAAMC,WAAU,GAAKC,KAAK,IAAK;MAC7B,MAAMC,UAAS,GAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,eAAe;MACjG,OAAOA,UAAU,CAACC,QAAQ,CAACF,KAAK,CAACG,IAAI;IACvC;IAEA,MAAMC,eAAc,GAAI9E,GAAG,CAAC,IAAI;IAEhCG,OAAO,CAAC,OAAO,EAAE;MACf8B,QAAQ;MACRb;IACF,CAAC;IAED,OAAO;MACLO,UAAU;MACVE,YAAY;MACZO,WAAW;MACXc,cAAc;MACduB,WAAW;MACXK;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}