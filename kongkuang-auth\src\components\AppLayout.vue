<template>
  <div class="app-layout">
    <n-layout>
      <n-layout-header bordered class="layout-header">
        <div class="header-content">
          <div class="header-left">
            <div class="logo-container">
              <h2 class="logo-text">空旷账号中心</h2>
            </div>
            <n-button
              quaternary
              circle
              size="medium"
              class="mobile-menu-button"
              @click="mobileMenuVisible = true"
            >
              <template #icon
                ><n-icon><menu-outline /></n-icon
              ></template>
            </n-button>
          </div>
          
          <div class="header-center">
            <n-menu
              mode="horizontal"
              :options="menuOptions"
              :value="activeKey"
              @update:value="handleMenuUpdate"
              class="horizontal-menu"
              :inline-theme="false"
              :inverted="false"
              style="--n-item-border-radius: 16px;"
            />
          </div>
          
          <div class="header-right">
            <n-space :size="16">
              <n-dropdown
                :options="userMenuOptions"
                @select="handleUserMenuSelect"
                trigger="click"
              >
                <div class="user-dropdown">
                  <n-avatar
                    round
                    size="medium"
                    :src="userAvatar || '/default-avatar.png'"
                    class="user-avatar"
                  />
                  <span class="username">{{ username }}</span>
                  <n-icon class="dropdown-icon"><chevron-down /></n-icon>
                </div>
              </n-dropdown>
            </n-space>
          </div>
        </div>
      </n-layout-header>

      <n-layout-content
        class="layout-main"
        :native-scrollbar="true"
        content-style="min-height: calc(100vh - 64px); padding: 0;"
      >
        <n-scrollbar style="max-height: calc(100vh - 64px);">
          <div class="main-content-wrapper">
            <div class="sub-header">
              <n-breadcrumb>
                <n-breadcrumb-item
                  v-for="(item, index) in breadcrumbItems"
                  :key="index"
                >
                  {{ item }}
                </n-breadcrumb-item>
              </n-breadcrumb>
            </div>
            <slot></slot>
          </div>
        </n-scrollbar>
      </n-layout-content>
    </n-layout>

    <!-- 移动端菜单抽屉 -->
    <n-drawer v-model:show="mobileMenuVisible" :width="280" placement="left">
      <n-drawer-content title="菜单">
        <n-menu
          :options="menuOptions"
          :value="activeKey"
          @update:value="handleMobileMenuSelect"
        />
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  NLayout,
  NLayoutHeader,
  NLayoutContent,
  NMenu,
  NBreadcrumb,
  NBreadcrumbItem,
  NAvatar,
  NDropdown,
  NIcon,
  NButton,
  NSpace,
  NDrawer,
  NDrawerContent,
  NScrollbar,
  useMessage
} from 'naive-ui'
import {
  HomeOutline,
  PersonOutline,
  ShieldOutline,
  AppsOutline,
  LogOutOutline,
  ChevronDown,
  MenuOutline,
  IdCardOutline
} from '@vicons/ionicons5'
import { useUserStore } from '../stores/user'

function renderIcon(icon) {
  return () => h(NIcon, null, { default: () => h(icon) })
}

export default defineComponent({
  name: 'AppLayout',
  components: {
    NLayout,
    NLayoutHeader,
    NLayoutContent,
    NMenu,
    NBreadcrumb,
    NBreadcrumbItem,
    NAvatar,
    NDropdown,
    NIcon,
    NButton,
    NSpace,
    NDrawer,
    NDrawerContent,
    NScrollbar,
    ChevronDown,
    MenuOutline,
    IdCardOutline
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    const message = useMessage()
    const userStore = useUserStore()
    const mobileMenuVisible = ref(false)
    
    // 用户头像和用户名
    const userAvatar = computed(() => userStore.user?.avatar || '')
    const username = computed(() => userStore.user?.nickname || userStore.user?.username || '用户')
    
    // 当前激活的菜单项
    const activeKey = computed(() => {
      const path = route.path
      if (path.startsWith('/dashboard')) return 'dashboard'
      if (path.startsWith('/profile')) return 'profile'
      if (path.startsWith('/security')) return 'security'
      if (path.startsWith('/applications')) return 'applications'
      if (path.startsWith('/verification')) return 'verification'
      return ''
    })
    
    // 面包屑导航
    const breadcrumbItems = computed(() => {
      const path = route.path
      const items = ['首页']
      
      if (path.startsWith('/dashboard')) {
        items.push('仪表盘')
      } else if (path.startsWith('/profile')) {
        items.push('个人资料')
      } else if (path.startsWith('/security')) {
        items.push('安全设置')
      } else if (path.startsWith('/applications')) {
        items.push('应用管理')
        if (path.includes('/create')) {
          items.push('创建应用')
        } else if (path.match(/\/applications\/[^/]+$/)) {
          items.push('应用详情')
        }
      } else if (path.startsWith('/verification')) {
        items.push('实名认证')
      }
      
      return items
    })
    
    // 顶部菜单选项
    const menuOptions = [
      {
        label: '仪表盘',
        key: 'dashboard',
        icon: renderIcon(HomeOutline)
      },
      {
        label: '个人资料',
        key: 'profile',
        icon: renderIcon(PersonOutline)
      },
      {
        label: '安全设置',
        key: 'security',
        icon: renderIcon(ShieldOutline)
      },
      {
        label: '实名认证',
        key: 'verification',
        icon: renderIcon(IdCardOutline)
      },
      {
        label: '应用管理',
        key: 'applications',
        icon: renderIcon(AppsOutline)
      }
    ]
    
    // 用户下拉菜单选项
    const userMenuOptions = [
      {
        label: userStore.user?.username || '用户',
        key: 'username',
        disabled: true
      },
      {
        type: 'divider',
        key: 'd1'
      },
      {
        label: '个人资料',
        key: 'profile',
        icon: renderIcon(PersonOutline)
      },
      {
        label: '安全设置',
        key: 'security',
        icon: renderIcon(ShieldOutline)
      },
      {
        type: 'divider',
        key: 'd2'
      },
      {
        label: '退出登录',
        key: 'logout',
        icon: renderIcon(LogOutOutline)
      }
    ]
    
    // 处理菜单点击
    const handleMenuUpdate = (key) => {
      switch (key) {
        case 'dashboard':
          router.push('/dashboard')
          break
        case 'profile':
          router.push('/profile')
          break
        case 'security':
          router.push('/security')
          break
        case 'applications':
          router.push('/applications')
          break
        case 'verification':
          router.push('/verification')
          break
      }
    }
    
    // 处理移动端菜单点击
    const handleMobileMenuSelect = (key) => {
      handleMenuUpdate(key)
      mobileMenuVisible.value = false
    }
    
    // 处理用户菜单选择
    const handleUserMenuSelect = (key) => {
      switch (key) {
        case 'profile':
          router.push('/profile')
          break
        case 'security':
          router.push('/security')
          break
        case 'logout':
          handleLogout()
          break
      }
    }
    
    // 退出登录
    const handleLogout = async () => {
      try {
        await userStore.logout()
        message.success('退出登录成功')
        router.push('/login')
      } catch (error) {
        message.error('退出登录失败')
      }
    }
    
    return {
      userAvatar,
      username,
      activeKey,
      breadcrumbItems,
      menuOptions,
      userMenuOptions,
      handleMenuUpdate,
      handleUserMenuSelect,
      mobileMenuVisible,
      handleMobileMenuSelect
    }
  }
})
</script>

<style scoped>
.app-layout {
  height: 100%;
}

.layout-header {
  padding: 0 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
  position: relative;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  margin-right: 10px;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.logo-text {
  font-size: 20px;
  font-weight: bold;
  color: var(--text-color-1);
  margin: 0;
  padding: 0;
  white-space: nowrap;
}

.mobile-menu-button {
  display: none;
}

.header-center {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  margin-left: -40px; /* 向左偏移，使菜单更靠近Logo */
}

.header-right {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.horizontal-menu {
  display: flex;
  justify-content: center;
}

.user-dropdown {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.user-dropdown:hover {
  background-color: var(--hover-color);
}

.user-avatar {
  margin-right: 8px;
  transition: transform 0.2s ease;
}

.username {
  font-size: 14px;
  font-weight: 500;
  margin-right: 4px;
  color: var(--text-color-1);
}

.dropdown-icon {
  font-size: 16px;
  color: var(--text-color-3);
}

.layout-main {
  background-color: var(--body-color);
}

.main-content-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.sub-header {
  margin-bottom: 24px;
}

/* 导航菜单样式 */
:deep(.n-menu .n-menu-item-content) {
  border-radius: 16px;
}

:deep(.n-menu .n-menu-item-content-header) {
  border-radius: 16px;
}

:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content) {
  border-radius: 16px;
}

:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content::before) {
  border-radius: 16px;
}

:deep(.n-menu .n-menu-item:hover .n-menu-item-content) {
  border-radius: 16px;
}

/* 强制覆盖导航菜单样式 */
:deep(.n-menu-item-content) {
  border-radius: 16px !important;
  padding: 0 16px !important;
}

:deep(.n-menu-item-content::before) {
  border-radius: 16px !important;
}

:deep(.n-menu-item-content-header) {
  border-radius: 16px !important;
}

:deep(.n-menu-item:hover .n-menu-item-content),
:deep(.n-menu-item.n-menu-item--selected .n-menu-item-content) {
  background-color: var(--n-item-color-active) !important;
  border-radius: 16px !important;
}

:deep(.n-menu-item) {
  margin: 0 4px !important;
}


@media (max-width: 992px) {
  .main-content-wrapper {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .mobile-menu-button {
    display: block;
  }

  .header-center {
    display: none;
  }
  
  .logo-text {
    font-size: 18px;
  }
}

@media (max-width: 576px) {
  .username {
    display: none;
  }

  .dropdown-icon {
    display: none;
  }

  .user-dropdown {
    padding: 0;
  }

  .main-content-wrapper {
    padding: 12px;
  }

  .layout-header {
    padding: 0 16px;
  }
  
  .logo-text {
    font-size: 16px;
  }
}
</style>