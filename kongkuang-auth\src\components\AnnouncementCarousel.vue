<template>
  <div class="announcement-carousel">
    <n-carousel 
      :autoplay="true" 
      :interval="5000" 
      :show-dots="true"
      :show-arrow="true"
      :draggable="true"
      effect="slide"
      class="carousel-container"
    >
      <div 
        v-for="(announcement, index) in announcements" 
        :key="index"
        class="announcement-slide"
        :style="{ backgroundImage: `linear-gradient(135deg, ${announcement.gradient})` }"
      >
        <div class="announcement-content">
          <h3 class="announcement-title">{{ announcement.title }}</h3>
          <p class="announcement-subtitle">{{ announcement.subtitle }}</p>
        </div>
        <div class="announcement-decoration">
          <div class="decoration-circle circle-1"></div>
          <div class="decoration-circle circle-2"></div>
          <div class="decoration-circle circle-3"></div>
        </div>
      </div>
    </n-carousel>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue'
import { NCarousel } from 'naive-ui'

export default defineComponent({
  name: 'AnnouncementCarousel',
  components: {
    NCarousel
  },
  setup() {
    const announcements = ref([
      {
        title: '欢迎使用空旷账户',
        subtitle: '统一身份认证，畅享所有服务',
        gradient: '#667eea 0%, #764ba2 100%'
      },
      {
        title: '共同创造良好的网络环境',
        subtitle: '遵守社区规范，维护网络秩序',
        gradient: '#f093fb 0%, #f5576c 100%'
      },
      {
        title: '安全可靠的身份验证',
        subtitle: '多重保护，确保账户安全',
        gradient: '#4facfe 0%, #00f2fe 100%'
      }
    ])

    return {
      announcements
    }
  }
})
</script>

<style scoped>
.announcement-carousel {
  width: 100%;
  height: 200px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.carousel-container {
  height: 100%;
}

.announcement-slide {
  position: relative;
  height: 200px;
  display: flex;
  align-items: center;
  padding: 0 40px;
  color: white;
  overflow: hidden;
}

.announcement-content {
  position: relative;
  z-index: 2;
  max-width: 60%;
}

.announcement-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.announcement-subtitle {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.announcement-decoration {
  position: absolute;
  right: -50px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.circle-1 {
  width: 120px;
  height: 120px;
  top: -60px;
  right: 0;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 80px;
  height: 80px;
  top: 20px;
  right: 60px;
  animation: float 6s ease-in-out infinite 2s;
}

.circle-3 {
  width: 60px;
  height: 60px;
  top: -20px;
  right: 120px;
  animation: float 6s ease-in-out infinite 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 轮播控制样式 */
:deep(.n-carousel__dots) {
  bottom: 16px;
}

:deep(.n-carousel__dot) {
  background: rgba(255, 255, 255, 0.4);
  border: none;
  width: 8px;
  height: 8px;
}

:deep(.n-carousel__dot--active) {
  background: rgba(255, 255, 255, 0.9);
  width: 24px;
  border-radius: 4px;
}

:deep(.n-carousel__arrow) {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

:deep(.n-carousel__arrow:hover) {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .announcement-carousel {
    height: 160px;
    margin-bottom: 16px;
  }
  
  .announcement-slide {
    height: 160px;
    padding: 0 24px;
  }
  
  .announcement-title {
    font-size: 22px;
  }
  
  .announcement-subtitle {
    font-size: 14px;
  }
  
  .announcement-decoration {
    right: -30px;
  }
  
  .circle-1 {
    width: 80px;
    height: 80px;
  }
  
  .circle-2 {
    width: 60px;
    height: 60px;
  }
  
  .circle-3 {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 576px) {
  .announcement-carousel {
    height: 140px;
  }
  
  .announcement-slide {
    height: 140px;
    padding: 0 20px;
  }
  
  .announcement-title {
    font-size: 20px;
  }
  
  .announcement-subtitle {
    font-size: 13px;
  }
  
  .announcement-content {
    max-width: 70%;
  }
}
</style>
