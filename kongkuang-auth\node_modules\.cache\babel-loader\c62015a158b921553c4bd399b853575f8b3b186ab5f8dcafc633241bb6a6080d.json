{"ast": null, "code": "import { ref, onMounted, computed } from 'vue';\nimport { NSpin, NGrid, NGi, NCard, NAlert, NDescriptions, NDescriptionsItem, NButton, NTag, NList, NListItem, NIcon, NEmpty, NAvatar, NText, NTooltip, useMessage } from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport { InformationCircleOutline, CheckmarkCircleOutline, LockClosedOutline, ShieldCheckmarkOutline, PersonOutline, WalletOutline, AppsOutline, LogOutOutline } from '@vicons/ionicons5';\nexport default {\n  __name: 'Dashboard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const userStore = useUserStore();\n    const message = useMessage();\n    const dialog = useDialog();\n    const recentApps = ref([]);\n    const verificationStatus = ref({\n      level1Completed: false,\n      level2Completed: false,\n      level1Info: null,\n      level2Info: null\n    });\n\n    // 检查是否为深色模式\n    const isDarkMode = computed(() => {\n      const themeMode = localStorage.getItem('theme') || 'system';\n      if (themeMode === 'system') {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      return themeMode === 'dark';\n    });\n    const formatDateTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      // Using toLocaleString for a more standard format, customize as needed\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).replace(/\\//g, '-');\n    };\n\n    // 格式化相对时间（如：3天前，2小时前）\n    const formatRelativeTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      const now = new Date();\n      const diffMs = now - date;\n      const diffSec = Math.floor(diffMs / 1000);\n      const diffMin = Math.floor(diffSec / 60);\n      const diffHour = Math.floor(diffMin / 60);\n      const diffDay = Math.floor(diffHour / 24);\n      const diffMonth = Math.floor(diffDay / 30);\n      const diffYear = Math.floor(diffMonth / 12);\n      if (diffYear > 0) {\n        return `${diffYear}年前`;\n      } else if (diffMonth > 0) {\n        return `${diffMonth}个月前`;\n      } else if (diffDay > 0) {\n        return `${diffDay}天前`;\n      } else if (diffHour > 0) {\n        return `${diffHour}小时前`;\n      } else if (diffMin > 0) {\n        return `${diffMin}分钟前`;\n      } else {\n        return '刚刚';\n      }\n    };\n\n    // 获取默认头像\n    const getDefaultAvatar = () => {\n      return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n    };\n\n    // 手机号码脱敏\n    const maskPhone = phone => {\n      if (!phone) return '';\n      return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n    };\n\n    // 处理退出登录\n    const handleLogout = async () => {\n      if (confirm('您确定要退出登录吗？')) {\n        try {\n          const apiClient = getApiClient();\n          await apiClient.post('/auth/logout');\n          userStore.logout();\n          message.success('已成功退出登录');\n          window.location.href = '/login';\n        } catch (error) {\n          console.error('退出登录失败:', error);\n          message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n        }\n      }\n    };\n    const fetchDashboardData = async () => {\n      loading.value = true;\n      try {\n        const apiClient = getApiClient();\n\n        // 先只获取仪表盘数据\n        const dashboardResponse = await apiClient.get('/dashboard');\n\n        // 处理仪表盘数据\n        if (dashboardResponse.data && dashboardResponse.data.success) {\n          // 更新用户信息，使用后端返回的格式化数据\n          if (dashboardResponse.data.user) {\n            userStore.user = {\n              ...userStore.user,\n              ...dashboardResponse.data.user,\n              // 使用后端返回的格式化时间，如果没有则使用原始数据\n              createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n              lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n              lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n            };\n          }\n\n          // 更新应用列表\n          recentApps.value = dashboardResponse.data.recentApps || [];\n\n          // 如果后端返回了认证状态，使用它\n          if (dashboardResponse.data.verificationStatus) {\n            verificationStatus.value = dashboardResponse.data.verificationStatus;\n          }\n        }\n        console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n        // 尝试获取认证状态（如果失败不影响主要功能）\n        try {\n          const verificationResponse = await apiClient.get('/users/verification-status');\n          if (verificationResponse.data && verificationResponse.data.success) {\n            verificationStatus.value = {\n              level1Completed: verificationResponse.data.level1Completed || false,\n              level2Completed: verificationResponse.data.level2Completed || false,\n              level1Info: verificationResponse.data.level1Info || null,\n              level2Info: verificationResponse.data.level2Info || null\n            };\n          }\n        } catch (verificationError) {\n          console.warn('获取认证状态失败:', verificationError);\n          // 不显示错误，使用默认值\n        }\n      } catch (error) {\n        console.error(\"Failed to fetch dashboard data:\", error);\n        message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        loading.value = false;\n      }\n    };\n    onMounted(() => {\n      // We need user data, if not present, maybe fetch it or rely on login flow\n      if (!userStore.user) {\n        // This case might happen on a page refresh, you might want to fetch user data\n        // For now, we assume user data is populated from login\n      }\n      fetchDashboardData();\n    });\n    const __returned__ = {\n      loading,\n      userStore,\n      message,\n      dialog,\n      recentApps,\n      verificationStatus,\n      isDarkMode,\n      formatDateTime,\n      formatRelativeTime,\n      getDefaultAvatar,\n      maskPhone,\n      handleLogout,\n      fetchDashboardData,\n      ref,\n      onMounted,\n      computed,\n      get NSpin() {\n        return NSpin;\n      },\n      get NGrid() {\n        return NGrid;\n      },\n      get NGi() {\n        return NGi;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NDescriptions() {\n        return NDescriptions;\n      },\n      get NDescriptionsItem() {\n        return NDescriptionsItem;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NList() {\n        return NList;\n      },\n      get NListItem() {\n        return NListItem;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NEmpty() {\n        return NEmpty;\n      },\n      get NAvatar() {\n        return NAvatar;\n      },\n      get NText() {\n        return NText;\n      },\n      get NTooltip() {\n        return NTooltip;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      },\n      get InformationCircleOutline() {\n        return InformationCircleOutline;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get LockClosedOutline() {\n        return LockClosedOutline;\n      },\n      get ShieldCheckmarkOutline() {\n        return ShieldCheckmarkOutline;\n      },\n      get PersonOutline() {\n        return PersonOutline;\n      },\n      get WalletOutline() {\n        return WalletOutline;\n      },\n      get AppsOutline() {\n        return AppsOutline;\n      },\n      get LogOutOutline() {\n        return LogOutOutline;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "NSpin", "NGrid", "NGi", "NCard", "N<PERSON><PERSON><PERSON>", "NDescriptions", "NDescriptionsItem", "NButton", "NTag", "NList", "NListItem", "NIcon", "NEmpty", "NAvatar", "NText", "NTooltip", "useMessage", "useUserStore", "getApiClient", "InformationCircleOutline", "CheckmarkCircleOutline", "LockClosedOutline", "ShieldCheckmarkOutline", "PersonOutline", "WalletOutline", "AppsOutline", "LogOutOutline", "loading", "userStore", "message", "dialog", "useDialog", "recentApps", "verificationStatus", "level1Completed", "level2Completed", "level1Info", "level2Info", "isDarkMode", "themeMode", "localStorage", "getItem", "window", "matchMedia", "matches", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "formatRelativeTime", "now", "diffMs", "diffSec", "Math", "floor", "diffMin", "diffHour", "diffDay", "diff<PERSON><PERSON><PERSON>", "diffYear", "getDefaultAvatar", "maskPhone", "phone", "handleLogout", "confirm", "apiClient", "post", "logout", "success", "location", "href", "error", "console", "response", "data", "fetchDashboardData", "value", "dashboardResponse", "get", "user", "createdAt", "registrationTime", "formatted", "lastLoginAt", "lastLoginTime", "lastLoginIp", "log", "verificationResponse", "verificationError", "warn"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <template #header>\n                <div class=\"card-header\">\n                  <h3>用户信息</h3>\n                  <div class=\"user-avatar\">\n                    <n-avatar\n                      :size=\"48\"\n                      :src=\"userStore.user?.avatar\"\n                      :fallback-src=\"getDefaultAvatar()\"\n                    >\n                      {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}\n                    </n-avatar>\n                  </div>\n                </div>\n              </template>\n\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"用户ID\">\n                  <n-text code>{{ userStore.user?.id }}</n-text>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户名\">\n                  <n-text strong>{{ userStore.user?.username }}</n-text>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名认证状态\">\n                  <div class=\"verification-status\">\n                    <div class=\"status-item\">\n                      <span class=\"status-label\">一级认证：</span>\n                      <n-tag\n                        :bordered=\"false\"\n                        :type=\"verificationStatus.level1Completed ? 'success' : 'default'\"\n                        size=\"small\"\n                      >\n                        {{ verificationStatus.level1Completed ? '已完成' : '未完成' }}\n                      </n-tag>\n                      <n-tooltip v-if=\"verificationStatus.level1Completed && verificationStatus.level1Info\">\n                        <template #trigger>\n                          <n-icon size=\"14\" color=\"#18a058\" style=\"margin-left: 4px;\">\n                            <information-circle-outline />\n                          </n-icon>\n                        </template>\n                        <div>\n                          <p>认证时间: {{ formatDateTime(verificationStatus.level1Info.verifiedAt) }}</p>\n                          <p>支付方式: {{ verificationStatus.level1Info.paymentMethod === 'alipay' ? '支付宝' : '微信' }}</p>\n                          <p>过期时间: {{ formatDateTime(verificationStatus.level1Info.expiresAt) }}</p>\n                        </div>\n                      </n-tooltip>\n                    </div>\n                    <div class=\"status-item\">\n                      <span class=\"status-label\">二级认证：</span>\n                      <n-tag\n                        :bordered=\"false\"\n                        :type=\"verificationStatus.level2Completed ? 'success' : 'default'\"\n                        size=\"small\"\n                      >\n                        {{ verificationStatus.level2Completed ? '已完成' : '未完成' }}\n                      </n-tag>\n                      <n-tooltip v-if=\"verificationStatus.level2Completed && verificationStatus.level2Info\">\n                        <template #trigger>\n                          <n-icon size=\"14\" color=\"#18a058\" style=\"margin-left: 4px;\">\n                            <information-circle-outline />\n                          </n-icon>\n                        </template>\n                        <div>\n                          <p>真实姓名: {{ verificationStatus.level2Info.realName }}</p>\n                          <p>认证时间: {{ formatDateTime(verificationStatus.level2Info.verifiedAt) }}</p>\n                        </div>\n                      </n-tooltip>\n                    </div>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"账户状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">\n                    <template #icon>\n                      <n-icon><checkmark-circle-outline /></n-icon>\n                    </template>\n                    正常\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录\">\n                  <div class=\"login-info\">\n                    <div>{{ formatDateTime(userStore.user?.lastLoginAt) }}</div>\n                    <div class=\"login-ip\">IP: {{ userStore.user?.lastLoginIp || 'N/A' }}</div>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <div class=\"email-info\">\n                      <span>{{ userStore.user?.email }}</span>\n                      <n-tag\n                        v-if=\"userStore.user?.is_email_verified\"\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        已验证\n                      </n-tag>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"warning\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        未验证\n                      </n-tag>\n                    </div>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定手机\" :span=\"2\" v-if=\"userStore.user?.phone\">\n                  <div class=\"phone-item\">\n                    <div class=\"phone-info\">\n                      <span>{{ maskPhone(userStore.user?.phone) }}</span>\n                      <n-tag\n                        v-if=\"userStore.user?.is_phone_verified\"\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        已验证\n                      </n-tag>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"warning\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        未验证\n                      </n-tag>\n                    </div>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n              </n-descriptions>\n\n              <div class=\"action-buttons\">\n                <n-button type=\"primary\" ghost @click=\"$router.push('/security')\">\n                  <template #icon>\n                    <n-icon><lock-closed-outline /></n-icon>\n                  </template>\n                  安全设置\n                </n-button>\n                <n-button type=\"primary\" ghost @click=\"$router.push('/verification')\">\n                  <template #icon>\n                    <n-icon><shield-checkmark-outline /></n-icon>\n                  </template>\n                  实名认证\n                </n-button>\n                <n-button type=\"primary\" ghost @click=\"$router.push('/profile')\">\n                  <template #icon>\n                    <n-icon><person-outline /></n-icon>\n                  </template>\n                  个人资料\n                </n-button>\n              </div>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <!-- 认证状态卡片 -->\n              <n-card title=\"认证状态\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"verification-summary\">\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <wallet-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">一级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level1Completed ? '识脸支付认证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level1Completed && verificationStatus.level1Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level1Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level1Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <shield-checkmark-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">二级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level2Completed ? '二要素验证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level2Completed && verificationStatus.level2Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level2Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level2Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 快捷操作卡片 -->\n              <n-card title=\"快捷操作\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"quick-actions\">\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/applications')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><apps-outline /></n-icon>\n                    </template>\n                    我的应用\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/security')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><lock-closed-outline /></n-icon>\n                    </template>\n                    安全中心\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"handleLogout\"\n                  >\n                    <template #icon>\n                      <n-icon><log-out-outline /></n-icon>\n                    </template>\n                    退出登录\n                  </n-button>\n                </div>\n              </n-card>\n\n              <!-- 授权服务卡片 -->\n              <n-card title=\"授权服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <n-list :show-divider=\"false\">\n                  <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                    <div class=\"service-info\">\n                      <div class=\"service-name\">{{ app.name }}</div>\n                      <div class=\"service-time\">{{ formatRelativeTime(app.lastUsed) }}</div>\n                    </div>\n                  </n-list-item>\n                  <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无授权服务\" size=\"small\" />\n                </n-list>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  NAvatar,\n  NText,\n  NTooltip,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  InformationCircleOutline,\n  CheckmarkCircleOutline,\n  LockClosedOutline,\n  ShieldCheckmarkOutline,\n  PersonOutline,\n  WalletOutline,\n  AppsOutline,\n  LogOutOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\nconst dialog = useDialog();\n\nconst recentApps = ref([]);\nconst verificationStatus = ref({\n  level1Completed: false,\n  level2Completed: false,\n  level1Info: null,\n  level2Info: null\n});\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 格式化相对时间（如：3天前，2小时前）\nconst formatRelativeTime = (dateString) => {\n  if (!dateString) return 'N/A';\n\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffMs = now - date;\n  const diffSec = Math.floor(diffMs / 1000);\n  const diffMin = Math.floor(diffSec / 60);\n  const diffHour = Math.floor(diffMin / 60);\n  const diffDay = Math.floor(diffHour / 24);\n  const diffMonth = Math.floor(diffDay / 30);\n  const diffYear = Math.floor(diffMonth / 12);\n\n  if (diffYear > 0) {\n    return `${diffYear}年前`;\n  } else if (diffMonth > 0) {\n    return `${diffMonth}个月前`;\n  } else if (diffDay > 0) {\n    return `${diffDay}天前`;\n  } else if (diffHour > 0) {\n    return `${diffHour}小时前`;\n  } else if (diffMin > 0) {\n    return `${diffMin}分钟前`;\n  } else {\n    return '刚刚';\n  }\n};\n\n// 获取默认头像\nconst getDefaultAvatar = () => {\n  return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n};\n\n// 手机号码脱敏\nconst maskPhone = (phone) => {\n  if (!phone) return '';\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n};\n\n// 处理退出登录\nconst handleLogout = async () => {\n  if (confirm('您确定要退出登录吗？')) {\n    try {\n      const apiClient = getApiClient();\n      await apiClient.post('/auth/logout');\n      userStore.logout();\n      message.success('已成功退出登录');\n      window.location.href = '/login';\n    } catch (error) {\n      console.error('退出登录失败:', error);\n      message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n    }\n  }\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n\n    // 先只获取仪表盘数据\n    const dashboardResponse = await apiClient.get('/dashboard');\n\n    // 处理仪表盘数据\n    if (dashboardResponse.data && dashboardResponse.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (dashboardResponse.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...dashboardResponse.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = dashboardResponse.data.recentApps || [];\n\n      // 如果后端返回了认证状态，使用它\n      if (dashboardResponse.data.verificationStatus) {\n        verificationStatus.value = dashboardResponse.data.verificationStatus;\n      }\n    }\n\n    console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n    // 尝试获取认证状态（如果失败不影响主要功能）\n    try {\n      const verificationResponse = await apiClient.get('/users/verification-status');\n      if (verificationResponse.data && verificationResponse.data.success) {\n        verificationStatus.value = {\n          level1Completed: verificationResponse.data.level1Completed || false,\n          level2Completed: verificationResponse.data.level2Completed || false,\n          level1Info: verificationResponse.data.level1Info || null,\n          level2Info: verificationResponse.data.level2Info || null\n        };\n      }\n    } catch (verificationError) {\n      console.warn('获取认证状态失败:', verificationError);\n      // 不显示错误，使用默认值\n    }\n\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.verification-status {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-label {\n  font-weight: 500;\n  min-width: 70px;\n}\n\n.login-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.login-ip {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n.email-item,\n.phone-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.email-info,\n.phone-info {\n  display: flex;\n  align-items: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.verification-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.verification-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.verification-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.verification-icon {\n  flex-shrink: 0;\n}\n\n.verification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.verification-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n\n.verification-desc {\n  font-size: 12px;\n  color: var(--n-text-color-2);\n  margin-bottom: 2px;\n}\n\n.verification-time {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n}\n\n.verification-action {\n  flex-shrink: 0;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n.service-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  width: 100%;\n}\n\n.service-name {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": "AAwUA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SACEC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,QAAQ,EACRC,UAAS,QACJ,UAAU;AACjB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SACEC,wBAAwB,EACxBC,sBAAsB,EACtBC,iBAAiB,EACjBC,sBAAsB,EACtBC,aAAa,EACbC,aAAa,EACbC,WAAW,EACXC,aAAY,QACP,mBAAmB;;;;;;;IAE1B,MAAMC,OAAO,GAAG9B,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAM+B,SAAS,GAAGX,YAAY,CAAC,CAAC;IAChC,MAAMY,OAAO,GAAGb,UAAU,CAAC,CAAC;IAC5B,MAAMc,MAAM,GAAGC,SAAS,CAAC,CAAC;IAE1B,MAAMC,UAAU,GAAGnC,GAAG,CAAC,EAAE,CAAC;IAC1B,MAAMoC,kBAAkB,GAAGpC,GAAG,CAAC;MAC7BqC,eAAe,EAAE,KAAK;MACtBC,eAAe,EAAE,KAAK;MACtBC,UAAU,EAAE,IAAI;MAChBC,UAAU,EAAE;IACd,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAGvC,QAAQ,CAAC,MAAM;MAChC,MAAMwC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ;MAC3D,IAAIF,SAAS,KAAK,QAAQ,EAAE;QAC1B,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAClE;MACA,OAAOL,SAAS,KAAK,MAAM;IAC7B,CAAC,CAAC;IAEF,MAAMM,cAAc,GAAIC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC;MACA,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACxB,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAIZ,UAAU,IAAK;MACzC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAE7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMa,GAAG,GAAG,IAAIX,IAAI,CAAC,CAAC;MACtB,MAAMY,MAAM,GAAGD,GAAG,GAAGZ,IAAI;MACzB,MAAMc,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,GAAG,IAAI,CAAC;MACzC,MAAMI,OAAO,GAAGF,IAAI,CAACC,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;MACxC,MAAMI,QAAQ,GAAGH,IAAI,CAACC,KAAK,CAACC,OAAO,GAAG,EAAE,CAAC;MACzC,MAAME,OAAO,GAAGJ,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG,EAAE,CAAC;MACzC,MAAME,SAAS,GAAGL,IAAI,CAACC,KAAK,CAACG,OAAO,GAAG,EAAE,CAAC;MAC1C,MAAME,QAAQ,GAAGN,IAAI,CAACC,KAAK,CAACI,SAAS,GAAG,EAAE,CAAC;MAE3C,IAAIC,QAAQ,GAAG,CAAC,EAAE;QAChB,OAAO,GAAGA,QAAQ,IAAI;MACxB,CAAC,MAAM,IAAID,SAAS,GAAG,CAAC,EAAE;QACxB,OAAO,GAAGA,SAAS,KAAK;MAC1B,CAAC,MAAM,IAAID,OAAO,GAAG,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,IAAI;MACvB,CAAC,MAAM,IAAID,QAAQ,GAAG,CAAC,EAAE;QACvB,OAAO,GAAGA,QAAQ,KAAK;MACzB,CAAC,MAAM,IAAID,OAAO,GAAG,CAAC,EAAE;QACtB,OAAO,GAAGA,OAAO,KAAK;MACxB,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC;;IAED;IACA,MAAMK,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,OAAO,4DAA4D;IACrE,CAAC;;IAED;IACA,MAAMC,SAAS,GAAIC,KAAK,IAAK;MAC3B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;MACrB,OAAOA,KAAK,CAACd,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;IACzD,CAAC;;IAED;IACA,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/B,IAAIC,OAAO,CAAC,YAAY,CAAC,EAAE;QACzB,IAAI;UACF,MAAMC,SAAS,GAAGxD,YAAY,CAAC,CAAC;UAChC,MAAMwD,SAAS,CAACC,IAAI,CAAC,cAAc,CAAC;UACpC/C,SAAS,CAACgD,MAAM,CAAC,CAAC;UAClB/C,OAAO,CAACgD,OAAO,CAAC,SAAS,CAAC;UAC1BnC,MAAM,CAACoC,QAAQ,CAACC,IAAI,GAAG,QAAQ;QACjC,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/BnD,OAAO,CAACmD,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACE,QAAQ,EAAEC,IAAI,EAAEtD,OAAO,IAAImD,KAAK,CAACnD,OAAO,CAAC,CAAC;QAC9E;MACF;IACF,CAAC;IAED,MAAMuD,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrCzD,OAAO,CAAC0D,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMX,SAAS,GAAGxD,YAAY,CAAC,CAAC;;QAEhC;QACA,MAAMoE,iBAAiB,GAAG,MAAMZ,SAAS,CAACa,GAAG,CAAC,YAAY,CAAC;;QAE3D;QACA,IAAID,iBAAiB,CAACH,IAAI,IAAIG,iBAAiB,CAACH,IAAI,CAACN,OAAO,EAAE;UAC5D;UACA,IAAIS,iBAAiB,CAACH,IAAI,CAACK,IAAI,EAAE;YAC/B5D,SAAS,CAAC4D,IAAI,GAAG;cACf,GAAG5D,SAAS,CAAC4D,IAAI;cACjB,GAAGF,iBAAiB,CAACH,IAAI,CAACK,IAAI;cAC9B;cACAC,SAAS,EAAEH,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACE,gBAAgB,EAAEC,SAAS,IAAI/D,SAAS,CAAC4D,IAAI,EAAEC,SAAS;cAC/FG,WAAW,EAAEN,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACK,aAAa,EAAEF,SAAS,IAAI/D,SAAS,CAAC4D,IAAI,EAAEI,WAAW;cAChGE,WAAW,EAAER,iBAAiB,CAACH,IAAI,CAACK,IAAI,CAACM,WAAW,IAAIlE,SAAS,CAAC4D,IAAI,EAAEM;YAC1E,CAAC;UACH;;UAEA;UACA9D,UAAU,CAACqD,KAAK,GAAGC,iBAAiB,CAACH,IAAI,CAACnD,UAAU,IAAI,EAAE;;UAE1D;UACA,IAAIsD,iBAAiB,CAACH,IAAI,CAAClD,kBAAkB,EAAE;YAC7CA,kBAAkB,CAACoD,KAAK,GAAGC,iBAAiB,CAACH,IAAI,CAAClD,kBAAkB;UACtE;QACF;QAEAgD,OAAO,CAACc,GAAG,CAAC,YAAY,EAAET,iBAAiB,CAACH,IAAI,CAAC;;QAEjD;QACA,IAAI;UACF,MAAMa,oBAAoB,GAAG,MAAMtB,SAAS,CAACa,GAAG,CAAC,4BAA4B,CAAC;UAC9E,IAAIS,oBAAoB,CAACb,IAAI,IAAIa,oBAAoB,CAACb,IAAI,CAACN,OAAO,EAAE;YAClE5C,kBAAkB,CAACoD,KAAK,GAAG;cACzBnD,eAAe,EAAE8D,oBAAoB,CAACb,IAAI,CAACjD,eAAe,IAAI,KAAK;cACnEC,eAAe,EAAE6D,oBAAoB,CAACb,IAAI,CAAChD,eAAe,IAAI,KAAK;cACnEC,UAAU,EAAE4D,oBAAoB,CAACb,IAAI,CAAC/C,UAAU,IAAI,IAAI;cACxDC,UAAU,EAAE2D,oBAAoB,CAACb,IAAI,CAAC9C,UAAU,IAAI;YACtD,CAAC;UACH;QACF,CAAC,CAAC,OAAO4D,iBAAiB,EAAE;UAC1BhB,OAAO,CAACiB,IAAI,CAAC,WAAW,EAAED,iBAAiB,CAAC;UAC5C;QACF;MAEF,CAAC,CAAC,OAAOjB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDnD,OAAO,CAACmD,KAAK,CAAC,aAAa,IAAIA,KAAK,CAACE,QAAQ,EAAEC,IAAI,EAAEtD,OAAO,IAAImD,KAAK,CAACnD,OAAO,CAAC,CAAC;MACjF,CAAC,SAAS;QACRF,OAAO,CAAC0D,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAEGvF,SAAS,CAAC,MAAM;MAClB;MACA,IAAI,CAAC8B,SAAS,CAAC4D,IAAI,EAAE;QACnB;QACA;MAAA;MAEFJ,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}