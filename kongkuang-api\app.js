require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const config = require('./config/config');
const path = require('path');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const applicationRoutes = require('./routes/applications');
const oauthRoutes = require('./routes/oauth');
const mcServerRoutes = require('./routes/mcserver');
const dashboardRoutes = require('./routes/dashboard');
const verificationRoutes = require('./routes/verification');

// 创建Express应用
const app = express();

// 安全性增强
app.use(helmet());

// 启用CORS
app.use(cors({
  origin: config.cors.origins,
  methods: config.cors.methods,
  allowedHeaders: config.cors.allowed_headers,
  credentials: true
}));

// 请求体解析
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Cookie解析
app.use(cookieParser());

// 压缩响应
app.use(compression());

// 简单的请求日志中间件
if (config.server.env === 'development') {
  app.use((req, res, next) => {
    // 跳过健康检查和静态资源的日志
    if (req.url !== '/health' && !req.url.startsWith('/static/')) {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        if (duration > 1000) {  // 只记录超过1秒的请求
          console.log(`${req.method} ${req.url} ${res.statusCode} - ${duration}ms`);
        }
      });
    }
    next();
  });
}

// 静态文件服务
app.use('/static', express.static(path.join(__dirname, 'public')));

// 主路由
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: '空旷API运行正常',
    version: '1.0.0',
    time: new Date().toISOString()
  });
});

// 路由
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/applications', applicationRoutes);
app.use('/oauth', oauthRoutes);
app.use('/mcserver', mcServerRoutes);
app.use('/dashboard', dashboardRoutes);
app.use('/verification', verificationRoutes);

// 健康检查端点
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({ 
    error: 'Not Found',
    message: '请求的资源不存在'
  });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  // 只在开发环境打印错误堆栈
  const errorDetails = config.server.env === 'development' ? err.stack : {};
  
  // 记录错误
  console.error('应用错误:', err.message);
  
  // 发送错误响应
  res.status(err.status || 500).json({
    error: err.name || 'InternalServerError',
    message: err.message || '服务器内部错误',
    ...(config.server.env === 'development' ? { stack: err.stack } : {})
  });
});

// 导出app供其他模块使用
module.exports = app; 