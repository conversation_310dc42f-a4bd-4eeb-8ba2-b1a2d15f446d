{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"card-header\"\n};\nconst _hoisted_5 = {\n  class: \"user-avatar\"\n};\nconst _hoisted_6 = {\n  class: \"verification-status\"\n};\nconst _hoisted_7 = {\n  class: \"status-item\"\n};\nconst _hoisted_8 = {\n  class: \"status-item\"\n};\nconst _hoisted_9 = {\n  class: \"login-info\"\n};\nconst _hoisted_10 = {\n  class: \"login-ip\"\n};\nconst _hoisted_11 = {\n  class: \"email-item\"\n};\nconst _hoisted_12 = {\n  class: \"email-info\"\n};\nconst _hoisted_13 = {\n  class: \"phone-item\"\n};\nconst _hoisted_14 = {\n  class: \"phone-info\"\n};\nconst _hoisted_15 = {\n  class: \"action-buttons\"\n};\nconst _hoisted_16 = {\n  class: \"side-cards\"\n};\nconst _hoisted_17 = {\n  class: \"verification-summary\"\n};\nconst _hoisted_18 = {\n  class: \"verification-item\"\n};\nconst _hoisted_19 = {\n  class: \"verification-icon\"\n};\nconst _hoisted_20 = {\n  class: \"verification-content\"\n};\nconst _hoisted_21 = {\n  class: \"verification-desc\"\n};\nconst _hoisted_22 = {\n  key: 0,\n  class: \"verification-time\"\n};\nconst _hoisted_23 = {\n  class: \"verification-action\"\n};\nconst _hoisted_24 = {\n  class: \"verification-item\"\n};\nconst _hoisted_25 = {\n  class: \"verification-icon\"\n};\nconst _hoisted_26 = {\n  class: \"verification-content\"\n};\nconst _hoisted_27 = {\n  class: \"verification-desc\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"verification-time\"\n};\nconst _hoisted_29 = {\n  class: \"verification-action\"\n};\nconst _hoisted_30 = {\n  class: \"quick-actions\"\n};\nconst _hoisted_31 = {\n  class: \"service-info\"\n};\nconst _hoisted_32 = {\n  class: \"service-name\"\n};\nconst _hoisted_33 = {\n  class: \"service-time\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [7]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 3,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_cache[8] || (_cache[8] = _createElementVNode(\"h3\", null, \"用户信息\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NAvatar\"], {\n            size: 48,\n            src: $setup.userStore.user?.avatar,\n            \"fallback-src\": $setup.getDefaultAvatar()\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.username?.charAt(0)?.toUpperCase()), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"src\", \"fallback-src\"])])])]),\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户ID\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NText\"], {\n                code: \"\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户名\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NText\"], {\n                strong: \"\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.username), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名认证状态\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", {\n                class: \"status-label\"\n              }, \"一级认证：\", -1 /* CACHED */)), _createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: $setup.verificationStatus.level1Completed ? 'success' : 'default',\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.verificationStatus.level1Completed ? '已完成' : '未完成'), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"]), $setup.verificationStatus.level1Completed && $setup.verificationStatus.level1Info ? (_openBlock(), _createBlock($setup[\"NTooltip\"], {\n                key: 0\n              }, {\n                trigger: _withCtx(() => [_createVNode($setup[\"NIcon\"], {\n                  size: \"14\",\n                  color: \"#18a058\",\n                  style: {\n                    \"margin-left\": \"4px\"\n                  }\n                }, {\n                  default: _withCtx(() => [_createVNode($setup[\"InformationCircleOutline\"])]),\n                  _: 1 /* STABLE */\n                })]),\n                default: _withCtx(() => [_createElementVNode(\"div\", null, [_createElementVNode(\"p\", null, \"认证时间: \" + _toDisplayString($setup.formatDateTime($setup.verificationStatus.level1Info.verifiedAt)), 1 /* TEXT */), _createElementVNode(\"p\", null, \"支付方式: \" + _toDisplayString($setup.verificationStatus.level1Info.paymentMethod === 'alipay' ? '支付宝' : '微信'), 1 /* TEXT */), _createElementVNode(\"p\", null, \"过期时间: \" + _toDisplayString($setup.formatDateTime($setup.verificationStatus.level1Info.expiresAt)), 1 /* TEXT */)])]),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_8, [_cache[10] || (_cache[10] = _createElementVNode(\"span\", {\n                class: \"status-label\"\n              }, \"二级认证：\", -1 /* CACHED */)), _createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: $setup.verificationStatus.level2Completed ? 'success' : 'default',\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.verificationStatus.level2Completed ? '已完成' : '未完成'), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"]), $setup.verificationStatus.level2Completed && $setup.verificationStatus.level2Info ? (_openBlock(), _createBlock($setup[\"NTooltip\"], {\n                key: 0\n              }, {\n                trigger: _withCtx(() => [_createVNode($setup[\"NIcon\"], {\n                  size: \"14\",\n                  color: \"#18a058\",\n                  style: {\n                    \"margin-left\": \"4px\"\n                  }\n                }, {\n                  default: _withCtx(() => [_createVNode($setup[\"InformationCircleOutline\"])]),\n                  _: 1 /* STABLE */\n                })]),\n                default: _withCtx(() => [_createElementVNode(\"div\", null, [_createElementVNode(\"p\", null, \"真实姓名: \" + _toDisplayString($setup.verificationStatus.level2Info.realName), 1 /* TEXT */), _createElementVNode(\"p\", null, \"认证时间: \" + _toDisplayString($setup.formatDateTime($setup.verificationStatus.level2Info.verifiedAt)), 1 /* TEXT */)])]),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)])])]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"账户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n                  default: _withCtx(() => [_createVNode($setup[\"CheckmarkCircleOutline\"])]),\n                  _: 1 /* STABLE */\n                })]),\n                default: _withCtx(() => [_cache[11] || (_cache[11] = _createTextVNode(\" 正常 \"))]),\n                _: 1 /* STABLE */,\n                __: [11]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录\"\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", null, _toDisplayString($setup.formatDateTime($setup.userStore.user?.lastLoginAt)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, \"IP: \" + _toDisplayString($setup.userStore.user?.lastLoginIp || 'N/A'), 1 /* TEXT */)])]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), $setup.userStore.user?.is_email_verified ? (_openBlock(), _createBlock($setup[\"NTag\"], {\n                key: 0,\n                bordered: false,\n                type: \"success\",\n                size: \"tiny\",\n                style: {\n                  \"margin-left\": \"8px\"\n                }\n              }, {\n                default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\" 已验证 \")])),\n                _: 1 /* STABLE */,\n                __: [12]\n              })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n                key: 1,\n                bordered: false,\n                type: \"warning\",\n                size: \"tiny\",\n                style: {\n                  \"margin-left\": \"8px\"\n                }\n              }, {\n                default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\" 未验证 \")])),\n                _: 1 /* STABLE */,\n                __: [13]\n              }))]), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [14]\n              })])]),\n              _: 1 /* STABLE */\n            }), $setup.userStore.user?.phone ? (_openBlock(), _createBlock($setup[\"NDescriptionsItem\"], {\n              key: 0,\n              label: \"绑定手机\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"span\", null, _toDisplayString($setup.maskPhone($setup.userStore.user?.phone)), 1 /* TEXT */), $setup.userStore.user?.is_phone_verified ? (_openBlock(), _createBlock($setup[\"NTag\"], {\n                key: 0,\n                bordered: false,\n                type: \"success\",\n                size: \"tiny\",\n                style: {\n                  \"margin-left\": \"8px\"\n                }\n              }, {\n                default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\" 已验证 \")])),\n                _: 1 /* STABLE */,\n                __: [15]\n              })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n                key: 1,\n                bordered: false,\n                type: \"warning\",\n                size: \"tiny\",\n                style: {\n                  \"margin-left\": \"8px\"\n                }\n              }, {\n                default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\" 未验证 \")])),\n                _: 1 /* STABLE */,\n                __: [16]\n              }))]), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [17]\n              })])]),\n              _: 1 /* STABLE */\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"div\", _hoisted_15, [_createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security'))\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"LockClosedOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[18] || (_cache[18] = _createTextVNode(\" 安全设置 \"))]),\n            _: 1 /* STABLE */,\n            __: [18]\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/verification'))\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"ShieldCheckmarkOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[19] || (_cache[19] = _createTextVNode(\" 实名认证 \"))]),\n            _: 1 /* STABLE */,\n            __: [19]\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/profile'))\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"PersonOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[20] || (_cache[20] = _createTextVNode(\" 个人资料 \"))]),\n            _: 1 /* STABLE */,\n            __: [20]\n          })])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_16, [_createCommentVNode(\" 认证状态卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"认证状态\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createVNode($setup[\"NIcon\"], {\n            size: \"20\",\n            color: $setup.verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"WalletOutline\"])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_20, [_cache[21] || (_cache[21] = _createElementVNode(\"div\", {\n            class: \"verification-title\"\n          }, \"一级认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_21, _toDisplayString($setup.verificationStatus.level1Completed ? '识脸支付认证' : '未完成'), 1 /* TEXT */), $setup.verificationStatus.level1Completed && $setup.verificationStatus.level1Info ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, _toDisplayString($setup.formatRelativeTime($setup.verificationStatus.level1Info.verifiedAt)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_23, [!$setup.verificationStatus.level1Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n            key: 0,\n            text: \"\",\n            type: \"primary\",\n            size: \"small\",\n            onClick: _cache[3] || (_cache[3] = $event => _ctx.$router.push('/verification'))\n          }, {\n            default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 去认证 \")])),\n            _: 1 /* STABLE */,\n            __: [22]\n          })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n            key: 1,\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 已完成 \")])),\n            _: 1 /* STABLE */,\n            __: [23]\n          }))])]), _createElementVNode(\"div\", _hoisted_24, [_createElementVNode(\"div\", _hoisted_25, [_createVNode($setup[\"NIcon\"], {\n            size: \"20\",\n            color: $setup.verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"ShieldCheckmarkOutline\"])]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_26, [_cache[24] || (_cache[24] = _createElementVNode(\"div\", {\n            class: \"verification-title\"\n          }, \"二级认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_27, _toDisplayString($setup.verificationStatus.level2Completed ? '二要素验证' : '未完成'), 1 /* TEXT */), $setup.verificationStatus.level2Completed && $setup.verificationStatus.level2Info ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, _toDisplayString($setup.formatRelativeTime($setup.verificationStatus.level2Info.verifiedAt)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_29, [!$setup.verificationStatus.level2Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n            key: 0,\n            text: \"\",\n            type: \"primary\",\n            size: \"small\",\n            onClick: _cache[4] || (_cache[4] = $event => _ctx.$router.push('/verification'))\n          }, {\n            default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 去认证 \")])),\n            _: 1 /* STABLE */,\n            __: [25]\n          })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n            key: 1,\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\" 已完成 \")])),\n            _: 1 /* STABLE */,\n            __: [26]\n          }))])])])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 快捷操作卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"快捷操作\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_30, [_createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[5] || (_cache[5] = $event => _ctx.$router.push('/applications')),\n            style: {\n              \"margin-bottom\": \"8px\"\n            }\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"AppsOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[27] || (_cache[27] = _createTextVNode(\" 我的应用 \"))]),\n            _: 1 /* STABLE */,\n            __: [27]\n          }), _createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[6] || (_cache[6] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-bottom\": \"8px\"\n            }\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"LockClosedOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[28] || (_cache[28] = _createTextVNode(\" 安全中心 \"))]),\n            _: 1 /* STABLE */,\n            __: [28]\n          }), _createVNode($setup[\"NButton\"], {\n            block: \"\",\n            type: \"primary\",\n            ghost: \"\",\n            onClick: $setup.handleLogout\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"LogOutOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_cache[29] || (_cache[29] = _createTextVNode(\" 退出登录 \"))]),\n            _: 1 /* STABLE */,\n            __: [29]\n          })])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 授权服务卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"授权服务\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NList\"], {\n            \"show-divider\": false\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps, app => {\n              return _openBlock(), _createBlock($setup[\"NListItem\"], {\n                key: app.id,\n                class: \"service-item\"\n              }, {\n                default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"div\", _hoisted_32, _toDisplayString(app.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_33, _toDisplayString($setup.formatRelativeTime(app.lastUsed)), 1 /* TEXT */)])]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */);\n            }), 128 /* KEYED_FRAGMENT */)), !$setup.recentApps || $setup.recentApps.length === 0 ? (_openBlock(), _createBlock($setup[\"NEmpty\"], {\n              key: 0,\n              description: \"暂无授权服务\",\n              size: \"small\"\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "header", "_withCtx", "_hoisted_4", "_hoisted_5", "size", "src", "avatar", "getDefaultAvatar", "char<PERSON>t", "toUpperCase", "column", "label", "code", "id", "strong", "_hoisted_6", "_hoisted_7", "verificationStatus", "level1Completed", "level1Info", "_createBlock", "key", "trigger", "formatDateTime", "verifiedAt", "paymentMethod", "expiresAt", "_hoisted_8", "level2Completed", "level2Info", "realName", "icon", "createdAt", "_hoisted_9", "lastLoginAt", "_hoisted_10", "lastLoginIp", "_hoisted_11", "_hoisted_12", "email", "is_email_verified", "text", "phone", "_hoisted_13", "_hoisted_14", "maskPhone", "is_phone_verified", "_hoisted_15", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_16", "_createCommentVNode", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "formatRelativeTime", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "block", "handleLogout", "_Fragment", "_renderList", "recentApps", "app", "_hoisted_31", "_hoisted_32", "name", "_hoisted_33", "lastUsed", "length", "description"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <template #header>\n                <div class=\"card-header\">\n                  <h3>用户信息</h3>\n                  <div class=\"user-avatar\">\n                    <n-avatar\n                      :size=\"48\"\n                      :src=\"userStore.user?.avatar\"\n                      :fallback-src=\"getDefaultAvatar()\"\n                    >\n                      {{ userStore.user?.username?.charAt(0)?.toUpperCase() }}\n                    </n-avatar>\n                  </div>\n                </div>\n              </template>\n\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"用户ID\">\n                  <n-text code>{{ userStore.user?.id }}</n-text>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户名\">\n                  <n-text strong>{{ userStore.user?.username }}</n-text>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名认证状态\">\n                  <div class=\"verification-status\">\n                    <div class=\"status-item\">\n                      <span class=\"status-label\">一级认证：</span>\n                      <n-tag\n                        :bordered=\"false\"\n                        :type=\"verificationStatus.level1Completed ? 'success' : 'default'\"\n                        size=\"small\"\n                      >\n                        {{ verificationStatus.level1Completed ? '已完成' : '未完成' }}\n                      </n-tag>\n                      <n-tooltip v-if=\"verificationStatus.level1Completed && verificationStatus.level1Info\">\n                        <template #trigger>\n                          <n-icon size=\"14\" color=\"#18a058\" style=\"margin-left: 4px;\">\n                            <information-circle-outline />\n                          </n-icon>\n                        </template>\n                        <div>\n                          <p>认证时间: {{ formatDateTime(verificationStatus.level1Info.verifiedAt) }}</p>\n                          <p>支付方式: {{ verificationStatus.level1Info.paymentMethod === 'alipay' ? '支付宝' : '微信' }}</p>\n                          <p>过期时间: {{ formatDateTime(verificationStatus.level1Info.expiresAt) }}</p>\n                        </div>\n                      </n-tooltip>\n                    </div>\n                    <div class=\"status-item\">\n                      <span class=\"status-label\">二级认证：</span>\n                      <n-tag\n                        :bordered=\"false\"\n                        :type=\"verificationStatus.level2Completed ? 'success' : 'default'\"\n                        size=\"small\"\n                      >\n                        {{ verificationStatus.level2Completed ? '已完成' : '未完成' }}\n                      </n-tag>\n                      <n-tooltip v-if=\"verificationStatus.level2Completed && verificationStatus.level2Info\">\n                        <template #trigger>\n                          <n-icon size=\"14\" color=\"#18a058\" style=\"margin-left: 4px;\">\n                            <information-circle-outline />\n                          </n-icon>\n                        </template>\n                        <div>\n                          <p>真实姓名: {{ verificationStatus.level2Info.realName }}</p>\n                          <p>认证时间: {{ formatDateTime(verificationStatus.level2Info.verifiedAt) }}</p>\n                        </div>\n                      </n-tooltip>\n                    </div>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"账户状态\">\n                  <n-tag :bordered=\"false\" type=\"success\" size=\"small\">\n                    <template #icon>\n                      <n-icon><checkmark-circle-outline /></n-icon>\n                    </template>\n                    正常\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录\">\n                  <div class=\"login-info\">\n                    <div>{{ formatDateTime(userStore.user?.lastLoginAt) }}</div>\n                    <div class=\"login-ip\">IP: {{ userStore.user?.lastLoginIp || 'N/A' }}</div>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <div class=\"email-info\">\n                      <span>{{ userStore.user?.email }}</span>\n                      <n-tag\n                        v-if=\"userStore.user?.is_email_verified\"\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        已验证\n                      </n-tag>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"warning\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        未验证\n                      </n-tag>\n                    </div>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"绑定手机\" :span=\"2\" v-if=\"userStore.user?.phone\">\n                  <div class=\"phone-item\">\n                    <div class=\"phone-info\">\n                      <span>{{ maskPhone(userStore.user?.phone) }}</span>\n                      <n-tag\n                        v-if=\"userStore.user?.is_phone_verified\"\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        已验证\n                      </n-tag>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"warning\"\n                        size=\"tiny\"\n                        style=\"margin-left: 8px;\"\n                      >\n                        未验证\n                      </n-tag>\n                    </div>\n                    <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n                  </div>\n                </n-descriptions-item>\n              </n-descriptions>\n\n              <div class=\"action-buttons\">\n                <n-button type=\"primary\" ghost @click=\"$router.push('/security')\">\n                  <template #icon>\n                    <n-icon><lock-closed-outline /></n-icon>\n                  </template>\n                  安全设置\n                </n-button>\n                <n-button type=\"primary\" ghost @click=\"$router.push('/verification')\">\n                  <template #icon>\n                    <n-icon><shield-checkmark-outline /></n-icon>\n                  </template>\n                  实名认证\n                </n-button>\n                <n-button type=\"primary\" ghost @click=\"$router.push('/profile')\">\n                  <template #icon>\n                    <n-icon><person-outline /></n-icon>\n                  </template>\n                  个人资料\n                </n-button>\n              </div>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <!-- 认证状态卡片 -->\n              <n-card title=\"认证状态\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"verification-summary\">\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level1Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <wallet-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">一级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level1Completed ? '识脸支付认证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level1Completed && verificationStatus.level1Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level1Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level1Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n\n                  <div class=\"verification-item\">\n                    <div class=\"verification-icon\">\n                      <n-icon\n                        size=\"20\"\n                        :color=\"verificationStatus.level2Completed ? '#18a058' : '#d0d0d0'\"\n                      >\n                        <shield-checkmark-outline />\n                      </n-icon>\n                    </div>\n                    <div class=\"verification-content\">\n                      <div class=\"verification-title\">二级认证</div>\n                      <div class=\"verification-desc\">\n                        {{ verificationStatus.level2Completed ? '二要素验证' : '未完成' }}\n                      </div>\n                      <div v-if=\"verificationStatus.level2Completed && verificationStatus.level2Info\" class=\"verification-time\">\n                        {{ formatRelativeTime(verificationStatus.level2Info.verifiedAt) }}\n                      </div>\n                    </div>\n                    <div class=\"verification-action\">\n                      <n-button\n                        v-if=\"!verificationStatus.level2Completed\"\n                        text\n                        type=\"primary\"\n                        size=\"small\"\n                        @click=\"$router.push('/verification')\"\n                      >\n                        去认证\n                      </n-button>\n                      <n-tag\n                        v-else\n                        :bordered=\"false\"\n                        type=\"success\"\n                        size=\"small\"\n                      >\n                        已完成\n                      </n-tag>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 快捷操作卡片 -->\n              <n-card title=\"快捷操作\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"quick-actions\">\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/applications')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><apps-outline /></n-icon>\n                    </template>\n                    我的应用\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"$router.push('/security')\"\n                    style=\"margin-bottom: 8px;\"\n                  >\n                    <template #icon>\n                      <n-icon><lock-closed-outline /></n-icon>\n                    </template>\n                    安全中心\n                  </n-button>\n                  <n-button\n                    block\n                    type=\"primary\"\n                    ghost\n                    @click=\"handleLogout\"\n                  >\n                    <template #icon>\n                      <n-icon><log-out-outline /></n-icon>\n                    </template>\n                    退出登录\n                  </n-button>\n                </div>\n              </n-card>\n\n              <!-- 授权服务卡片 -->\n              <n-card title=\"授权服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <n-list :show-divider=\"false\">\n                  <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                    <div class=\"service-info\">\n                      <div class=\"service-name\">{{ app.name }}</div>\n                      <div class=\"service-time\">{{ formatRelativeTime(app.lastUsed) }}</div>\n                    </div>\n                  </n-list-item>\n                  <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无授权服务\" size=\"small\" />\n                </n-list>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  NAvatar,\n  NText,\n  NTooltip,\n  useMessage,\n  useDialog\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  InformationCircleOutline,\n  CheckmarkCircleOutline,\n  LockClosedOutline,\n  ShieldCheckmarkOutline,\n  PersonOutline,\n  WalletOutline,\n  AppsOutline,\n  LogOutOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\nconst dialog = useDialog();\n\nconst recentApps = ref([]);\nconst verificationStatus = ref({\n  level1Completed: false,\n  level2Completed: false,\n  level1Info: null,\n  level2Info: null\n});\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 格式化相对时间（如：3天前，2小时前）\nconst formatRelativeTime = (dateString) => {\n  if (!dateString) return 'N/A';\n\n  const date = new Date(dateString);\n  const now = new Date();\n  const diffMs = now - date;\n  const diffSec = Math.floor(diffMs / 1000);\n  const diffMin = Math.floor(diffSec / 60);\n  const diffHour = Math.floor(diffMin / 60);\n  const diffDay = Math.floor(diffHour / 24);\n  const diffMonth = Math.floor(diffDay / 30);\n  const diffYear = Math.floor(diffMonth / 12);\n\n  if (diffYear > 0) {\n    return `${diffYear}年前`;\n  } else if (diffMonth > 0) {\n    return `${diffMonth}个月前`;\n  } else if (diffDay > 0) {\n    return `${diffDay}天前`;\n  } else if (diffHour > 0) {\n    return `${diffHour}小时前`;\n  } else if (diffMin > 0) {\n    return `${diffMin}分钟前`;\n  } else {\n    return '刚刚';\n  }\n};\n\n// 获取默认头像\nconst getDefaultAvatar = () => {\n  return 'https://07akioni.oss-cn-beijing.aliyuncs.com/07akioni.jpeg';\n};\n\n// 手机号码脱敏\nconst maskPhone = (phone) => {\n  if (!phone) return '';\n  return phone.replace(/(\\d{3})\\d{4}(\\d{4})/, '$1****$2');\n};\n\n// 处理退出登录\nconst handleLogout = () => {\n  dialog.warning({\n    title: '确认退出',\n    content: '您确定要退出登录吗？',\n    positiveText: '确认',\n    negativeText: '取消',\n    onPositiveClick: async () => {\n      try {\n        const apiClient = getApiClient();\n        await apiClient.post('/auth/logout');\n        userStore.logout();\n        message.success('已成功退出登录');\n        window.location.href = '/login';\n      } catch (error) {\n        console.error('退出登录失败:', error);\n        message.error('退出登录失败: ' + (error.response?.data?.message || error.message));\n      }\n    }\n  });\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n\n    // 先只获取仪表盘数据\n    const dashboardResponse = await apiClient.get('/dashboard');\n\n    // 处理仪表盘数据\n    if (dashboardResponse.data && dashboardResponse.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (dashboardResponse.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...dashboardResponse.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: dashboardResponse.data.user.registrationTime?.formatted || userStore.user?.createdAt,\n          lastLoginAt: dashboardResponse.data.user.lastLoginTime?.formatted || userStore.user?.lastLoginAt,\n          lastLoginIp: dashboardResponse.data.user.lastLoginIp || userStore.user?.lastLoginIp\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = dashboardResponse.data.recentApps || [];\n\n      // 如果后端返回了认证状态，使用它\n      if (dashboardResponse.data.verificationStatus) {\n        verificationStatus.value = dashboardResponse.data.verificationStatus;\n      }\n    }\n\n    console.log('仪表盘数据加载成功:', dashboardResponse.data);\n\n    // 尝试获取认证状态（如果失败不影响主要功能）\n    try {\n      const verificationResponse = await apiClient.get('/users/verification-status');\n      if (verificationResponse.data && verificationResponse.data.success) {\n        verificationStatus.value = {\n          level1Completed: verificationResponse.data.level1Completed || false,\n          level2Completed: verificationResponse.data.level2Completed || false,\n          level1Info: verificationResponse.data.level1Info || null,\n          level2Info: verificationResponse.data.level2Info || null\n        };\n      }\n    } catch (verificationError) {\n      console.warn('获取认证状态失败:', verificationError);\n      // 不显示错误，使用默认值\n    }\n\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 600;\n}\n\n.user-avatar {\n  flex-shrink: 0;\n}\n\n.verification-status {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.status-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.status-label {\n  font-weight: 500;\n  min-width: 70px;\n}\n\n.login-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.login-ip {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n.email-item,\n.phone-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.email-info,\n.phone-info {\n  display: flex;\n  align-items: center;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 12px;\n  margin-top: 16px;\n  flex-wrap: wrap;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.verification-summary {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.verification-item {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  padding: 12px;\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.verification-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.verification-icon {\n  flex-shrink: 0;\n}\n\n.verification-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.verification-title {\n  font-weight: 600;\n  font-size: 14px;\n  margin-bottom: 2px;\n}\n\n.verification-desc {\n  font-size: 12px;\n  color: var(--n-text-color-2);\n  margin-bottom: 2px;\n}\n\n.verification-time {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n}\n\n.verification-action {\n  flex-shrink: 0;\n}\n\n.quick-actions {\n  display: flex;\n  flex-direction: column;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n.service-info {\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n  width: 100%;\n}\n\n.service-name {\n  font-weight: 500;\n  font-size: 14px;\n}\n\n.service-time {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAYZA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAa;;EAuBnBA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAa;;EAsBnBA,KAAK,EAAC;AAAa;;EAmCrBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAU;;EAIlBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EAyBpBA,KAAK,EAAC;AAAY;;EAChBA,KAAK,EAAC;AAAY;;EA0BxBA,KAAK,EAAC;AAAgB;;EAwBxBA,KAAK,EAAC;AAAY;;EAGdA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAmB;;;EAGkDA,KAAK,EAAC;;;EAInFA,KAAK,EAAC;AAAqB;;EAqB7BA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAmB;;;EAGkDA,KAAK,EAAC;;;EAInFA,KAAK,EAAC;AAAqB;;EAyB/BA,KAAK,EAAC;AAAe;;EA2CjBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAc;;uBAxT7CC,mBAAA,CAmUM,OAnUNC,UAmUM,GAlUJC,YAAA,CAiUSC,MAAA;IAjUAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA+TM,CA/TNC,mBAAA,CA+TM,OA/TNC,UA+TM,GA9TJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEhB,KAAK,EAAC;;wBAAa,MAIpEiB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAqTSC,MAAA;MArTD,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAsKO,CAtKPhB,YAAA,CAsKOC,MAAA;QAtKAgB,IAAI,EAAE;MAAC;0BACZ,MAoKS,CApKTjB,YAAA,CAoKSC,MAAA;UApKAY,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;UAC1EC,MAAM,EAAAC,QAAA,CACf,MAWM,CAXNjB,mBAAA,CAWM,OAXNkB,UAWM,G,0BAVJlB,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAQM,OARNmB,UAQM,GAPJvB,YAAA,CAMWC,MAAA;YALRuB,IAAI,EAAE,EAAE;YACRC,GAAG,EAAExB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,MAAM;YAC3B,cAAY,EAAEzB,MAAA,CAAA0B,gBAAgB;;8BAE/B,MAAwD,C,kCAArD1B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,EAAEkB,MAAM,KAAKC,WAAW,mB;;;4BAM3D,MA8HiB,CA9HjB7B,YAAA,CA8HiBC,MAAA;YA7Hf,iBAAe,EAAC,KAAK;YACpB6B,MAAM,EAAE;;8BAET,MAEsB,CAFtB9B,YAAA,CAEsBC,MAAA;cAFD8B,KAAK,EAAC;YAAM;gCAC/B,MAA8C,CAA9C/B,YAAA,CAA8CC,MAAA;gBAAtC+B,IAAI,EAAJ;cAAI;kCAAC,MAAwB,C,kCAArB/B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEwB,EAAE,iB;;;;gBAEpCjC,YAAA,CAEsBC,MAAA;cAFD8B,KAAK,EAAC;YAAK;gCAC9B,MAAsD,CAAtD/B,YAAA,CAAsDC,MAAA;gBAA9CiC,MAAM,EAAN;cAAM;kCAAC,MAA8B,C,kCAA3BjC,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,iB;;;;gBAE5CV,YAAA,CA8CsBC,MAAA;cA9CD8B,KAAK,EAAC;YAAQ;gCACjC,MA4CM,CA5CN3B,mBAAA,CA4CM,OA5CN+B,UA4CM,GA3CJ/B,mBAAA,CAqBM,OArBNgC,UAqBM,G,0BApBJhC,mBAAA,CAAuC;gBAAjCP,KAAK,EAAC;cAAc,GAAC,OAAK,qBAChCG,YAAA,CAMQC,MAAA;gBALLY,QAAQ,EAAE,KAAK;gBACfD,IAAI,EAAEX,MAAA,CAAAoC,kBAAkB,CAACC,eAAe;gBACzCd,IAAI,EAAC;;kCAEL,MAAwD,C,kCAArDvB,MAAA,CAAAoC,kBAAkB,CAACC,eAAe,iC;;2CAEtBrC,MAAA,CAAAoC,kBAAkB,CAACC,eAAe,IAAIrC,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,I,cAApFC,YAAA,CAWYvC,MAAA;gBAAAwC,GAAA;cAAA;gBAVCC,OAAO,EAAArB,QAAA,CAChB,MAES,CAFTrB,YAAA,CAESC,MAAA;kBAFDuB,IAAI,EAAC,IAAI;kBAACN,KAAK,EAAC,SAAS;kBAACF,KAAyB,EAAzB;oBAAA;kBAAA;;oCAChC,MAA8B,CAA9BhB,YAAA,CAA8BC,MAAA,8B;;;kCAGlC,MAIM,CAJNG,mBAAA,CAIM,cAHJA,mBAAA,CAA2E,WAAxE,QAAM,GAAAG,gBAAA,CAAGN,MAAA,CAAA0C,cAAc,CAAC1C,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,CAACK,UAAU,mBACnExC,mBAAA,CAA0F,WAAvF,QAAM,GAAAG,gBAAA,CAAGN,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,CAACM,aAAa,8CACvDzC,mBAAA,CAA0E,WAAvE,QAAM,GAAAG,gBAAA,CAAGN,MAAA,CAAA0C,cAAc,CAAC1C,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,CAACO,SAAS,kB;;yDAIxE1C,mBAAA,CAoBM,OApBN2C,UAoBM,G,4BAnBJ3C,mBAAA,CAAuC;gBAAjCP,KAAK,EAAC;cAAc,GAAC,OAAK,qBAChCG,YAAA,CAMQC,MAAA;gBALLY,QAAQ,EAAE,KAAK;gBACfD,IAAI,EAAEX,MAAA,CAAAoC,kBAAkB,CAACW,eAAe;gBACzCxB,IAAI,EAAC;;kCAEL,MAAwD,C,kCAArDvB,MAAA,CAAAoC,kBAAkB,CAACW,eAAe,iC;;2CAEtB/C,MAAA,CAAAoC,kBAAkB,CAACW,eAAe,IAAI/C,MAAA,CAAAoC,kBAAkB,CAACY,UAAU,I,cAApFT,YAAA,CAUYvC,MAAA;gBAAAwC,GAAA;cAAA;gBATCC,OAAO,EAAArB,QAAA,CAChB,MAES,CAFTrB,YAAA,CAESC,MAAA;kBAFDuB,IAAI,EAAC,IAAI;kBAACN,KAAK,EAAC,SAAS;kBAACF,KAAyB,EAAzB;oBAAA;kBAAA;;oCAChC,MAA8B,CAA9BhB,YAAA,CAA8BC,MAAA,8B;;;kCAGlC,MAGM,CAHNG,mBAAA,CAGM,cAFJA,mBAAA,CAAyD,WAAtD,QAAM,GAAAG,gBAAA,CAAGN,MAAA,CAAAoC,kBAAkB,CAACY,UAAU,CAACC,QAAQ,kBAClD9C,mBAAA,CAA2E,WAAxE,QAAM,GAAAG,gBAAA,CAAGN,MAAA,CAAA0C,cAAc,CAAC1C,MAAA,CAAAoC,kBAAkB,CAACY,UAAU,CAACL,UAAU,kB;;;;gBAM7E5C,YAAA,CAOsBC,MAAA;cAPD8B,KAAK,EAAC;YAAM;gCAC/B,MAKQ,CALR/B,YAAA,CAKQC,MAAA;gBALAY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;gBAChC2B,IAAI,EAAA9B,QAAA,CACb,MAA6C,CAA7CrB,YAAA,CAA6CC,MAAA;oCAArC,MAA4B,CAA5BD,YAAA,CAA4BC,MAAA,4B;;;kCAC3B,MAEb,C,6CAFa,MAEb,G;;;;;gBAEFD,YAAA,CAEsBC,MAAA;cAFD8B,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5C9B,MAAA,CAAA0C,cAAc,CAAC1C,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAE2C,SAAS,kB;;gBAE7CpD,YAAA,CAKsBC,MAAA;cALD8B,KAAK,EAAC;YAAM;gCAC/B,MAGM,CAHN3B,mBAAA,CAGM,OAHNiD,UAGM,GAFJjD,mBAAA,CAA4D,aAAAG,gBAAA,CAApDN,MAAA,CAAA0C,cAAc,CAAC1C,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAE6C,WAAW,mBAClDlD,mBAAA,CAA0E,OAA1EmD,WAA0E,EAApD,MAAI,GAAAhD,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAE+C,WAAW,0B;;gBAG5DxD,YAAA,CAyBsBC,MAAA;cAzBD8B,KAAK,EAAC,MAAM;cAAEd,IAAI,EAAE;;gCACvC,MAuBM,CAvBNb,mBAAA,CAuBM,OAvBNqD,WAuBM,GAtBJrD,mBAAA,CAoBM,OApBNsD,WAoBM,GAnBJtD,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkD,KAAK,kBAEtB1D,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmD,iBAAiB,I,cADzCpB,YAAA,CAQQvC,MAAA;;gBANLY,QAAQ,EAAE,KAAK;gBAChBD,IAAI,EAAC,SAAS;gBACdY,IAAI,EAAC,MAAM;gBACXR,KAAyB,EAAzB;kBAAA;gBAAA;;kCACD,MAEDF,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;mCACA0B,YAAA,CAQQvC,MAAA;;gBANLY,QAAQ,EAAE,KAAK;gBAChBD,IAAI,EAAC,SAAS;gBACdY,IAAI,EAAC,MAAM;gBACXR,KAAyB,EAAzB;kBAAA;gBAAA;;kCACD,MAEDF,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;qBAEFd,YAAA,CAAwDC,MAAA;gBAA9C4D,IAAI,EAAJ,EAAI;gBAACjD,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;gBAGCb,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEqD,KAAK,I,cAAvEtB,YAAA,CAyBsBvC,MAAA;;cAzBD8B,KAAK,EAAC,MAAM;cAAEd,IAAI,EAAE;;gCACvC,MAuBM,CAvBNb,mBAAA,CAuBM,OAvBN2D,WAuBM,GAtBJ3D,mBAAA,CAoBM,OApBN4D,WAoBM,GAnBJ5D,mBAAA,CAAmD,cAAAG,gBAAA,CAA1CN,MAAA,CAAAgE,SAAS,CAAChE,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEqD,KAAK,mBAEhC7D,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEyD,iBAAiB,I,cADzC1B,YAAA,CAQQvC,MAAA;;gBANLY,QAAQ,EAAE,KAAK;gBAChBD,IAAI,EAAC,SAAS;gBACdY,IAAI,EAAC,MAAM;gBACXR,KAAyB,EAAzB;kBAAA;gBAAA;;kCACD,MAEDF,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;mCACA0B,YAAA,CAQQvC,MAAA;;gBANLY,QAAQ,EAAE,KAAK;gBAChBD,IAAI,EAAC,SAAS;gBACdY,IAAI,EAAC,MAAM;gBACXR,KAAyB,EAAzB;kBAAA;gBAAA;;kCACD,MAEDF,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;qBAEFd,YAAA,CAAwDC,MAAA;gBAA9C4D,IAAI,EAAJ,EAAI;gBAACjD,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;cAKnDV,mBAAA,CAmBM,OAnBN+D,WAmBM,GAlBJnE,YAAA,CAKWC,MAAA;YALDW,IAAI,EAAC,SAAS;YAACwD,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;YACtCtB,IAAI,EAAA9B,QAAA,CACb,MAAwC,CAAxCrB,YAAA,CAAwCC,MAAA;gCAAhC,MAAuB,CAAvBD,YAAA,CAAuBC,MAAA,uB;;;8BACtB,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAKWC,MAAA;YALDW,IAAI,EAAC,SAAS;YAACwD,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;YACtCtB,IAAI,EAAA9B,QAAA,CACb,MAA6C,CAA7CrB,YAAA,CAA6CC,MAAA;gCAArC,MAA4B,CAA5BD,YAAA,CAA4BC,MAAA,4B;;;8BAC3B,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAKWC,MAAA;YALDW,IAAI,EAAC,SAAS;YAACwD,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;YACtCtB,IAAI,EAAA9B,QAAA,CACb,MAAmC,CAAnCrB,YAAA,CAAmCC,MAAA;gCAA3B,MAAkB,CAAlBD,YAAA,CAAkBC,MAAA,mB;;;8BACjB,MAEb,C,6CAFa,QAEb,G;;;;;;;UAKND,YAAA,CA2IOC,MAAA;QA3IAgB,IAAI,EAAE;MAAC;0BACZ,MAyIM,CAzINb,mBAAA,CAyIM,OAzINsE,WAyIM,GAxIJC,mBAAA,YAAe,EACf3E,YAAA,CAgFSC,MAAA;UAhFDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MA8EM,CA9ENf,mBAAA,CA8EM,OA9ENwE,WA8EM,GA7EJxE,mBAAA,CAqCM,OArCNyE,WAqCM,GApCJzE,mBAAA,CAOM,OAPN0E,WAOM,GANJ9E,YAAA,CAKSC,MAAA;YAJPuB,IAAI,EAAC,IAAI;YACRN,KAAK,EAAEjB,MAAA,CAAAoC,kBAAkB,CAACC,eAAe;;8BAE1C,MAAkB,CAAlBtC,YAAA,CAAkBC,MAAA,mB;;0CAGtBG,mBAAA,CAQM,OARN2E,WAQM,G,4BAPJ3E,mBAAA,CAA0C;YAArCP,KAAK,EAAC;UAAoB,GAAC,MAAI,qBACpCO,mBAAA,CAEM,OAFN4E,WAEM,EAAAzE,gBAAA,CADDN,MAAA,CAAAoC,kBAAkB,CAACC,eAAe,qCAE5BrC,MAAA,CAAAoC,kBAAkB,CAACC,eAAe,IAAIrC,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,I,cAA9EzC,mBAAA,CAEM,OAFNmF,WAEM,EAAA1E,gBAAA,CADDN,MAAA,CAAAiF,kBAAkB,CAACjF,MAAA,CAAAoC,kBAAkB,CAACE,UAAU,CAACK,UAAU,qB,qCAGlExC,mBAAA,CAkBM,OAlBN+E,WAkBM,G,CAhBKlF,MAAA,CAAAoC,kBAAkB,CAACC,eAAe,I,cAD3CE,YAAA,CAQWvC,MAAA;;YANT4D,IAAI,EAAJ,EAAI;YACJjD,IAAI,EAAC,SAAS;YACdY,IAAI,EAAC,OAAO;YACX6C,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;8BACrB,MAED3D,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;+BACA0B,YAAA,CAOQvC,MAAA;;YALLY,QAAQ,EAAE,KAAK;YAChBD,IAAI,EAAC,SAAS;YACdY,IAAI,EAAC;;8BACN,MAEDV,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;mBAIJV,mBAAA,CAqCM,OArCNgF,WAqCM,GApCJhF,mBAAA,CAOM,OAPNiF,WAOM,GANJrF,YAAA,CAKSC,MAAA;YAJPuB,IAAI,EAAC,IAAI;YACRN,KAAK,EAAEjB,MAAA,CAAAoC,kBAAkB,CAACW,eAAe;;8BAE1C,MAA4B,CAA5BhD,YAAA,CAA4BC,MAAA,4B;;0CAGhCG,mBAAA,CAQM,OARNkF,WAQM,G,4BAPJlF,mBAAA,CAA0C;YAArCP,KAAK,EAAC;UAAoB,GAAC,MAAI,qBACpCO,mBAAA,CAEM,OAFNmF,WAEM,EAAAhF,gBAAA,CADDN,MAAA,CAAAoC,kBAAkB,CAACW,eAAe,oCAE5B/C,MAAA,CAAAoC,kBAAkB,CAACW,eAAe,IAAI/C,MAAA,CAAAoC,kBAAkB,CAACY,UAAU,I,cAA9EnD,mBAAA,CAEM,OAFN0F,WAEM,EAAAjF,gBAAA,CADDN,MAAA,CAAAiF,kBAAkB,CAACjF,MAAA,CAAAoC,kBAAkB,CAACY,UAAU,CAACL,UAAU,qB,qCAGlExC,mBAAA,CAkBM,OAlBNqF,WAkBM,G,CAhBKxF,MAAA,CAAAoC,kBAAkB,CAACW,eAAe,I,cAD3CR,YAAA,CAQWvC,MAAA;;YANT4D,IAAI,EAAJ,EAAI;YACJjD,IAAI,EAAC,SAAS;YACdY,IAAI,EAAC,OAAO;YACX6C,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;8BACrB,MAED3D,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;+BACA0B,YAAA,CAOQvC,MAAA;;YALLY,QAAQ,EAAE,KAAK;YAChBD,IAAI,EAAC,SAAS;YACdY,IAAI,EAAC;;8BACN,MAEDV,MAAA,SAAAA,MAAA,Q,iBAFC,OAED,E;;;;;gDAMR6D,mBAAA,YAAe,EACf3E,YAAA,CAsCSC,MAAA;UAtCDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MAoCM,CApCNf,mBAAA,CAoCM,OApCNsF,WAoCM,GAnCJ1F,YAAA,CAWWC,MAAA;YAVT0F,KAAK,EAAL,EAAK;YACL/E,IAAI,EAAC,SAAS;YACdwD,KAAK,EAAL,EAAK;YACJC,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YACpBzD,KAA2B,EAA3B;cAAA;YAAA;;YAEWmC,IAAI,EAAA9B,QAAA,CACb,MAAiC,CAAjCrB,YAAA,CAAiCC,MAAA;gCAAzB,MAAgB,CAAhBD,YAAA,CAAgBC,MAAA,iB;;;8BACf,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAWWC,MAAA;YAVT0F,KAAK,EAAL,EAAK;YACL/E,IAAI,EAAC,SAAS;YACdwD,KAAK,EAAL,EAAK;YACJC,OAAK,EAAAvD,MAAA,QAAAA,MAAA,MAAAwD,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YACpBzD,KAA2B,EAA3B;cAAA;YAAA;;YAEWmC,IAAI,EAAA9B,QAAA,CACb,MAAwC,CAAxCrB,YAAA,CAAwCC,MAAA;gCAAhC,MAAuB,CAAvBD,YAAA,CAAuBC,MAAA,uB;;;8BACtB,MAEb,C,6CAFa,QAEb,G;;;cACAD,YAAA,CAUWC,MAAA;YATT0F,KAAK,EAAL,EAAK;YACL/E,IAAI,EAAC,SAAS;YACdwD,KAAK,EAAL,EAAK;YACJC,OAAK,EAAEpE,MAAA,CAAA2F;;YAEGzC,IAAI,EAAA9B,QAAA,CACb,MAAoC,CAApCrB,YAAA,CAAoCC,MAAA;gCAA5B,MAAmB,CAAnBD,YAAA,CAAmBC,MAAA,mB;;;8BAClB,MAEb,C,6CAFa,QAEb,G;;;;;gDAIJ0E,mBAAA,YAAe,EACf3E,YAAA,CAUSC,MAAA;UAVDU,KAAK,EAAC,MAAM;UAAEE,QAAQ,EAAE,KAAK;UAAEhB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAqB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7F,MAQS,CARTnB,YAAA,CAQSC,MAAA;YARA,cAAY,EAAE;UAAK;8BACb,MAAyB,E,kBAAtCH,mBAAA,CAKc+F,SAAA,QAAAC,WAAA,CALa7F,MAAA,CAAA8F,UAAU,EAAjBC,GAAG;mCAAvBxD,YAAA,CAKcvC,MAAA;gBAL0BwC,GAAG,EAAEuD,GAAG,CAAC/D,EAAE;gBAAEpC,KAAK,EAAC;;kCACzD,MAGM,CAHNO,mBAAA,CAGM,OAHN6F,WAGM,GAFJ7F,mBAAA,CAA8C,OAA9C8F,WAA8C,EAAA3F,gBAAA,CAAjByF,GAAG,CAACG,IAAI,kBACrC/F,mBAAA,CAAsE,OAAtEgG,WAAsE,EAAA7F,gBAAA,CAAzCN,MAAA,CAAAiF,kBAAkB,CAACc,GAAG,CAACK,QAAQ,kB;;;6CAGhDpG,MAAA,CAAA8F,UAAU,IAAI9F,MAAA,CAAA8F,UAAU,CAACO,MAAM,U,cAA/C9D,YAAA,CAA2FvC,MAAA;;cAApCsG,WAAW,EAAC,QAAQ;cAAC/E,IAAI,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}