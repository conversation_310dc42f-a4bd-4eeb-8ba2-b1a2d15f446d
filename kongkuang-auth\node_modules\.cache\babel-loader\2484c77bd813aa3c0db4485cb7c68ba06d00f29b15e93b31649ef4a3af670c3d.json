{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeStyle as _normalizeStyle } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"dashboard-layout\"\n};\nconst _hoisted_5 = {\n  class: \"left-section\"\n};\nconst _hoisted_6 = {\n  class: \"email-item\"\n};\nconst _hoisted_7 = {\n  class: \"right-section\"\n};\nconst _hoisted_8 = {\n  class: \"side-cards-horizontal\"\n};\nconst _hoisted_9 = {\n  class: \"services-content\"\n};\nconst _hoisted_10 = {\n  key: 0,\n  class: \"service-list\"\n};\nconst _hoisted_11 = {\n  class: \"service-name\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"no-services\"\n};\nconst _hoisted_13 = {\n  class: \"security-header\"\n};\nconst _hoisted_14 = {\n  class: \"security-index-display\"\n};\nconst _hoisted_15 = {\n  class: \"security-score-large\"\n};\nconst _hoisted_16 = {\n  class: \"score-label\"\n};\nconst _hoisted_17 = {\n  class: \"security-guide\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [3]\n    }), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NCard\"], {\n      bordered: false,\n      class: \"user-info-panel\",\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n        \"label-placement\": \"top\",\n        column: 2\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"ID\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"实名状态\"\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n            bordered: false,\n            type: $setup.userStore.user?.level2_verified ? 'success' : 'warning',\n            size: \"small\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"])]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"注册时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"最后登录时间\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.last_login || $setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"最后登录 IP\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"用户状态\"\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n            bordered: false,\n            type: \"success\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"正常\")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode($setup[\"NDescriptionsItem\"], {\n          label: \"绑定邮箱\",\n          span: 2\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n            text: \"\",\n            type: \"primary\",\n            size: \"small\"\n          }, {\n            default: _withCtx(() => _cache[5] || (_cache[5] = [_createTextVNode(\"换绑\")])),\n            _: 1 /* STABLE */,\n            __: [5]\n          })])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NButton\"], {\n        type: \"primary\",\n        ghost: \"\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n        style: {\n          \"margin-top\": \"16px\"\n        }\n      }, {\n        default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\" 更改密码 \")])),\n        _: 1 /* STABLE */,\n        __: [6]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 授权服务卡片 \"), _createVNode($setup[\"NCard\"], {\n      title: \"可使用空旷账户登录的服务\",\n      bordered: false,\n      class: \"compact-card services-card\",\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [$setup.recentApps && $setup.recentApps.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps.slice(0, 3), app => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: app.id,\n          class: \"service-item-compact\"\n        }, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n          class: \"service-dot\"\n        }, null, -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_11, _toDisplayString(app.name), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode($setup[\"NIcon\"], {\n        size: \"24\",\n        color: \"#d0d0d0\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"AppsOutline\"])]),\n        _: 1 /* STABLE */\n      }), _cache[8] || (_cache[8] = _createElementVNode(\"span\", null, \"暂无服务\", -1 /* CACHED */))]))])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 账户安全指标卡片 \"), _createVNode($setup[\"NCard\"], {\n      bordered: false,\n      class: \"compact-card security-card\",\n      \"theme-overrides\": {\n        color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n      }\n    }, {\n      header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"账户安全指数\", -1 /* CACHED */)), _createVNode($setup[\"NButton\"], {\n        text: \"\",\n        type: \"primary\",\n        size: \"small\",\n        onClick: _cache[1] || (_cache[1] = $event => _ctx.$router.push('/security')),\n        class: \"security-action-btn\"\n      }, {\n        icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n          default: _withCtx(() => [_createVNode($setup[\"AddOutline\"])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })])]),\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createCommentVNode(\" 安全指数展示 \"), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n        class: \"score-number\",\n        style: _normalizeStyle({\n          color: $setup.getSecurityColor($setup.securityScore)\n        })\n      }, _toDisplayString($setup.securityScore), 5 /* TEXT, STYLE */), _createElementVNode(\"div\", _hoisted_16, [_createVNode($setup[\"NTag\"], {\n        type: $setup.getSecurityLevelType($setup.securityScore),\n        bordered: false,\n        size: \"small\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getSecurityLevelText($setup.securityScore)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"type\"])])]), _createCommentVNode(\" 引导文字 \"), _createElementVNode(\"div\", _hoisted_17, [_cache[11] || (_cache[11] = _createElementVNode(\"span\", {\n        class: \"guide-text\"\n      }, \"前往安全中心完善账户安全设置\", -1 /* CACHED */)), _createVNode($setup[\"NButton\"], {\n        text: \"\",\n        type: \"primary\",\n        size: \"tiny\",\n        onClick: _cache[2] || (_cache[2] = $event => _ctx.$router.push('/security'))\n      }, {\n        default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\" 立即前往 → \")])),\n        _: 1 /* STABLE */,\n        __: [10]\n      })])])]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"theme-overrides\"])])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "_hoisted_4", "_hoisted_5", "color", "isDarkMode", "column", "label", "id", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "span", "_hoisted_6", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "style", "_hoisted_7", "_hoisted_8", "_createCommentVNode", "_hoisted_9", "recentApps", "length", "_hoisted_10", "_Fragment", "_renderList", "slice", "app", "key", "_hoisted_11", "name", "_hoisted_12", "header", "_withCtx", "_hoisted_13", "icon", "_hoisted_14", "_hoisted_15", "_normalizeStyle", "getSecurityColor", "securityScore", "_hoisted_16", "getSecurityLevelType", "getSecurityLevelText", "_hoisted_17"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <div class=\"dashboard-layout\">\n          <div class=\"left-section\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </div>\n\n          <div class=\"right-section\">\n            <div class=\"side-cards-horizontal\">\n              <!-- 授权服务卡片 -->\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" class=\"compact-card services-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"services-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item-compact\">\n                      <div class=\"service-dot\"></div>\n                      <span class=\"service-name\">{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-services\">\n                    <n-icon size=\"24\" color=\"#d0d0d0\">\n                      <apps-outline />\n                    </n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card :bordered=\"false\" class=\"compact-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <template #header>\n                  <div class=\"security-header\">\n                    <span>账户安全指数</span>\n                    <n-button\n                      text\n                      type=\"primary\"\n                      size=\"small\"\n                      @click=\"$router.push('/security')\"\n                      class=\"security-action-btn\"\n                    >\n                      <template #icon>\n                        <n-icon>\n                          <add-outline />\n                        </n-icon>\n                      </template>\n                    </n-button>\n                  </div>\n                </template>\n\n                <div class=\"security-index-display\">\n                  <!-- 安全指数展示 -->\n                  <div class=\"security-score-large\">\n                    <div class=\"score-number\" :style=\"{ color: getSecurityColor(securityScore) }\">\n                      {{ securityScore }}\n                    </div>\n                    <div class=\"score-label\">\n                      <n-tag\n                        :type=\"getSecurityLevelType(securityScore)\"\n                        :bordered=\"false\"\n                        size=\"small\"\n                      >\n                        {{ getSecurityLevelText(securityScore) }}\n                      </n-tag>\n                    </div>\n                  </div>\n\n                  <!-- 引导文字 -->\n                  <div class=\"security-guide\">\n                    <span class=\"guide-text\">前往安全中心完善账户安全设置</span>\n                    <n-button\n                      text\n                      type=\"primary\"\n                      size=\"tiny\"\n                      @click=\"$router.push('/security')\"\n                    >\n                      立即前往 →\n                    </n-button>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline,\n  AddOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n/* 新的布局样式 */\n.dashboard-layout {\n  display: grid;\n  grid-template-columns: 2fr 1fr; /* 左侧2份，右侧1份 */\n  gap: 16px;\n  flex: 1;\n  align-items: start; /* 从顶部对齐 */\n}\n\n.left-section {\n  /* 网格项会自动占据分配的空间 */\n}\n\n.right-section {\n  /* 网格项会自动占据分配的空间 */\n  display: flex;\n  flex-direction: column;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 垂直并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n  flex: 1;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  display: flex;\n  flex-direction: column;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩，适应水平布局 */\n}\n\n.services-content {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 8px 0;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 12px; /* 增加项目间距 */\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n  padding: 8px 12px; /* 增加内边距 */\n  background-color: var(--n-color-target);\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.service-item-compact:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.service-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n  color: var(--n-text-color-3);\n  font-size: 13px;\n  padding: 20px;\n}\n\n.more-services {\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 8px;\n  padding: 4px 8px;\n  background-color: var(--n-color-target);\n  border-radius: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  /* 简洁的安全指数显示 */\n}\n\n.security-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.security-action-btn {\n  opacity: 0.7;\n  transition: opacity 0.3s ease;\n}\n\n.security-action-btn:hover {\n  opacity: 1;\n}\n\n.security-index-display {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  flex: 1; /* 填充整个卡片高度 */\n  padding: 16px 0;\n  justify-content: center;\n}\n\n.security-score-large {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 12px;\n}\n\n.score-number {\n  font-size: 48px;\n  font-weight: 700;\n  line-height: 1;\n  transition: color 0.3s ease;\n}\n\n.score-label {\n  display: flex;\n  justify-content: center;\n}\n\n.security-guide {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n  text-align: center;\n}\n\n.guide-text {\n  font-size: 13px;\n  color: var(--n-text-color-2);\n  line-height: 1.4;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards {\n    gap: 12px;\n  }\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAQpBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAc;;EA+BZA,KAAK,EAAC;AAAY;;EAY1BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAuB;;EAGzBA,KAAK,EAAC;AAAkB;;;EACqBA,KAAK,EAAC;;;EAG5CA,KAAK,EAAC;AAAc;;;EAGlBA,KAAK,EAAC;;;EAYbA,KAAK,EAAC;AAAiB;;EAkBzBA,KAAK,EAAC;AAAwB;;EAE5BA,KAAK,EAAC;AAAsB;;EAI1BA,KAAK,EAAC;AAAa;;EAYrBA,KAAK,EAAC;AAAgB;;uBAlH3CC,mBAAA,CAoIM,OApINC,UAoIM,GAnIJC,YAAA,CAkISC,MAAA;IAlIAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MAgIM,CAhINC,mBAAA,CAgIM,OAhINC,UAgIM,GA/HJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEhB,KAAK,EAAC;;wBAAa,MAIpEiB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAV,mBAAA,CAsHM,OAtHNW,UAsHM,GArHJX,mBAAA,CAyCM,OAzCNY,UAyCM,GAxCJhB,YAAA,CAuCSC,MAAA;MAvCAY,QAAQ,EAAE,KAAK;MAAEhB,KAAK,EAAC,iBAAiB;MAAE,iBAAe;QAAAoB,KAAA,EAAWhB,MAAA,CAAAiB,UAAU;MAAA;;wBACrF,MAkCiB,CAlCjBlB,YAAA,CAkCiBC,MAAA;QAjCf,iBAAe,EAAC,KAAK;QACpBkB,MAAM,EAAE;;0BAET,MAEsB,CAFtBnB,YAAA,CAEsBC,MAAA;UAFDmB,KAAK,EAAC;QAAI;4BAC7B,MAAwB,C,kCAArBnB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEY,EAAE,iB;;YAEvBrB,YAAA,CAQsBC,MAAA;UARDmB,KAAK,EAAC;QAAM;4BAC/B,MAMQ,CANRpB,YAAA,CAMQC,MAAA;YALLY,QAAQ,EAAE,KAAK;YACfD,IAAI,EAAEX,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,eAAe;YACtCC,IAAI,EAAC;;8BAEL,MAAqD,C,kCAAlDtB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,eAAe,iC;;;;YAGtCtB,YAAA,CAEsBC,MAAA;UAFDmB,KAAK,EAAC;QAAM;4BAC/B,MAA+C,C,kCAA5CnB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEgB,SAAS,kB;;YAE7CzB,YAAA,CAEsBC,MAAA;UAFDmB,KAAK,EAAC;QAAQ;4BACjC,MAA+E,C,kCAA5EnB,MAAA,CAAAuB,cAAc,CAACvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,UAAU,IAAIzB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,WAAW,kB;;YAE7E3B,YAAA,CAEsBC,MAAA;UAFDmB,KAAK,EAAC;QAAS;4BAClC,MAAyC,C,kCAAtCnB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmB,WAAW,yB;;YAEhC5B,YAAA,CAEsBC,MAAA;UAFDmB,KAAK,EAAC;QAAM;4BAC9B,MAA+D,CAA/DpB,YAAA,CAA+DC,MAAA;YAAvDY,QAAQ,EAAE,KAAK;YAAED,IAAI,EAAC,SAAS;YAACW,IAAI,EAAC;;8BAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;YAEzDd,YAAA,CAKqBC,MAAA;UALAmB,KAAK,EAAC,MAAM;UAAES,IAAI,EAAE;;4BACxC,MAGE,CAHFzB,mBAAA,CAGE,OAHF0B,UAGE,GAFA1B,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEsB,KAAK,kBAC7B/B,YAAA,CAAwDC,MAAA;YAA9C+B,IAAI,EAAJ,EAAI;YAACpB,IAAI,EAAC,SAAS;YAACW,IAAI,EAAC;;8BAAQ,MAAET,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;UAInDd,YAAA,CAEUC,MAAA;QAFAW,IAAI,EAAC,SAAS;QAACqB,KAAK,EAAL,EAAK;QAAEC,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;QAAeC,KAAyB,EAAzB;UAAA;QAAA;;0BAA0B,MAE7FzB,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;8CAIJV,mBAAA,CAyEM,OAzENoC,UAyEM,GAxEJpC,mBAAA,CAuEM,OAvENqC,UAuEM,GAtEJC,mBAAA,YAAe,EACf1C,YAAA,CAeSC,MAAA;MAfDU,KAAK,EAAC,cAAc;MAAEE,QAAQ,EAAE,KAAK;MAAEhB,KAAK,EAAC,4BAA4B;MAAE,iBAAe;QAAAoB,KAAA,EAAWhB,MAAA,CAAAiB,UAAU;MAAA;;wBACrH,MAaM,CAbNd,mBAAA,CAaM,OAbNuC,UAaM,GAZO1C,MAAA,CAAA2C,UAAU,IAAI3C,MAAA,CAAA2C,UAAU,CAACC,MAAM,Q,cAA1C/C,mBAAA,CAKM,OALNgD,WAKM,I,kBAJJhD,mBAAA,CAGMiD,SAAA,QAAAC,WAAA,CAHa/C,MAAA,CAAA2C,UAAU,CAACK,KAAK,QAAvBC,GAAG;6BAAfpD,mBAAA,CAGM;UAHsCqD,GAAG,EAAED,GAAG,CAAC7B,EAAE;UAAExB,KAAK,EAAC;sCAC7DO,mBAAA,CAA+B;UAA1BP,KAAK,EAAC;QAAa,4BACxBO,mBAAA,CAAgD,QAAhDgD,WAAgD,EAAA7C,gBAAA,CAAlB2C,GAAG,CAACG,IAAI,iB;yDAG1CvD,mBAAA,CAKM,OALNwD,WAKM,GAJJtD,YAAA,CAESC,MAAA;QAFDsB,IAAI,EAAC,IAAI;QAACN,KAAK,EAAC;;0BACtB,MAAgB,CAAhBjB,YAAA,CAAgBC,MAAA,iB;;oCAElBG,mBAAA,CAAiB,cAAX,MAAI,oB;;4CAKhBsC,mBAAA,cAAiB,EACjB1C,YAAA,CAkDSC,MAAA;MAlDAY,QAAQ,EAAE,KAAK;MAAEhB,KAAK,EAAC,4BAA4B;MAAE,iBAAe;QAAAoB,KAAA,EAAWhB,MAAA,CAAAiB,UAAU;MAAA;;MACrFqC,MAAM,EAAAC,QAAA,CACf,MAeM,CAfNpD,mBAAA,CAeM,OAfNqD,WAeM,G,0BAdJrD,mBAAA,CAAmB,cAAb,QAAM,qBACZJ,YAAA,CAYWC,MAAA;QAXT+B,IAAI,EAAJ,EAAI;QACJpB,IAAI,EAAC,SAAS;QACdW,IAAI,EAAC,OAAO;QACXW,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;QACpBzC,KAAK,EAAC;;QAEK6D,IAAI,EAAAF,QAAA,CACb,MAES,CAFTxD,YAAA,CAESC,MAAA;4BADP,MAAe,CAAfD,YAAA,CAAeC,MAAA,gB;;;;;wBAOzB,MA6BM,CA7BNG,mBAAA,CA6BM,OA7BNuD,WA6BM,GA5BJjB,mBAAA,YAAe,EACftC,mBAAA,CAaM,OAbNwD,WAaM,GAZJxD,mBAAA,CAEM;QAFDP,KAAK,EAAC,cAAc;QAAE0C,KAAK,EAAAsB,eAAA;UAAA5C,KAAA,EAAWhB,MAAA,CAAA6D,gBAAgB,CAAC7D,MAAA,CAAA8D,aAAa;QAAA;0BACpE9D,MAAA,CAAA8D,aAAa,yBAElB3D,mBAAA,CAQM,OARN4D,WAQM,GAPJhE,YAAA,CAMQC,MAAA;QALLW,IAAI,EAAEX,MAAA,CAAAgE,oBAAoB,CAAChE,MAAA,CAAA8D,aAAa;QACxClD,QAAQ,EAAE,KAAK;QAChBU,IAAI,EAAC;;0BAEL,MAAyC,C,kCAAtCtB,MAAA,CAAAiE,oBAAoB,CAACjE,MAAA,CAAA8D,aAAa,kB;;uCAK3CrB,mBAAA,UAAa,EACbtC,mBAAA,CAUM,OAVN+D,WAUM,G,4BATJ/D,mBAAA,CAA8C;QAAxCP,KAAK,EAAC;MAAY,GAAC,gBAAc,qBACvCG,YAAA,CAOWC,MAAA;QANT+B,IAAI,EAAJ,EAAI;QACJpB,IAAI,EAAC,SAAS;QACdW,IAAI,EAAC,MAAM;QACVW,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;;0BACrB,MAEDxB,MAAA,SAAAA,MAAA,Q,iBAFC,UAED,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}