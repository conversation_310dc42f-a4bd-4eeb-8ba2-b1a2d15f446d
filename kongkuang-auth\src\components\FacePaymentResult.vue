<template>
  <div class="face-payment-result">
    <n-modal 
      v-model:show="visible" 
      preset="card" 
      :title="resultTitle"
      style="width: 500px; max-width: 90vw;"
      :closable="false"
      :mask-closable="false"
    >
      <div class="result-content">
        <!-- 成功状态 -->
        <div v-if="status === 'success'" class="result-success">
          <div class="result-icon">
            <n-icon size="64" color="#18a058">
              <checkmark-circle-outline />
            </n-icon>
          </div>
          <h3>识脸支付成功</h3>
          <p class="result-message">恭喜！您已成功完成一级认证</p>
          
          <div class="payment-info">
            <div class="info-item">
              <span class="label">支付方式：</span>
              <span class="value">{{ paymentMethod === 'alipay' ? '支付宝识脸支付' : '微信识脸支付' }}</span>
            </div>
            <div class="info-item">
              <span class="label">支付金额：</span>
              <span class="value">¥{{ amount }}</span>
            </div>
            <div class="info-item">
              <span class="label">认证时间：</span>
              <span class="value">{{ formatTime(verifiedAt) }}</span>
            </div>
          </div>
          
          <div class="identity-info">
            <n-alert title="获取的实名信息" type="success" style="margin: 16px 0;">
              <div class="identity-details">
                <div class="info-item">
                  <span class="label">真实姓名：</span>
                  <span class="value">{{ maskedName }}</span>
                </div>
                <div class="info-item">
                  <span class="label">身份证号：</span>
                  <span class="value">{{ maskedIdNumber }}</span>
                </div>
              </div>
            </n-alert>
          </div>
          
          <div class="notice">
            <n-alert title="重要提示" type="warning" style="margin-top: 16px;">
              <p>• 一级认证信息将在30分钟后自动清除</p>
              <p>• 如需永久保存，请完成二级认证</p>
              <p>• 认证信息仅用于身份验证，严格保护隐私</p>
            </n-alert>
          </div>
        </div>
        
        <!-- 失败状态 -->
        <div v-else-if="status === 'failed'" class="result-failed">
          <div class="result-icon">
            <n-icon size="64" color="#d03050">
              <close-circle-outline />
            </n-icon>
          </div>
          <h3>识脸支付失败</h3>
          <p class="result-message">{{ errorMessage || '支付过程中出现问题，请重试' }}</p>
          
          <div class="failure-reasons">
            <n-alert title="可能的原因" type="error" style="margin: 16px 0;">
              <ul>
                <li>人脸识别验证失败</li>
                <li>支付账户余额不足</li>
                <li>网络连接异常</li>
                <li>支付平台服务异常</li>
              </ul>
            </n-alert>
          </div>
        </div>
        
        <!-- 处理中状态 -->
        <div v-else-if="status === 'processing'" class="result-processing">
          <div class="result-icon">
            <n-spin size="large" />
          </div>
          <h3>正在处理</h3>
          <p class="result-message">正在验证支付结果，请稍候...</p>
          
          <div class="processing-steps">
            <div class="step" :class="{ active: currentStep >= 1 }">
              <n-icon size="16"><scan-outline /></n-icon>
              <span>人脸识别验证</span>
            </div>
            <div class="step" :class="{ active: currentStep >= 2 }">
              <n-icon size="16"><wallet-outline /></n-icon>
              <span>处理支付</span>
            </div>
            <div class="step" :class="{ active: currentStep >= 3 }">
              <n-icon size="16"><person-outline /></n-icon>
              <span>获取实名信息</span>
            </div>
            <div class="step" :class="{ active: currentStep >= 4 }">
              <n-icon size="16"><checkmark-circle-outline /></n-icon>
              <span>完成认证</span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="modal-footer">
          <n-button 
            v-if="status === 'success'" 
            type="primary" 
            @click="handleConfirm"
          >
            确定
          </n-button>
          <n-button 
            v-else-if="status === 'failed'" 
            @click="handleRetry"
          >
            重试
          </n-button>
          <n-button 
            v-if="status === 'failed'" 
            type="primary" 
            @click="handleConfirm"
          >
            关闭
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import {
  NModal,
  NIcon,
  NSpin,
  NButton,
  NAlert
} from 'naive-ui';
import {
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ScanOutline,
  WalletOutline,
  PersonOutline
} from '@vicons/ionicons5';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    default: 'processing', // processing, success, failed
    validator: (value) => ['processing', 'success', 'failed'].includes(value)
  },
  paymentMethod: {
    type: String,
    default: 'alipay' // alipay, wechat
  },
  amount: {
    type: [String, Number],
    default: '1.2'
  },
  realName: {
    type: String,
    default: ''
  },
  idNumber: {
    type: String,
    default: ''
  },
  verifiedAt: {
    type: [String, Date],
    default: null
  },
  errorMessage: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:show', 'confirm', 'retry']);

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const currentStep = ref(1);

const resultTitle = computed(() => {
  switch (props.status) {
    case 'success':
      return '一级认证成功';
    case 'failed':
      return '认证失败';
    case 'processing':
    default:
      return '正在认证';
  }
});

const maskedName = computed(() => {
  if (!props.realName) return '';
  if (props.realName.length <= 2) return props.realName;
  return props.realName.charAt(0) + '***' + props.realName.charAt(props.realName.length - 1);
});

const maskedIdNumber = computed(() => {
  if (!props.idNumber) return '';
  if (props.idNumber.length < 10) return props.idNumber;
  return props.idNumber.substring(0, 6) + '****' + props.idNumber.substring(props.idNumber.length - 4);
});

const formatTime = (time) => {
  if (!time) return '';
  const date = new Date(time);
  return date.toLocaleString('zh-CN');
};

const handleConfirm = () => {
  emit('confirm');
  visible.value = false;
};

const handleRetry = () => {
  emit('retry');
  visible.value = false;
};

// 模拟处理步骤
watch(() => props.status, (newStatus) => {
  if (newStatus === 'processing') {
    currentStep.value = 1;
    const stepInterval = setInterval(() => {
      if (currentStep.value < 4 && props.status === 'processing') {
        currentStep.value++;
      } else {
        clearInterval(stepInterval);
      }
    }, 1000);
  }
});

onMounted(() => {
  if (props.status === 'processing') {
    currentStep.value = 1;
  }
});
</script>

<style scoped>
.face-payment-result {
  /* 组件容器样式 */
}

.result-content {
  text-align: center;
  padding: 16px 0;
}

.result-icon {
  margin-bottom: 16px;
}

.result-content h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
}

.result-message {
  margin: 0 0 24px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.payment-info,
.identity-details {
  text-align: left;
  margin: 16px 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--n-border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-item .label {
  font-weight: 500;
  color: var(--n-text-color-2);
}

.info-item .value {
  font-weight: 600;
  color: var(--n-text-color-1);
}

.processing-steps {
  display: flex;
  justify-content: space-between;
  margin: 24px 0;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--n-text-color-3);
  transition: all 0.3s ease;
}

.step.active {
  color: var(--n-primary-color);
}

.step span {
  text-align: center;
  max-width: 60px;
  line-height: 1.2;
}

.failure-reasons ul {
  margin: 8px 0;
  padding-left: 20px;
}

.failure-reasons li {
  margin: 4px 0;
  color: var(--n-text-color-2);
}

.notice p {
  margin: 4px 0;
  font-size: 13px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

@media (max-width: 768px) {
  .processing-steps {
    flex-direction: column;
    gap: 16px;
  }
  
  .step {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
  }
  
  .step span {
    max-width: none;
  }
}
</style>
