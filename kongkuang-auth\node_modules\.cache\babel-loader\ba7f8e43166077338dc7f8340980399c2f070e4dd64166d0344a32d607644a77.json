{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { NCard, NButton, NAlert, NIcon, NAvatar, NModal, NTag, NSpin, NForm, NFormItem, NInput, useMessage } from 'naive-ui';\nimport { ShieldOutline, ShieldCheckmarkOutline, Checkmark, LogoAlipay, LogoWechat, CardOutline, WalletOutline, CheckmarkCircleOutline, CloseCircleOutline, ArrowForward, Person, PersonOutline, ScanOutline } from '@vicons/ionicons5';\nimport { useUserStore } from '../stores/user';\nimport FacePaymentResult from '../components/FacePaymentResult.vue';\nimport { getApiClient } from '../utils/api';\nexport default {\n  __name: 'Verification',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const router = useRouter();\n    const message = useMessage();\n    const userStore = useUserStore();\n    const apiClient = getApiClient();\n\n    // 认证状态\n    const level1Completed = ref(false);\n    const level2Completed = ref(false);\n    const level1Loading = ref(false);\n    const level2Loading = ref(false);\n\n    // 模态框状态\n    const showLevel1Modal = ref(false);\n    const showLevel2Modal = ref(false);\n\n    // 一级认证相关\n    const selectedPayment = ref('');\n\n    // 识脸支付结果相关\n    const showPaymentResult = ref(false);\n    const paymentStatus = ref('processing'); // processing, success, failed\n    const paymentResult = ref({\n      realName: '',\n      idNumber: '',\n      verifiedAt: null,\n      errorMessage: ''\n    });\n\n    // 二级认证相关\n    const level2FormRef = ref(null);\n    const level2Form = ref({\n      realName: '',\n      idNumber: ''\n    });\n    const level2VerificationResult = ref(null);\n\n    // 二级认证表单验证规则\n    const level2Rules = {\n      realName: [{\n        required: true,\n        message: '请输入真实姓名',\n        trigger: ['blur', 'input']\n      }, {\n        min: 2,\n        max: 20,\n        message: '姓名长度应在2-20个字符之间',\n        trigger: ['blur', 'input']\n      }],\n      idNumber: [{\n        required: true,\n        message: '请输入身份证号码',\n        trigger: ['blur', 'input']\n      }, {\n        validator(rule, value) {\n          const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\n          if (!reg.test(value)) {\n            return new Error('请输入正确的身份证号码');\n          }\n          return true;\n        },\n        trigger: ['blur', 'input']\n      }]\n    };\n\n    // 表单验证状态\n    const isLevel2FormValid = computed(() => {\n      return level2Form.value.realName.trim() && level2Form.value.idNumber.trim() && /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(level2Form.value.idNumber);\n    });\n\n    // 检查认证状态\n    const checkVerificationStatus = async () => {\n      try {\n        const response = await apiClient.get('/users/verification-status');\n        level1Completed.value = response.data.level1Completed || false;\n        level2Completed.value = response.data.level2Completed || false;\n      } catch (error) {\n        console.error('获取认证状态失败:', error);\n      }\n    };\n\n    // 开始一级认证\n    const startLevel1Verification = () => {\n      showLevel1Modal.value = true;\n      selectedPayment.value = 'alipay'; // 默认选择支付宝\n    };\n\n    // 处理一级认证支付\n    const proceedLevel1Payment = async () => {\n      if (!selectedPayment.value) {\n        message.warning('请选择支付方式');\n        return;\n      }\n      level1Loading.value = true;\n\n      // 关闭选择模态框，显示支付结果模态框\n      showLevel1Modal.value = false;\n      showPaymentResult.value = true;\n      paymentStatus.value = 'processing';\n\n      // 重置支付结果\n      paymentResult.value = {\n        realName: '',\n        idNumber: '',\n        verifiedAt: null,\n        errorMessage: ''\n      };\n      try {\n        // 调用支付API\n        const response = await apiClient.post('/verification/level1/payment', {\n          paymentMethod: selectedPayment.value,\n          amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5\n        });\n        if (response.data.success) {\n          // 模拟识脸支付过程\n          await simulateFacePayment(response.data.orderId);\n        } else {\n          throw new Error(response.data.message || '发起支付失败');\n        }\n      } catch (error) {\n        paymentStatus.value = 'failed';\n        paymentResult.value.errorMessage = error.response?.data?.message || error.message || '发起支付失败';\n      } finally {\n        level1Loading.value = false;\n      }\n    };\n\n    // 模拟识脸支付过程\n    const simulateFacePayment = async orderId => {\n      // 模拟识脸支付的各个步骤\n      const steps = [{\n        delay: 2000,\n        message: '正在进行人脸识别...'\n      }, {\n        delay: 3000,\n        message: '人脸识别成功，正在处理支付...'\n      }, {\n        delay: 2000,\n        message: '支付成功，正在获取实名信息...'\n      }, {\n        delay: 1500,\n        message: '认证完成'\n      }];\n      for (let i = 0; i < steps.length; i++) {\n        await new Promise(resolve => setTimeout(resolve, steps[i].delay));\n        if (i === steps.length - 1) {\n          // 最后一步，检查支付状态\n          await checkPaymentStatus(orderId);\n        }\n      }\n    };\n\n    // 检查支付状态\n    const checkPaymentStatus = async orderId => {\n      try {\n        const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);\n        if (response.data.status === 'completed') {\n          // 支付成功，显示成功结果\n          paymentStatus.value = 'success';\n          paymentResult.value = {\n            realName: response.data.data?.realName || '张三',\n            idNumber: response.data.data?.idNumber || '110101199001011234',\n            verifiedAt: response.data.data?.verifiedAt || new Date(),\n            errorMessage: ''\n          };\n          level1Completed.value = true;\n\n          // 刷新认证状态\n          await loadVerificationStatus();\n        } else if (response.data.status === 'failed') {\n          // 支付失败\n          paymentStatus.value = 'failed';\n          paymentResult.value.errorMessage = response.data.message || '支付失败，请重试';\n        } else {\n          // 支付处理中，继续等待\n          setTimeout(() => checkPaymentStatus(orderId), 2000);\n        }\n      } catch (error) {\n        console.error('检查支付状态失败:', error);\n        paymentStatus.value = 'failed';\n        paymentResult.value.errorMessage = '检查支付状态失败，请重试';\n      }\n    };\n\n    // 处理支付结果确认\n    const handlePaymentResultConfirm = () => {\n      showPaymentResult.value = false;\n      if (paymentStatus.value === 'success') {\n        message.success('一级认证完成！');\n      }\n    };\n\n    // 处理支付重试\n    const handlePaymentRetry = () => {\n      showPaymentResult.value = false;\n      showLevel1Modal.value = true;\n    };\n\n    // 开始二级认证\n    const startLevel2Verification = () => {\n      showLevel2Modal.value = true;\n      level2VerificationResult.value = null;\n      // 重置表单\n      level2Form.value = {\n        realName: '',\n        idNumber: ''\n      };\n    };\n\n    // 提交二级认证\n    const submitLevel2Verification = async () => {\n      try {\n        // 验证表单\n        await level2FormRef.value?.validate();\n        level2Loading.value = true;\n        level2VerificationResult.value = null;\n        const response = await apiClient.post('/verification/level2/verify', {\n          realName: level2Form.value.realName.trim(),\n          idNumber: level2Form.value.idNumber.trim()\n        });\n        if (response.data.success) {\n          level2VerificationResult.value = {\n            success: true,\n            message: response.data.message || '身份验证成功'\n          };\n          message.success('二级认证成功！');\n        } else {\n          level2VerificationResult.value = {\n            success: false,\n            message: response.data.message || '身份验证失败'\n          };\n        }\n      } catch (error) {\n        console.error('二级认证失败:', error);\n        level2VerificationResult.value = {\n          success: false,\n          message: error.response?.data?.message || '身份验证失败，请稍后重试'\n        };\n        message.error(level2VerificationResult.value.message);\n      } finally {\n        level2Loading.value = false;\n      }\n    };\n\n    // 完成二级认证\n    const completeLevel2Verification = () => {\n      level2Completed.value = true;\n      showLevel2Modal.value = false;\n      message.success('二级认证已完成！');\n    };\n\n    // 组件挂载时检查认证状态\n    onMounted(() => {\n      checkVerificationStatus();\n    });\n    const __returned__ = {\n      router,\n      message,\n      userStore,\n      apiClient,\n      level1Completed,\n      level2Completed,\n      level1Loading,\n      level2Loading,\n      showLevel1Modal,\n      showLevel2Modal,\n      selectedPayment,\n      showPaymentResult,\n      paymentStatus,\n      paymentResult,\n      level2FormRef,\n      level2Form,\n      level2VerificationResult,\n      level2Rules,\n      isLevel2FormValid,\n      checkVerificationStatus,\n      startLevel1Verification,\n      proceedLevel1Payment,\n      simulateFacePayment,\n      checkPaymentStatus,\n      handlePaymentResultConfirm,\n      handlePaymentRetry,\n      startLevel2Verification,\n      submitLevel2Verification,\n      completeLevel2Verification,\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NAvatar() {\n        return NAvatar;\n      },\n      get NModal() {\n        return NModal;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NSpin() {\n        return NSpin;\n      },\n      get NForm() {\n        return NForm;\n      },\n      get NFormItem() {\n        return NFormItem;\n      },\n      get NInput() {\n        return NInput;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get ShieldOutline() {\n        return ShieldOutline;\n      },\n      get ShieldCheckmarkOutline() {\n        return ShieldCheckmarkOutline;\n      },\n      get Checkmark() {\n        return Checkmark;\n      },\n      get LogoAlipay() {\n        return LogoAlipay;\n      },\n      get LogoWechat() {\n        return LogoWechat;\n      },\n      get CardOutline() {\n        return CardOutline;\n      },\n      get WalletOutline() {\n        return WalletOutline;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get ArrowForward() {\n        return ArrowForward;\n      },\n      get Person() {\n        return Person;\n      },\n      get PersonOutline() {\n        return PersonOutline;\n      },\n      get ScanOutline() {\n        return ScanOutline;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      FacePaymentResult,\n      get getApiClient() {\n        return getApiClient;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useRouter", "NCard", "NButton", "N<PERSON><PERSON><PERSON>", "NIcon", "NAvatar", "NModal", "NTag", "NSpin", "NForm", "NFormItem", "NInput", "useMessage", "ShieldOutline", "ShieldCheckmarkOutline", "Checkmark", "LogoAlipay", "LogoWechat", "CardOutline", "WalletOutline", "CheckmarkCircleOutline", "CloseCircleOutline", "ArrowForward", "Person", "PersonOutline", "ScanOutline", "useUserStore", "FacePaymentResult", "getApiClient", "router", "message", "userStore", "apiClient", "level1Completed", "level2Completed", "level1Loading", "level2Loading", "showLevel1Modal", "showLevel2Modal", "selectedPayment", "showPaymentResult", "paymentStatus", "paymentResult", "realName", "idNumber", "verifiedAt", "errorMessage", "level2FormRef", "level2Form", "level2VerificationResult", "level2Rules", "required", "trigger", "min", "max", "validator", "rule", "value", "reg", "test", "Error", "isLevel2FormValid", "trim", "checkVerificationStatus", "response", "get", "data", "error", "console", "startLevel1Verification", "proceedLevel1Payment", "warning", "post", "paymentMethod", "amount", "success", "simulateFacePayment", "orderId", "steps", "delay", "i", "length", "Promise", "resolve", "setTimeout", "checkPaymentStatus", "status", "Date", "loadVerificationStatus", "handlePaymentResultConfirm", "handlePaymentRetry", "startLevel2Verification", "submitLevel2Verification", "validate", "completeLevel2Verification"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Verification.vue"], "sourcesContent": ["<template>\r\n  <div class=\"verification-container\">\r\n    <h1 class=\"page-title\">实名认证</h1>\r\n\r\n    <!-- 认证状态概览 -->\r\n    <div class=\"verification-overview\">\r\n      <n-card class=\"level-card primary-card\" :class=\"{ 'completed': level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level2Completed ? '#18a058' : '#2080f0'\">\r\n            <shield-checkmark-outline v-if=\"level2Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>二级认证</h3>\r\n          <n-tag v-if=\"level2Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else type=\"info\" size=\"small\">免费认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过姓名和身份证号进行二要素验证，完全免费</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>二要素身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问所有功能</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>完全免费</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else disabled>已完成</n-button>\r\n        </div>\r\n      </n-card>\r\n\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level1Completed, 'disabled': !level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level1Completed ? '#18a058' : (level2Completed ? '#2080f0' : '#d0d0d0')\">\r\n            <shield-checkmark-outline v-if=\"level1Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>一级认证</h3>\r\n          <n-tag v-if=\"level1Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else-if=\"level2Completed\" type=\"warning\" size=\"small\">可进行</n-tag>\r\n          <n-tag v-else type=\"default\" size=\"small\">需完成二级认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过支付宝或微信识脸支付验证，获得更高信任度</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>识脸支付验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>实名信息获取</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>更高信任等级</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level1Completed && level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel1Verification\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else-if=\"level1Completed\" disabled>已完成</n-button>\r\n          <n-button v-else disabled>需完成二级认证</n-button>\r\n        </div>\r\n      </n-card>\r\n    </div>\r\n\r\n    <!-- 一级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel1Modal\" preset=\"card\" title=\"一级认证 - 识脸支付\" style=\"width: 700px; max-width: 90vw;\">\r\n      <div class=\"level1-content\">\r\n        <n-alert title=\"识脸支付认证说明\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          通过支付宝或微信识脸支付进行实名认证，支付成功后自动获取您的真实身份信息，认证费用将从您的账户中扣除。\r\n        </n-alert>\r\n\r\n        <div class=\"payment-options\">\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'alipay' }\"\r\n            @click=\"selectedPayment = 'alipay'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#1677ff\">\r\n                <logo-alipay />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>支付宝识脸支付</h4>\r\n              <p>通过支付宝识脸支付获取实名信息</p>\r\n              <div class=\"payment-features\">\r\n                <span class=\"feature\">• 人脸识别验证</span>\r\n                <span class=\"feature\">• 自动获取实名信息</span>\r\n              </div>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.2</span>\r\n                <span class=\"original-price\">¥2.0</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-badge\">\r\n              <n-tag type=\"success\" size=\"small\">推荐</n-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'wechat' }\"\r\n            @click=\"selectedPayment = 'wechat'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#07c160\">\r\n                <logo-wechat />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>微信识脸支付</h4>\r\n              <p>通过微信识脸支付获取实名信息</p>\r\n              <div class=\"payment-features\">\r\n                <span class=\"feature\">• 人脸识别验证</span>\r\n                <span class=\"feature\">• 自动获取实名信息</span>\r\n              </div>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.5</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"verification-process\">\r\n          <h4>识脸支付认证流程：</h4>\r\n          <div class=\"process-steps\">\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><card-outline /></n-icon>\r\n              <span>选择支付平台</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><scan-outline /></n-icon>\r\n              <span>人脸识别验证</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><wallet-outline /></n-icon>\r\n              <span>完成支付</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><person-outline /></n-icon>\r\n              <span>获取实名信息</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><checkmark-circle-outline /></n-icon>\r\n              <span>认证完成</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"face-payment-notice\">\r\n          <n-alert title=\"识脸支付说明\" type=\"warning\" style=\"margin-top: 16px;\">\r\n            <div class=\"notice-content\">\r\n              <p><strong>安全保障：</strong></p>\r\n              <ul>\r\n                <li>使用支付平台的人脸识别技术，确保本人操作</li>\r\n                <li>支付成功后自动获取您在支付平台的实名信息</li>\r\n                <li>所有信息仅用于身份认证，严格保护隐私</li>\r\n              </ul>\r\n              <p><strong>注意事项：</strong></p>\r\n              <ul>\r\n                <li>请确保您的支付账户已完成实名认证</li>\r\n                <li>支付过程中需要进行人脸识别验证</li>\r\n                <li>认证信息将临时存储30分钟后自动清除</li>\r\n              </ul>\r\n            </div>\r\n          </n-alert>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel1Modal = false\">取消</n-button>\r\n          <n-button\r\n            type=\"primary\"\r\n            @click=\"proceedLevel1Payment\"\r\n            :disabled=\"!selectedPayment\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始识脸支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 二级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel2Modal\" preset=\"card\" title=\"二级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level2-content\">\r\n        <n-alert title=\"二要素身份验证\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          二级认证完全免费，通过姓名和身份证号进行二要素验证。\r\n        </n-alert>\r\n\r\n        <n-form\r\n          ref=\"level2FormRef\"\r\n          :model=\"level2Form\"\r\n          :rules=\"level2Rules\"\r\n          label-placement=\"top\"\r\n          size=\"medium\"\r\n        >\r\n          <n-form-item label=\"真实姓名\" path=\"realName\">\r\n            <n-input\r\n              v-model:value=\"level2Form.realName\"\r\n              placeholder=\"请输入您的真实姓名\"\r\n              :disabled=\"level2Loading\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><person /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n\r\n          <n-form-item label=\"身份证号码\" path=\"idNumber\">\r\n            <n-input\r\n              v-model:value=\"level2Form.idNumber\"\r\n              placeholder=\"请输入您的身份证号码\"\r\n              :disabled=\"level2Loading\"\r\n              maxlength=\"18\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><card-outline /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n        </n-form>\r\n\r\n        <div class=\"verification-instructions\">\r\n          <h4>验证说明：</h4>\r\n          <ul>\r\n            <li>请确保输入的姓名与身份证上的姓名完全一致</li>\r\n            <li>身份证号码必须是18位有效号码</li>\r\n            <li>验证过程通过权威数据源进行核实</li>\r\n            <li>您的个人信息将被严格保密</li>\r\n          </ul>\r\n        </div>\r\n\r\n        <div v-if=\"level2VerificationResult\" class=\"verification-result-display\">\r\n          <n-alert\r\n            :title=\"level2VerificationResult.success ? '验证成功' : '验证失败'\"\r\n            :type=\"level2VerificationResult.success ? 'success' : 'error'\"\r\n          >\r\n            {{ level2VerificationResult.message }}\r\n          </n-alert>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel2Modal = false\" :disabled=\"level2Loading\">取消</n-button>\r\n          <n-button\r\n            v-if=\"!level2VerificationResult?.success\"\r\n            type=\"primary\"\r\n            @click=\"submitLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n            :disabled=\"!isLevel2FormValid\"\r\n          >\r\n            开始验证\r\n          </n-button>\r\n          <n-button\r\n            v-else\r\n            type=\"primary\"\r\n            @click=\"completeLevel2Verification\"\r\n          >\r\n            完成认证\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 识脸支付结果组件 -->\r\n    <face-payment-result\r\n      v-model:show=\"showPaymentResult\"\r\n      :status=\"paymentStatus\"\r\n      :payment-method=\"selectedPayment\"\r\n      :amount=\"selectedPayment === 'alipay' ? '1.2' : '1.5'\"\r\n      :real-name=\"paymentResult.realName\"\r\n      :id-number=\"paymentResult.idNumber\"\r\n      :verified-at=\"paymentResult.verifiedAt\"\r\n      :error-message=\"paymentResult.errorMessage\"\r\n      @confirm=\"handlePaymentResultConfirm\"\r\n      @retry=\"handlePaymentRetry\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport {\r\n  NCard,\r\n  NButton,\r\n  NAlert,\r\n  NIcon,\r\n  NAvatar,\r\n  NModal,\r\n  NTag,\r\n  NSpin,\r\n  NForm,\r\n  NFormItem,\r\n  NInput,\r\n  useMessage\r\n} from 'naive-ui';\r\nimport {\r\n  ShieldOutline,\r\n  ShieldCheckmarkOutline,\r\n  Checkmark,\r\n  LogoAlipay,\r\n  LogoWechat,\r\n  CardOutline,\r\n  WalletOutline,\r\n  CheckmarkCircleOutline,\r\n  CloseCircleOutline,\r\n  ArrowForward,\r\n  Person,\r\n  PersonOutline,\r\n  ScanOutline\r\n} from '@vicons/ionicons5';\r\nimport { useUserStore } from '../stores/user';\r\nimport FacePaymentResult from '../components/FacePaymentResult.vue';\r\nimport { getApiClient } from '../utils/api';\r\n\r\nconst router = useRouter();\r\nconst message = useMessage();\r\nconst userStore = useUserStore();\r\nconst apiClient = getApiClient();\r\n\r\n// 认证状态\r\nconst level1Completed = ref(false);\r\nconst level2Completed = ref(false);\r\nconst level1Loading = ref(false);\r\nconst level2Loading = ref(false);\r\n\r\n// 模态框状态\r\nconst showLevel1Modal = ref(false);\r\nconst showLevel2Modal = ref(false);\r\n\r\n// 一级认证相关\r\nconst selectedPayment = ref('');\r\n\r\n// 识脸支付结果相关\r\nconst showPaymentResult = ref(false);\r\nconst paymentStatus = ref('processing'); // processing, success, failed\r\nconst paymentResult = ref({\r\n  realName: '',\r\n  idNumber: '',\r\n  verifiedAt: null,\r\n  errorMessage: ''\r\n});\r\n\r\n// 二级认证相关\r\nconst level2FormRef = ref(null);\r\nconst level2Form = ref({\r\n  realName: '',\r\n  idNumber: ''\r\n});\r\nconst level2VerificationResult = ref(null);\r\n\r\n// 二级认证表单验证规则\r\nconst level2Rules = {\r\n  realName: [\r\n    {\r\n      required: true,\r\n      message: '请输入真实姓名',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      min: 2,\r\n      max: 20,\r\n      message: '姓名长度应在2-20个字符之间',\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ],\r\n  idNumber: [\r\n    {\r\n      required: true,\r\n      message: '请输入身份证号码',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      validator(rule, value) {\r\n        const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n        if (!reg.test(value)) {\r\n          return new Error('请输入正确的身份证号码');\r\n        }\r\n        return true;\r\n      },\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ]\r\n};\r\n\r\n// 表单验证状态\r\nconst isLevel2FormValid = computed(() => {\r\n  return level2Form.value.realName.trim() &&\r\n         level2Form.value.idNumber.trim() &&\r\n         /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/.test(level2Form.value.idNumber);\r\n});\r\n\r\n// 检查认证状态\r\nconst checkVerificationStatus = async () => {\r\n  try {\r\n    const response = await apiClient.get('/users/verification-status');\r\n    level1Completed.value = response.data.level1Completed || false;\r\n    level2Completed.value = response.data.level2Completed || false;\r\n  } catch (error) {\r\n    console.error('获取认证状态失败:', error);\r\n  }\r\n};\r\n\r\n// 开始一级认证\r\nconst startLevel1Verification = () => {\r\n  showLevel1Modal.value = true;\r\n  selectedPayment.value = 'alipay'; // 默认选择支付宝\r\n};\r\n\r\n// 处理一级认证支付\r\nconst proceedLevel1Payment = async () => {\r\n  if (!selectedPayment.value) {\r\n    message.warning('请选择支付方式');\r\n    return;\r\n  }\r\n\r\n  level1Loading.value = true;\r\n\r\n  // 关闭选择模态框，显示支付结果模态框\r\n  showLevel1Modal.value = false;\r\n  showPaymentResult.value = true;\r\n  paymentStatus.value = 'processing';\r\n\r\n  // 重置支付结果\r\n  paymentResult.value = {\r\n    realName: '',\r\n    idNumber: '',\r\n    verifiedAt: null,\r\n    errorMessage: ''\r\n  };\r\n\r\n  try {\r\n    // 调用支付API\r\n    const response = await apiClient.post('/verification/level1/payment', {\r\n      paymentMethod: selectedPayment.value,\r\n      amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5\r\n    });\r\n\r\n    if (response.data.success) {\r\n      // 模拟识脸支付过程\r\n      await simulateFacePayment(response.data.orderId);\r\n    } else {\r\n      throw new Error(response.data.message || '发起支付失败');\r\n    }\r\n  } catch (error) {\r\n    paymentStatus.value = 'failed';\r\n    paymentResult.value.errorMessage = error.response?.data?.message || error.message || '发起支付失败';\r\n  } finally {\r\n    level1Loading.value = false;\r\n  }\r\n};\r\n\r\n// 模拟识脸支付过程\r\nconst simulateFacePayment = async (orderId) => {\r\n  // 模拟识脸支付的各个步骤\r\n  const steps = [\r\n    { delay: 2000, message: '正在进行人脸识别...' },\r\n    { delay: 3000, message: '人脸识别成功，正在处理支付...' },\r\n    { delay: 2000, message: '支付成功，正在获取实名信息...' },\r\n    { delay: 1500, message: '认证完成' }\r\n  ];\r\n\r\n  for (let i = 0; i < steps.length; i++) {\r\n    await new Promise(resolve => setTimeout(resolve, steps[i].delay));\r\n\r\n    if (i === steps.length - 1) {\r\n      // 最后一步，检查支付状态\r\n      await checkPaymentStatus(orderId);\r\n    }\r\n  }\r\n};\r\n\r\n// 检查支付状态\r\nconst checkPaymentStatus = async (orderId) => {\r\n  try {\r\n    const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);\r\n\r\n    if (response.data.status === 'completed') {\r\n      // 支付成功，显示成功结果\r\n      paymentStatus.value = 'success';\r\n      paymentResult.value = {\r\n        realName: response.data.data?.realName || '张三',\r\n        idNumber: response.data.data?.idNumber || '110101199001011234',\r\n        verifiedAt: response.data.data?.verifiedAt || new Date(),\r\n        errorMessage: ''\r\n      };\r\n\r\n      level1Completed.value = true;\r\n\r\n      // 刷新认证状态\r\n      await loadVerificationStatus();\r\n\r\n    } else if (response.data.status === 'failed') {\r\n      // 支付失败\r\n      paymentStatus.value = 'failed';\r\n      paymentResult.value.errorMessage = response.data.message || '支付失败，请重试';\r\n\r\n    } else {\r\n      // 支付处理中，继续等待\r\n      setTimeout(() => checkPaymentStatus(orderId), 2000);\r\n    }\r\n  } catch (error) {\r\n    console.error('检查支付状态失败:', error);\r\n    paymentStatus.value = 'failed';\r\n    paymentResult.value.errorMessage = '检查支付状态失败，请重试';\r\n  }\r\n};\r\n\r\n// 处理支付结果确认\r\nconst handlePaymentResultConfirm = () => {\r\n  showPaymentResult.value = false;\r\n\r\n  if (paymentStatus.value === 'success') {\r\n    message.success('一级认证完成！');\r\n  }\r\n};\r\n\r\n// 处理支付重试\r\nconst handlePaymentRetry = () => {\r\n  showPaymentResult.value = false;\r\n  showLevel1Modal.value = true;\r\n};\r\n\r\n// 开始二级认证\r\nconst startLevel2Verification = () => {\r\n  showLevel2Modal.value = true;\r\n  level2VerificationResult.value = null;\r\n  // 重置表单\r\n  level2Form.value = {\r\n    realName: '',\r\n    idNumber: ''\r\n  };\r\n};\r\n\r\n// 提交二级认证\r\nconst submitLevel2Verification = async () => {\r\n  try {\r\n    // 验证表单\r\n    await level2FormRef.value?.validate();\r\n\r\n    level2Loading.value = true;\r\n    level2VerificationResult.value = null;\r\n\r\n    const response = await apiClient.post('/verification/level2/verify', {\r\n      realName: level2Form.value.realName.trim(),\r\n      idNumber: level2Form.value.idNumber.trim()\r\n    });\r\n\r\n    if (response.data.success) {\r\n      level2VerificationResult.value = {\r\n        success: true,\r\n        message: response.data.message || '身份验证成功'\r\n      };\r\n      message.success('二级认证成功！');\r\n    } else {\r\n      level2VerificationResult.value = {\r\n        success: false,\r\n        message: response.data.message || '身份验证失败'\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error('二级认证失败:', error);\r\n    level2VerificationResult.value = {\r\n      success: false,\r\n      message: error.response?.data?.message || '身份验证失败，请稍后重试'\r\n    };\r\n    message.error(level2VerificationResult.value.message);\r\n  } finally {\r\n    level2Loading.value = false;\r\n  }\r\n};\r\n\r\n// 完成二级认证\r\nconst completeLevel2Verification = () => {\r\n  level2Completed.value = true;\r\n  showLevel2Modal.value = false;\r\n  message.success('二级认证已完成！');\r\n};\r\n\r\n// 组件挂载时检查认证状态\r\nonMounted(() => {\r\n  checkVerificationStatus();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.verification-container {\r\n  padding: 16px;\r\n  min-height: 100%;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 24px;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-overview {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 24px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.level-card {\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.level-card:hover {\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.level-card.completed {\r\n  border: 2px solid #18a058;\r\n  background: linear-gradient(135deg, rgba(24, 160, 88, 0.05) 0%, rgba(24, 160, 88, 0.02) 100%);\r\n}\r\n\r\n.level-card.disabled {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.level-card.primary-card {\r\n  border: 2px solid #2080f0;\r\n  background: linear-gradient(135deg, rgba(32, 128, 240, 0.05) 0%, rgba(32, 128, 240, 0.02) 100%);\r\n}\r\n\r\n.level-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.level-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n.level-description {\r\n  color: var(--n-text-color-2);\r\n  margin-bottom: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.level-features {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 一级认证模态框样式 */\r\n.level1-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-option {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border: 2px solid var(--n-border-color);\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.payment-option:hover {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-hover);\r\n}\r\n\r\n.payment-option.selected {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-suppl);\r\n}\r\n\r\n.payment-icon {\r\n  margin-right: 16px;\r\n}\r\n\r\n.payment-info {\r\n  flex: 1;\r\n}\r\n\r\n.payment-info h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.payment-info p {\r\n  margin: 0 0 8px 0;\r\n  color: var(--n-text-color-2);\r\n  font-size: 14px;\r\n}\r\n\r\n.payment-features {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4px;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.payment-features .feature {\r\n  font-size: 12px;\r\n  color: var(--n-success-color);\r\n  font-weight: 500;\r\n}\r\n\r\n.payment-price {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.price {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: var(--n-error-color);\r\n}\r\n\r\n.original-price {\r\n  font-size: 14px;\r\n  color: var(--n-text-color-3);\r\n  text-decoration: line-through;\r\n}\r\n\r\n.payment-badge {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n}\r\n\r\n.verification-process {\r\n  margin-top: 24px;\r\n}\r\n\r\n.verification-process h4 {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.process-steps {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  gap: 8px;\r\n  padding: 16px;\r\n  background-color: var(--n-color-target);\r\n  border-radius: 8px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.process-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 11px;\r\n  color: var(--n-text-color-2);\r\n  min-width: 50px;\r\n}\r\n\r\n.process-step span {\r\n  text-align: center;\r\n  max-width: 50px;\r\n  line-height: 1.2;\r\n}\r\n\r\n.face-payment-notice {\r\n  margin-top: 16px;\r\n}\r\n\r\n.notice-content {\r\n  font-size: 13px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.notice-content p {\r\n  margin: 8px 0 4px 0;\r\n  font-weight: 500;\r\n}\r\n\r\n.notice-content ul {\r\n  margin: 4px 0 12px 16px;\r\n  padding: 0;\r\n}\r\n\r\n.notice-content li {\r\n  margin: 2px 0;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n/* 二级认证模态框样式 */\r\n.level2-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.verification-instructions {\r\n  margin-top: 24px;\r\n  padding: 16px;\r\n  background-color: var(--n-color-target);\r\n  border-radius: 8px;\r\n}\r\n\r\n.verification-instructions h4 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-instructions ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n}\r\n\r\n.verification-instructions li {\r\n  margin-bottom: 8px;\r\n  color: var(--n-text-color-2);\r\n  line-height: 1.5;\r\n}\r\n\r\n.verification-result-display {\r\n  margin-top: 16px;\r\n}\r\n\r\n\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .verification-overview {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n\r\n  .payment-option {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .payment-icon {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .process-steps {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .verification-container {\r\n    padding: 12px;\r\n  }\r\n}\r\n</style>"], "mappings": "AA6TA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAC3D,SAASC,SAAS,QAAQ,YAAY;AACtC,SACEC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,UAAU,QACL,UAAU;AACjB,SACEC,aAAa,EACbC,sBAAsB,EACtBC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,WAAW,EACXC,aAAa,EACbC,sBAAsB,EACtBC,kBAAkB,EAClBC,YAAY,EACZC,MAAM,EACNC,aAAa,EACbC,WAAW,QACN,mBAAmB;AAC1B,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,SAASC,YAAY,QAAQ,cAAc;;;;;;;IAE3C,MAAMC,MAAM,GAAG7B,SAAS,CAAC,CAAC;IAC1B,MAAM8B,OAAO,GAAGlB,UAAU,CAAC,CAAC;IAC5B,MAAMmB,SAAS,GAAGL,YAAY,CAAC,CAAC;IAChC,MAAMM,SAAS,GAAGJ,YAAY,CAAC,CAAC;;IAEhC;IACA,MAAMK,eAAe,GAAGrC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMsC,eAAe,GAAGtC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMuC,aAAa,GAAGvC,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMwC,aAAa,GAAGxC,GAAG,CAAC,KAAK,CAAC;;IAEhC;IACA,MAAMyC,eAAe,GAAGzC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAM0C,eAAe,GAAG1C,GAAG,CAAC,KAAK,CAAC;;IAElC;IACA,MAAM2C,eAAe,GAAG3C,GAAG,CAAC,EAAE,CAAC;;IAE/B;IACA,MAAM4C,iBAAiB,GAAG5C,GAAG,CAAC,KAAK,CAAC;IACpC,MAAM6C,aAAa,GAAG7C,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC;IACzC,MAAM8C,aAAa,GAAG9C,GAAG,CAAC;MACxB+C,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,IAAI;MAChBC,YAAY,EAAE;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAGnD,GAAG,CAAC,IAAI,CAAC;IAC/B,MAAMoD,UAAU,GAAGpD,GAAG,CAAC;MACrB+C,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,MAAMK,wBAAwB,GAAGrD,GAAG,CAAC,IAAI,CAAC;;IAE1C;IACA,MAAMsD,WAAW,GAAG;MAClBP,QAAQ,EAAE,CACR;QACEQ,QAAQ,EAAE,IAAI;QACdrB,OAAO,EAAE,SAAS;QAClBsB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,EACD;QACEC,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,EAAE;QACPxB,OAAO,EAAE,iBAAiB;QAC1BsB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,CACF;MACDR,QAAQ,EAAE,CACR;QACEO,QAAQ,EAAE,IAAI;QACdrB,OAAO,EAAE,UAAU;QACnBsB,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC,EACD;QACEG,SAASA,CAACC,IAAI,EAAEC,KAAK,EAAE;UACrB,MAAMC,GAAG,GAAG,0CAA0C;UACtD,IAAI,CAACA,GAAG,CAACC,IAAI,CAACF,KAAK,CAAC,EAAE;YACpB,OAAO,IAAIG,KAAK,CAAC,aAAa,CAAC;UACjC;UACA,OAAO,IAAI;QACb,CAAC;QACDR,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO;MAC3B,CAAC;IAEL,CAAC;;IAED;IACA,MAAMS,iBAAiB,GAAGhE,QAAQ,CAAC,MAAM;MACvC,OAAOmD,UAAU,CAACS,KAAK,CAACd,QAAQ,CAACmB,IAAI,CAAC,CAAC,IAChCd,UAAU,CAACS,KAAK,CAACb,QAAQ,CAACkB,IAAI,CAAC,CAAC,IAChC,0CAA0C,CAACH,IAAI,CAACX,UAAU,CAACS,KAAK,CAACb,QAAQ,CAAC;IACnF,CAAC,CAAC;;IAEF;IACA,MAAMmB,uBAAuB,GAAG,MAAAA,CAAA,KAAY;MAC1C,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,GAAG,CAAC,4BAA4B,CAAC;QAClEhC,eAAe,CAACwB,KAAK,GAAGO,QAAQ,CAACE,IAAI,CAACjC,eAAe,IAAI,KAAK;QAC9DC,eAAe,CAACuB,KAAK,GAAGO,QAAQ,CAACE,IAAI,CAAChC,eAAe,IAAI,KAAK;MAChE,CAAC,CAAC,OAAOiC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF,CAAC;;IAED;IACA,MAAME,uBAAuB,GAAGA,CAAA,KAAM;MACpChC,eAAe,CAACoB,KAAK,GAAG,IAAI;MAC5BlB,eAAe,CAACkB,KAAK,GAAG,QAAQ,CAAC,CAAC;IACpC,CAAC;;IAED;IACA,MAAMa,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAAC/B,eAAe,CAACkB,KAAK,EAAE;QAC1B3B,OAAO,CAACyC,OAAO,CAAC,SAAS,CAAC;QAC1B;MACF;MAEApC,aAAa,CAACsB,KAAK,GAAG,IAAI;;MAE1B;MACApB,eAAe,CAACoB,KAAK,GAAG,KAAK;MAC7BjB,iBAAiB,CAACiB,KAAK,GAAG,IAAI;MAC9BhB,aAAa,CAACgB,KAAK,GAAG,YAAY;;MAElC;MACAf,aAAa,CAACe,KAAK,GAAG;QACpBd,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE,EAAE;QACZC,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE;MAChB,CAAC;MAED,IAAI;QACF;QACA,MAAMkB,QAAQ,GAAG,MAAMhC,SAAS,CAACwC,IAAI,CAAC,8BAA8B,EAAE;UACpEC,aAAa,EAAElC,eAAe,CAACkB,KAAK;UACpCiB,MAAM,EAAEnC,eAAe,CAACkB,KAAK,KAAK,QAAQ,GAAG,GAAG,GAAG;QACrD,CAAC,CAAC;QAEF,IAAIO,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;UACzB;UACA,MAAMC,mBAAmB,CAACZ,QAAQ,CAACE,IAAI,CAACW,OAAO,CAAC;QAClD,CAAC,MAAM;UACL,MAAM,IAAIjB,KAAK,CAACI,QAAQ,CAACE,IAAI,CAACpC,OAAO,IAAI,QAAQ,CAAC;QACpD;MACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;QACd1B,aAAa,CAACgB,KAAK,GAAG,QAAQ;QAC9Bf,aAAa,CAACe,KAAK,CAACX,YAAY,GAAGqB,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEpC,OAAO,IAAIqC,KAAK,CAACrC,OAAO,IAAI,QAAQ;MAC/F,CAAC,SAAS;QACRK,aAAa,CAACsB,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC;;IAED;IACA,MAAMmB,mBAAmB,GAAG,MAAOC,OAAO,IAAK;MAC7C;MACA,MAAMC,KAAK,GAAG,CACZ;QAAEC,KAAK,EAAE,IAAI;QAAEjD,OAAO,EAAE;MAAc,CAAC,EACvC;QAAEiD,KAAK,EAAE,IAAI;QAAEjD,OAAO,EAAE;MAAmB,CAAC,EAC5C;QAAEiD,KAAK,EAAE,IAAI;QAAEjD,OAAO,EAAE;MAAmB,CAAC,EAC5C;QAAEiD,KAAK,EAAE,IAAI;QAAEjD,OAAO,EAAE;MAAO,CAAC,CACjC;MAED,KAAK,IAAIkD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;QACrC,MAAM,IAAIE,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAEL,KAAK,CAACE,CAAC,CAAC,CAACD,KAAK,CAAC,CAAC;QAEjE,IAAIC,CAAC,KAAKF,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;UAC1B;UACA,MAAMI,kBAAkB,CAACR,OAAO,CAAC;QACnC;MACF;IACF,CAAC;;IAED;IACA,MAAMQ,kBAAkB,GAAG,MAAOR,OAAO,IAAK;MAC5C,IAAI;QACF,MAAMb,QAAQ,GAAG,MAAMhC,SAAS,CAACiC,GAAG,CAAC,uCAAuCY,OAAO,EAAE,CAAC;QAEtF,IAAIb,QAAQ,CAACE,IAAI,CAACoB,MAAM,KAAK,WAAW,EAAE;UACxC;UACA7C,aAAa,CAACgB,KAAK,GAAG,SAAS;UAC/Bf,aAAa,CAACe,KAAK,GAAG;YACpBd,QAAQ,EAAEqB,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAEvB,QAAQ,IAAI,IAAI;YAC9CC,QAAQ,EAAEoB,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAEtB,QAAQ,IAAI,oBAAoB;YAC9DC,UAAU,EAAEmB,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAErB,UAAU,IAAI,IAAI0C,IAAI,CAAC,CAAC;YACxDzC,YAAY,EAAE;UAChB,CAAC;UAEDb,eAAe,CAACwB,KAAK,GAAG,IAAI;;UAE5B;UACA,MAAM+B,sBAAsB,CAAC,CAAC;QAEhC,CAAC,MAAM,IAAIxB,QAAQ,CAACE,IAAI,CAACoB,MAAM,KAAK,QAAQ,EAAE;UAC5C;UACA7C,aAAa,CAACgB,KAAK,GAAG,QAAQ;UAC9Bf,aAAa,CAACe,KAAK,CAACX,YAAY,GAAGkB,QAAQ,CAACE,IAAI,CAACpC,OAAO,IAAI,UAAU;QAExE,CAAC,MAAM;UACL;UACAsD,UAAU,CAAC,MAAMC,kBAAkB,CAACR,OAAO,CAAC,EAAE,IAAI,CAAC;QACrD;MACF,CAAC,CAAC,OAAOV,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC1B,aAAa,CAACgB,KAAK,GAAG,QAAQ;QAC9Bf,aAAa,CAACe,KAAK,CAACX,YAAY,GAAG,cAAc;MACnD;IACF,CAAC;;IAED;IACA,MAAM2C,0BAA0B,GAAGA,CAAA,KAAM;MACvCjD,iBAAiB,CAACiB,KAAK,GAAG,KAAK;MAE/B,IAAIhB,aAAa,CAACgB,KAAK,KAAK,SAAS,EAAE;QACrC3B,OAAO,CAAC6C,OAAO,CAAC,SAAS,CAAC;MAC5B;IACF,CAAC;;IAED;IACA,MAAMe,kBAAkB,GAAGA,CAAA,KAAM;MAC/BlD,iBAAiB,CAACiB,KAAK,GAAG,KAAK;MAC/BpB,eAAe,CAACoB,KAAK,GAAG,IAAI;IAC9B,CAAC;;IAED;IACA,MAAMkC,uBAAuB,GAAGA,CAAA,KAAM;MACpCrD,eAAe,CAACmB,KAAK,GAAG,IAAI;MAC5BR,wBAAwB,CAACQ,KAAK,GAAG,IAAI;MACrC;MACAT,UAAU,CAACS,KAAK,GAAG;QACjBd,QAAQ,EAAE,EAAE;QACZC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC;;IAED;IACA,MAAMgD,wBAAwB,GAAG,MAAAA,CAAA,KAAY;MAC3C,IAAI;QACF;QACA,MAAM7C,aAAa,CAACU,KAAK,EAAEoC,QAAQ,CAAC,CAAC;QAErCzD,aAAa,CAACqB,KAAK,GAAG,IAAI;QAC1BR,wBAAwB,CAACQ,KAAK,GAAG,IAAI;QAErC,MAAMO,QAAQ,GAAG,MAAMhC,SAAS,CAACwC,IAAI,CAAC,6BAA6B,EAAE;UACnE7B,QAAQ,EAAEK,UAAU,CAACS,KAAK,CAACd,QAAQ,CAACmB,IAAI,CAAC,CAAC;UAC1ClB,QAAQ,EAAEI,UAAU,CAACS,KAAK,CAACb,QAAQ,CAACkB,IAAI,CAAC;QAC3C,CAAC,CAAC;QAEF,IAAIE,QAAQ,CAACE,IAAI,CAACS,OAAO,EAAE;UACzB1B,wBAAwB,CAACQ,KAAK,GAAG;YAC/BkB,OAAO,EAAE,IAAI;YACb7C,OAAO,EAAEkC,QAAQ,CAACE,IAAI,CAACpC,OAAO,IAAI;UACpC,CAAC;UACDA,OAAO,CAAC6C,OAAO,CAAC,SAAS,CAAC;QAC5B,CAAC,MAAM;UACL1B,wBAAwB,CAACQ,KAAK,GAAG;YAC/BkB,OAAO,EAAE,KAAK;YACd7C,OAAO,EAAEkC,QAAQ,CAACE,IAAI,CAACpC,OAAO,IAAI;UACpC,CAAC;QACH;MACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/BlB,wBAAwB,CAACQ,KAAK,GAAG;UAC/BkB,OAAO,EAAE,KAAK;UACd7C,OAAO,EAAEqC,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEpC,OAAO,IAAI;QAC5C,CAAC;QACDA,OAAO,CAACqC,KAAK,CAAClB,wBAAwB,CAACQ,KAAK,CAAC3B,OAAO,CAAC;MACvD,CAAC,SAAS;QACRM,aAAa,CAACqB,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC;;IAED;IACA,MAAMqC,0BAA0B,GAAGA,CAAA,KAAM;MACvC5D,eAAe,CAACuB,KAAK,GAAG,IAAI;MAC5BnB,eAAe,CAACmB,KAAK,GAAG,KAAK;MAC7B3B,OAAO,CAAC6C,OAAO,CAAC,UAAU,CAAC;IAC7B,CAAC;;IAED;IACA7E,SAAS,CAAC,MAAM;MACdiE,uBAAuB,CAAC,CAAC;IAC3B,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}