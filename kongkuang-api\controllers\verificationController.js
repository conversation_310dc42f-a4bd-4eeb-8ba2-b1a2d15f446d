const { User } = require('../models');
const config = require('../config/config');
const axios = require('axios');
const crypto = require('crypto');

// 获取用户认证状态
exports.getVerificationStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      level1Completed: user.level1_verified || false,
      level2Completed: user.level2_verified || false,
      verificationData: {
        realName: user.real_name,
        idNumber: user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
        level1VerifiedAt: user.level1_verified_at,
        level2VerifiedAt: user.level2_verified_at
      }
    });
  } catch (error) {
    console.error('获取认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取认证状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 一级认证 - 发起支付
exports.createLevel1Payment = async (req, res) => {
  try {
    const { paymentMethod, amount } = req.body;
    const userId = req.user.id;
    
    // 验证支付方式和金额
    const validPayments = {
      'alipay': 1.2,
      'wechat': 1.5
    };
    
    if (!validPayments[paymentMethod] || validPayments[paymentMethod] !== amount) {
      return res.status(400).json({
        success: false,
        message: '无效的支付方式或金额'
      });
    }
    
    // 检查用户是否已经完成一级认证
    const user = await User.findByPk(userId);
    if (user.level1_verified) {
      return res.status(400).json({
        success: false,
        message: '您已完成一级认证'
      });
    }
    
    // 生成订单ID
    const orderId = `L1_${userId}_${Date.now()}`;
    
    // 在开发环境中模拟支付
    if (config.server.env === 'development') {
      // 模拟支付链接
      const mockPaymentUrl = `http://localhost:3001/mock-payment?orderId=${orderId}&amount=${amount}&method=${paymentMethod}`;
      
      // 这里应该保存订单信息到数据库
      // 为了简化，我们直接返回模拟的支付链接
      
      return res.json({
        success: true,
        orderId,
        paymentUrl: mockPaymentUrl,
        message: '支付订单创建成功'
      });
    }
    
    // 生产环境中调用真实的支付API
    // TODO: 集成支付宝/微信支付API
    
    res.json({
      success: true,
      orderId,
      paymentUrl: '#', // 实际的支付链接
      message: '支付订单创建成功'
    });
  } catch (error) {
    console.error('创建支付订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建支付订单失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 检查一级认证支付状态
exports.checkLevel1PaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;
    
    // 在开发环境中模拟支付成功
    if (config.server.env === 'development') {
      // 模拟支付成功，更新用户认证状态
      const user = await User.findByPk(userId);
      if (!user.level1_verified) {
        await user.update({
          level1_verified: true,
          level1_verified_at: new Date(),
          real_name: '测试用户', // 在实际环境中从支付平台获取
          id_number: '110101199001011234' // 在实际环境中从支付平台获取
        });
      }
      
      return res.json({
        success: true,
        status: 'completed',
        message: '支付成功，一级认证完成'
      });
    }
    
    // 生产环境中查询真实的支付状态
    // TODO: 查询支付平台的订单状态
    
    res.json({
      success: true,
      status: 'pending',
      message: '支付处理中'
    });
  } catch (error) {
    console.error('查询支付状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询支付状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 二级认证 - 人脸识别
exports.faceRecognition = async (req, res) => {
  try {
    const { image, userId } = req.body;
    
    // 检查用户是否完成一级认证
    const user = await User.findByPk(userId);
    if (!user || !user.level1_verified) {
      return res.status(400).json({
        success: false,
        message: '请先完成一级认证'
      });
    }
    
    if (user.level2_verified) {
      return res.status(400).json({
        success: false,
        message: '您已完成二级认证'
      });
    }
    
    // 调用阿里云人脸识别API
    const faceResult = await callAliyunFaceAPI(image, user);
    
    if (faceResult.success) {
      // 生成验证ID用于后续完成认证
      const verificationId = crypto.randomUUID();
      
      // 这里应该保存验证结果到数据库或缓存
      // 为了简化，我们直接返回成功结果
      
      res.json({
        success: true,
        verificationId,
        confidence: faceResult.confidence,
        message: '人脸识别成功'
      });
    } else {
      res.json({
        success: false,
        message: faceResult.message || '人脸识别失败'
      });
    }
  } catch (error) {
    console.error('人脸识别失败:', error);
    res.status(500).json({
      success: false,
      message: '人脸识别服务异常',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 完成二级认证
exports.completeLevel2Verification = async (req, res) => {
  try {
    const { verificationId } = req.body;
    const userId = req.user.id;
    
    // 验证verificationId的有效性
    // 在实际环境中应该从数据库或缓存中验证
    
    const user = await User.findByPk(userId);
    if (!user || !user.level1_verified) {
      return res.status(400).json({
        success: false,
        message: '请先完成一级认证'
      });
    }
    
    if (user.level2_verified) {
      return res.status(400).json({
        success: false,
        message: '您已完成二级认证'
      });
    }
    
    // 更新用户二级认证状态
    await user.update({
      level2_verified: true,
      level2_verified_at: new Date()
    });
    
    res.json({
      success: true,
      message: '二级认证完成'
    });
  } catch (error) {
    console.error('完成二级认证失败:', error);
    res.status(500).json({
      success: false,
      message: '完成二级认证失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 调用阿里云人脸识别API
const callAliyunFaceAPI = async (imageBase64, user) => {
  try {
    // 在开发环境中模拟人脸识别成功
    if (config.server.env === 'development') {
      return {
        success: true,
        confidence: 0.95,
        message: '人脸识别成功（开发模式）'
      };
    }
    
    // 实际调用阿里云API
    const appCode = 'ac2c8231f12445928b757dd27e67dbce';
    const apiUrl = 'https://face.market.alicloudapi.com/face/human_face_compare';
    
    // 移除base64前缀
    const imageData = imageBase64.replace(/^data:image\/[a-z]+;base64,/, '');
    
    const response = await axios.post(apiUrl, {
      image1: imageData,
      image2: imageData, // 在实际环境中应该是身份证照片
      type: 1
    }, {
      headers: {
        'Authorization': `APPCODE ${appCode}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (response.data.success) {
      const confidence = response.data.data.confidence;
      return {
        success: confidence > 0.8, // 置信度阈值
        confidence,
        message: confidence > 0.8 ? '人脸识别成功' : '人脸识别失败，相似度不足'
      };
    } else {
      return {
        success: false,
        message: response.data.message || '人脸识别失败'
      };
    }
  } catch (error) {
    console.error('调用阿里云人脸识别API失败:', error);
    return {
      success: false,
      message: '人脸识别服务异常'
    };
  }
};

// 模拟支付页面（仅开发环境）
exports.mockPayment = async (req, res) => {
  if (config.server.env !== 'development') {
    return res.status(404).json({ message: 'Not found' });
  }
  
  const { orderId, amount, method } = req.query;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>模拟支付</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .payment-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .success { background: #4CAF50; color: white; }
        .cancel { background: #f44336; color: white; }
      </style>
    </head>
    <body>
      <h2>模拟支付页面</h2>
      <div class="payment-info">
        <p>订单号: ${orderId}</p>
        <p>支付方式: ${method === 'alipay' ? '支付宝' : '微信支付'}</p>
        <p>支付金额: ¥${amount}</p>
      </div>
      <button class="success" onclick="window.close()">确认支付</button>
      <button class="cancel" onclick="window.close()">取消支付</button>
      <script>
        // 3秒后自动关闭窗口（模拟支付成功）
        setTimeout(() => {
          window.close();
        }, 3000);
      </script>
    </body>
    </html>
  `);
};
