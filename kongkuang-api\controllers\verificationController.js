const { User } = require('../models');
const config = require('../config/config');
const aliyunConfig = require('../config/aliyun');
const axios = require('axios');
const crypto = require('crypto');
const level1Cache = require('../utils/level1Cache');
const { AlipayFaceAuthService, WechatFaceAuthService } = require('../services/paymentService');

// 获取用户认证状态
exports.getVerificationStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取一级认证状态（从缓存）
    const level1Info = level1Cache.getLevel1Info(userId);

    // 获取二级认证状态（从数据库）
    const level2Data = user.level2_verified ? {
      realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
      idNumber: user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
      verifiedAt: user.level2_verified_at,
      verificationData: user.level2_verification_data ? JSON.parse(user.level2_verification_data) : null
    } : null;

    res.json({
      success: true,
      level1Completed: !!level1Info,
      level2Completed: user.level2_verified || false,
      verificationData: {
        level1: level1Info,
        level2: level2Data
      }
    });
  } catch (error) {
    console.error('获取认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取认证状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 一级认证 - 创建识脸支付
exports.createLevel1Payment = async (req, res) => {
  try {
    const { paymentMethod } = req.body; // 'alipay' 或 'wechat'
    const userId = req.user.id;

    // 检查用户是否已经完成一级认证（从缓存检查）
    if (level1Cache.isLevel1Verified(userId)) {
      return res.status(400).json({
        success: false,
        message: '您已完成一级认证'
      });
    }

    // 检查用户是否已完成二级认证
    const user = await User.findByPk(userId);
    if (!user.level2_verified) {
      return res.status(400).json({
        success: false,
        message: '请先完成二级认证'
      });
    }

    let paymentService;
    let result;

    if (paymentMethod === 'alipay') {
      paymentService = new AlipayFaceAuthService();
      result = await paymentService.createFaceAuthOrder(userId, {
        username: user.username,
        email: user.email
      });
    } else if (paymentMethod === 'wechat') {
      paymentService = new WechatFaceAuthService();
      result = await paymentService.createFaceAuthOrder(userId, {
        username: user.username,
        email: user.email
      });
    } else {
      return res.status(400).json({
        success: false,
        message: '不支持的支付方式，请选择支付宝或微信'
      });
    }

    if (result.success) {
      res.json({
        success: true,
        orderId: result.orderId,
        paymentMethod: paymentMethod,
        amount: result.amount,
        paymentUrl: result.paymentUrl,
        qrCode: result.qrCode,
        message: result.message || `请使用${paymentMethod === 'alipay' ? '支付宝' : '微信'}进行识脸支付认证`
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.message || '创建识脸支付订单失败'
      });
    }

  } catch (error) {
    console.error('创建一级认证识脸支付失败:', error);
    res.status(500).json({
      success: false,
      message: '创建识脸支付订单失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 检查一级认证识脸支付状态
exports.checkLevel1PaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // 在开发环境中模拟识脸支付成功
    if (config.server.env === 'development') {
      let paymentService;
      let result;

      if (orderId.includes('ALIPAY_FACE')) {
        paymentService = new AlipayFaceAuthService();
        result = await paymentService.simulateFaceAuthSuccess(orderId, userId);
      } else if (orderId.includes('WECHAT_FACE')) {
        paymentService = new WechatFaceAuthService();
        result = await paymentService.simulateFaceAuthSuccess(orderId, userId);
      } else {
        return res.status(400).json({
          success: false,
          message: '无效的订单ID'
        });
      }

      if (result.success) {
        return res.json({
          success: true,
          status: 'completed',
          message: result.message,
          data: {
            ...result.data,
            expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
          }
        });
      } else {
        return res.status(400).json({
          success: false,
          message: result.message || '识脸支付认证失败'
        });
      }
    }

    // 生产环境中查询真实的支付状态
    // TODO: 查询支付平台的订单状态，获取识脸认证结果

    res.json({
      success: true,
      status: 'pending',
      message: '识脸支付处理中'
    });
  } catch (error) {
    console.error('查询识脸支付状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询识脸支付状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 二级认证 - 二要素验证（姓名+身份证号）
exports.level2Verification = async (req, res) => {
  try {
    const { realName, idNumber } = req.body;
    const userId = req.user.id;

    // 验证输入参数
    if (!realName || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '请输入姓名和身份证号码'
      });
    }

    // 验证身份证号码格式
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(idNumber)) {
      return res.status(400).json({
        success: false,
        message: '身份证号码格式不正确'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (user.level2_verified) {
      return res.status(400).json({
        success: false,
        message: '您已完成二级认证'
      });
    }

    // 调用阿里云二要素验证API
    const verificationResult = await callAliyunTwoFactorAPI(realName, idNumber);

    if (verificationResult.success) {
      // 准备详细的验证数据
      const verificationData = {
        verifiedAt: new Date(),
        apiProvider: 'aliyun',
        verificationMethod: 'two_factor',
        confidence: verificationResult.confidence || 1.0,
        apiResponse: verificationResult.data || {},
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };

      // 高强度存储到数据库
      await user.update({
        real_name: realName,
        id_number: idNumber,
        level2_verified: true,
        level2_verified_at: new Date(),
        level2_verification_data: JSON.stringify(verificationData)
      });

      console.log(`二级认证成功 - 用户ID: ${userId}, 姓名: ${realName.replace(/(.{1}).*(.{1})/, '$1***$2')}`);

      res.json({
        success: true,
        message: '二级认证成功',
        data: {
          realName: realName.replace(/(.{1}).*(.{1})/, '$1***$2'),
          idNumber: idNumber.replace(/(.{6}).*(.{4})/, '$1****$2'),
          verifiedAt: verificationData.verifiedAt,
          confidence: verificationData.confidence
        }
      });
    } else {
      console.log(`二级认证失败 - 用户ID: ${userId}, 原因: ${verificationResult.message}`);
      res.json({
        success: false,
        message: verificationResult.message || '身份验证失败，请检查姓名和身份证号码是否正确'
      });
    }
  } catch (error) {
    console.error('二级认证失败:', error);
    res.status(500).json({
      success: false,
      message: '身份验证服务异常',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取二级认证状态
exports.getLevel2Status = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      level2Verified: user.level2_verified || false,
      realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
      idNumber: user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
      verifiedAt: user.level2_verified_at
    });
  } catch (error) {
    console.error('获取二级认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取认证状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 调用阿里云二要素验证API
const callAliyunTwoFactorAPI = async (realName, idNumber) => {
  try {
    // 获取阿里云API配置
    const apiConfig = aliyunConfig.getIdVerificationConfig();

    // 验证配置完整性
    aliyunConfig.validateConfig();

    // 判断是否使用真实API
    const useRealAPI = aliyunConfig.shouldUseRealAPI();

    if (!useRealAPI && config.server.env === 'development') {
      console.log(`[开发模式] 模拟二要素验证: ${realName}, ${idNumber.replace(/(.{6}).*(.{4})/, '$1****$2')}`);

      // 模拟一些验证失败的情况用于测试
      if (realName === '测试失败' || idNumber === '000000000000000000') {
        return {
          success: false,
          message: '身份信息验证失败（开发模式）',
          confidence: 0.0,
          data: {
            verifyResult: '0',
            description: '验证失败'
          }
        };
      }

      // 模拟成功响应
      return {
        success: true,
        message: '身份验证成功（开发模式）',
        confidence: 1.0,
        data: {
          name: realName,
          idcard: idNumber,
          verifyResult: '1',
          description: '验证通过'
        }
      };
    }

    // 构建GET请求URL（根据CSDN博客示例）
    const requestUrl = `${apiConfig.apiUrl}?idCard=${idNumber}&name=${encodeURIComponent(realName)}`;

    console.log('调用阿里云二要素验证API:', {
      name: realName,
      idcard: idNumber.replace(/(.{6}).*(.{4})/, '$1****$2'),
      apiUrl: apiConfig.apiUrl,
      method: 'GET',
      appCode: apiConfig.appCode.substring(0, 8) + '...'
    });

    // 调用真实的阿里云API（使用GET方法）
    const response = await axios.get(requestUrl, {
      headers: {
        'Authorization': `APPCODE ${apiConfig.appCode}`
      },
      timeout: apiConfig.timeout
    });

    console.log('阿里云API响应状态:', response.status);
    console.log('阿里云API响应数据:', JSON.stringify(response.data, null, 2));

    // 解析响应结果（根据实际API响应格式）
    if (response.data) {
      const { status, msg, traceId } = response.data;

      if (status === '200') {
        // 验证成功
        return {
          success: true,
          message: '身份验证成功',
          confidence: 1.0,
          data: {
            name: realName,
            idcard: idNumber,
            verifyResult: '1',
            description: msg || '验证通过',
            traceId: traceId,
            apiResponse: response.data
          }
        };
      } else if (status === '201') {
        // 验证失败（姓名与身份证不匹配）
        return {
          success: false,
          message: '身份信息验证失败，姓名与身份证号码不匹配',
          confidence: 0.0,
          data: {
            verifyResult: '0',
            description: msg || '验证失败',
            traceId: traceId,
            apiResponse: response.data
          }
        };
      } else if (status === '205') {
        // 身份证格式不正确
        return {
          success: false,
          message: '身份证号码格式不正确，请检查后重试',
          confidence: 0.0,
          data: {
            verifyResult: '0',
            description: msg || '身份证格式错误',
            traceId: traceId,
            apiResponse: response.data
          }
        };
      } else {
        // 其他状态
        return {
          success: false,
          message: msg || '身份验证失败',
          confidence: 0.0,
          data: {
            verifyResult: '0',
            description: msg || '验证失败',
            status: status,
            traceId: traceId,
            apiResponse: response.data
          }
        };
      }
    } else {
      return {
        success: false,
        message: '身份验证服务响应异常',
        confidence: 0.0
      };
    }
  } catch (error) {
    console.error('调用阿里云二要素验证API失败:', error);

    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        message: '验证服务超时，请稍后重试',
        confidence: 0.0
      };
    }

    if (error.response) {
      console.error('API错误响应状态:', error.response.status);
      console.error('API错误响应数据:', error.response.data);

      const errorMessage = error.response.data?.message ||
                          error.response.data?.error ||
                          `API请求失败 (状态码: ${error.response.status})`;

      return {
        success: false,
        message: errorMessage,
        confidence: 0.0,
        data: {
          httpStatus: error.response.status,
          errorResponse: error.response.data
        }
      };
    }

    return {
      success: false,
      message: '身份验证服务异常，请稍后重试',
      confidence: 0.0,
      data: {
        errorType: error.name,
        errorMessage: error.message
      }
    };
  }
};

// 模拟支付页面（仅开发环境）
exports.mockPayment = async (req, res) => {
  if (config.server.env !== 'development') {
    return res.status(404).json({ message: 'Not found' });
  }
  
  const { orderId, amount, method } = req.query;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>模拟支付</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .payment-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .success { background: #4CAF50; color: white; }
        .cancel { background: #f44336; color: white; }
      </style>
    </head>
    <body>
      <h2>模拟支付页面</h2>
      <div class="payment-info">
        <p>订单号: ${orderId}</p>
        <p>支付方式: ${method === 'alipay' ? '支付宝' : '微信支付'}</p>
        <p>支付金额: ¥${amount}</p>
      </div>
      <button class="success" onclick="window.close()">确认支付</button>
      <button class="cancel" onclick="window.close()">取消支付</button>
      <script>
        // 3秒后自动关闭窗口（模拟支付成功）
        setTimeout(() => {
          window.close();
        }, 3000);
      </script>
    </body>
    </html>
  `);
};
