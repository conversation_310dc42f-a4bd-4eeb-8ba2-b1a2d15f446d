const { User } = require('../models');
const config = require('../config/config');
const axios = require('axios');
const crypto = require('crypto');
const level1Cache = require('../utils/level1Cache');

// 获取用户认证状态
exports.getVerificationStatus = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取一级认证状态（从缓存）
    const level1Info = level1Cache.getLevel1Info(userId);

    // 获取二级认证状态（从数据库）
    const level2Data = user.level2_verified ? {
      realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
      idNumber: user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
      verifiedAt: user.level2_verified_at,
      verificationData: user.level2_verification_data ? JSON.parse(user.level2_verification_data) : null
    } : null;

    res.json({
      success: true,
      level1Completed: !!level1Info,
      level2Completed: user.level2_verified || false,
      verificationData: {
        level1: level1Info,
        level2: level2Data
      }
    });
  } catch (error) {
    console.error('获取认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取认证状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 一级认证 - 发起支付
exports.createLevel1Payment = async (req, res) => {
  try {
    const { paymentMethod, amount } = req.body;
    const userId = req.user.id;
    
    // 验证支付方式和金额
    const validPayments = {
      'alipay': 1.2,
      'wechat': 1.5
    };
    
    if (!validPayments[paymentMethod] || validPayments[paymentMethod] !== amount) {
      return res.status(400).json({
        success: false,
        message: '无效的支付方式或金额'
      });
    }
    
    // 检查用户是否已经完成一级认证（从缓存检查）
    if (level1Cache.isLevel1Verified(userId)) {
      return res.status(400).json({
        success: false,
        message: '您已完成一级认证'
      });
    }
    
    // 生成订单ID
    const orderId = `L1_${userId}_${Date.now()}`;
    
    // 在开发环境中模拟支付
    if (config.server.env === 'development') {
      // 模拟支付链接
      const mockPaymentUrl = `http://localhost:3001/mock-payment?orderId=${orderId}&amount=${amount}&method=${paymentMethod}`;
      
      // 这里应该保存订单信息到数据库
      // 为了简化，我们直接返回模拟的支付链接
      
      return res.json({
        success: true,
        orderId,
        paymentUrl: mockPaymentUrl,
        message: '支付订单创建成功'
      });
    }
    
    // 生产环境中调用真实的支付API
    // TODO: 集成支付宝/微信支付API
    
    res.json({
      success: true,
      orderId,
      paymentUrl: '#', // 实际的支付链接
      message: '支付订单创建成功'
    });
  } catch (error) {
    console.error('创建支付订单失败:', error);
    res.status(500).json({
      success: false,
      message: '创建支付订单失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 检查一级认证支付状态
exports.checkLevel1PaymentStatus = async (req, res) => {
  try {
    const { orderId } = req.params;
    const userId = req.user.id;

    // 在开发环境中模拟支付成功
    if (config.server.env === 'development') {
      // 模拟从支付平台获取的实名信息
      const mockPaymentData = {
        verified: true,
        verifiedAt: new Date(),
        paymentMethod: orderId.includes('alipay') ? 'alipay' : 'wechat',
        realName: '张三', // 模拟从支付平台获取的真实姓名
        idNumber: '110101199001011234', // 模拟从支付平台获取的身份证号
        orderId: orderId,
        amount: orderId.includes('alipay') ? 1.2 : 1.5
      };

      // 将一级认证信息存储到缓存（不保存到数据库）
      level1Cache.setLevel1Status(userId, mockPaymentData);

      console.log(`一级认证完成 - 用户ID: ${userId}, 支付方式: ${mockPaymentData.paymentMethod}`);

      return res.json({
        success: true,
        status: 'completed',
        message: '支付成功，一级认证完成',
        data: {
          paymentMethod: mockPaymentData.paymentMethod,
          verifiedAt: mockPaymentData.verifiedAt,
          expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30分钟后过期
        }
      });
    }

    // 生产环境中查询真实的支付状态
    // TODO: 查询支付平台的订单状态，获取实名信息

    res.json({
      success: true,
      status: 'pending',
      message: '支付处理中'
    });
  } catch (error) {
    console.error('查询支付状态失败:', error);
    res.status(500).json({
      success: false,
      message: '查询支付状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 二级认证 - 二要素验证（姓名+身份证号）
exports.level2Verification = async (req, res) => {
  try {
    const { realName, idNumber } = req.body;
    const userId = req.user.id;

    // 验证输入参数
    if (!realName || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '请输入姓名和身份证号码'
      });
    }

    // 验证身份证号码格式
    const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    if (!idCardRegex.test(idNumber)) {
      return res.status(400).json({
        success: false,
        message: '身份证号码格式不正确'
      });
    }

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (user.level2_verified) {
      return res.status(400).json({
        success: false,
        message: '您已完成二级认证'
      });
    }

    // 调用阿里云二要素验证API
    const verificationResult = await callAliyunTwoFactorAPI(realName, idNumber);

    if (verificationResult.success) {
      // 准备详细的验证数据
      const verificationData = {
        verifiedAt: new Date(),
        apiProvider: 'aliyun',
        verificationMethod: 'two_factor',
        confidence: verificationResult.confidence || 1.0,
        apiResponse: verificationResult.data || {},
        ipAddress: req.ip || req.connection.remoteAddress,
        userAgent: req.get('User-Agent')
      };

      // 高强度存储到数据库
      await user.update({
        real_name: realName,
        id_number: idNumber,
        level2_verified: true,
        level2_verified_at: new Date(),
        level2_verification_data: JSON.stringify(verificationData)
      });

      console.log(`二级认证成功 - 用户ID: ${userId}, 姓名: ${realName.replace(/(.{1}).*(.{1})/, '$1***$2')}`);

      res.json({
        success: true,
        message: '二级认证成功',
        data: {
          realName: realName.replace(/(.{1}).*(.{1})/, '$1***$2'),
          idNumber: idNumber.replace(/(.{6}).*(.{4})/, '$1****$2'),
          verifiedAt: verificationData.verifiedAt,
          confidence: verificationData.confidence
        }
      });
    } else {
      console.log(`二级认证失败 - 用户ID: ${userId}, 原因: ${verificationResult.message}`);
      res.json({
        success: false,
        message: verificationResult.message || '身份验证失败，请检查姓名和身份证号码是否正确'
      });
    }
  } catch (error) {
    console.error('二级认证失败:', error);
    res.status(500).json({
      success: false,
      message: '身份验证服务异常',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取二级认证状态
exports.getLevel2Status = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      level2Verified: user.level2_verified || false,
      realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
      idNumber: user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null,
      verifiedAt: user.level2_verified_at
    });
  } catch (error) {
    console.error('获取二级认证状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取认证状态失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 调用阿里云二要素验证API
const callAliyunTwoFactorAPI = async (realName, idNumber) => {
  try {
    // 在开发环境中模拟验证成功
    if (config.server.env === 'development') {
      console.log(`[开发模式] 模拟二要素验证: ${realName}, ${idNumber}`);
      // 模拟一些验证失败的情况用于测试
      if (realName === '测试失败' || idNumber === '000000000000000000') {
        return {
          success: false,
          message: '身份信息验证失败（开发模式）'
        };
      }
      return {
        success: true,
        message: '身份验证成功（开发模式）'
      };
    }

    // 阿里云二要素验证API配置
    const appCode = 'ac2c8231f12445928b757dd27e67dbce';
    const apiUrl = 'https://idcert.market.alicloudapi.com/idcert';

    // 构建请求参数
    const requestData = {
      name: realName,
      idcard: idNumber
    };

    console.log('调用阿里云二要素验证API:', { name: realName, idcard: idNumber.replace(/(.{6}).*(.{4})/, '$1****$2') });

    const response = await axios.post(apiUrl, requestData, {
      headers: {
        'Authorization': `APPCODE ${appCode}`,
        'Content-Type': 'application/json; charset=UTF-8'
      },
      timeout: 10000 // 10秒超时
    });

    console.log('阿里云API响应:', response.data);

    // 解析响应结果
    if (response.data && response.data.code === 200) {
      const result = response.data.data;

      if (result && result.result === '1') {
        return {
          success: true,
          message: '身份验证成功',
          data: {
            name: realName,
            idcard: idNumber,
            verifyResult: result.result,
            description: result.desc || '验证通过'
          }
        };
      } else {
        return {
          success: false,
          message: result?.desc || '身份信息验证失败，请检查姓名和身份证号码是否正确',
          data: {
            verifyResult: result?.result || '0',
            description: result?.desc || '验证失败'
          }
        };
      }
    } else {
      return {
        success: false,
        message: response.data?.message || '身份验证服务异常'
      };
    }
  } catch (error) {
    console.error('调用阿里云二要素验证API失败:', error);

    if (error.code === 'ECONNABORTED') {
      return {
        success: false,
        message: '验证服务超时，请稍后重试'
      };
    }

    if (error.response) {
      console.error('API错误响应:', error.response.data);
      return {
        success: false,
        message: error.response.data?.message || '身份验证服务异常'
      };
    }

    return {
      success: false,
      message: '身份验证服务异常，请稍后重试'
    };
  }
};

// 模拟支付页面（仅开发环境）
exports.mockPayment = async (req, res) => {
  if (config.server.env !== 'development') {
    return res.status(404).json({ message: 'Not found' });
  }
  
  const { orderId, amount, method } = req.query;
  
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
      <title>模拟支付</title>
      <meta charset="utf-8">
      <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .payment-info { background: #f5f5f5; padding: 20px; border-radius: 8px; margin: 20px 0; }
        button { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; }
        .success { background: #4CAF50; color: white; }
        .cancel { background: #f44336; color: white; }
      </style>
    </head>
    <body>
      <h2>模拟支付页面</h2>
      <div class="payment-info">
        <p>订单号: ${orderId}</p>
        <p>支付方式: ${method === 'alipay' ? '支付宝' : '微信支付'}</p>
        <p>支付金额: ¥${amount}</p>
      </div>
      <button class="success" onclick="window.close()">确认支付</button>
      <button class="cancel" onclick="window.close()">取消支付</button>
      <script>
        // 3秒后自动关闭窗口（模拟支付成功）
        setTimeout(() => {
          window.close();
        }, 3000);
      </script>
    </body>
    </html>
  `);
};
