const express = require('express');
const router = express.Router();
const verificationController = require('../controllers/verificationController');
const { auth } = require('../middlewares/auth');

// 获取认证状态
router.get('/status', auth, verificationController.getVerificationStatus);

// 一级认证相关路由
router.post('/level1/payment', auth, verificationController.createLevel1Payment);
router.get('/level1/payment-status/:orderId', auth, verificationController.checkLevel1PaymentStatus);

// 二级认证相关路由（二要素验证）
router.post('/level2/verify', auth, verificationController.level2Verification);
router.get('/level2/status', auth, verificationController.getLevel2Status);

// 模拟支付页面（仅开发环境）
router.get('/mock-payment', verificationController.mockPayment);

// 获取一级认证缓存状态（仅开发环境）
router.get('/level1/cache-stats', auth, (req, res) => {
  const config = require('../config/config');
  if (config.server.env !== 'development') {
    return res.status(404).json({ message: 'Not found' });
  }

  const level1Cache = require('../utils/level1Cache');
  const stats = level1Cache.getCacheStats();

  res.json({
    success: true,
    cacheStats: stats,
    userStatus: level1Cache.getLevel1Info(req.user.id)
  });
});

module.exports = router;
