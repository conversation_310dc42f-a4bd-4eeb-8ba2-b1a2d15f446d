const axios = require('axios');
const aliyunConfig = require('./config/aliyun');

async function testAliyunGETAPI() {
  try {
    console.log('=== 阿里云二要素验证API测试 (GET方法) ===\n');
    
    // 获取配置
    const apiConfig = aliyunConfig.getIdVerificationConfig();
    
    console.log('API配置信息:');
    console.log('- AppCode:', apiConfig.appCode.substring(0, 8) + '...');
    console.log('- API URL:', apiConfig.apiUrl);
    console.log('- 请求方法:', 'GET');
    console.log('- 超时时间:', apiConfig.timeout + 'ms\n');
    
    // 测试数据（使用正确格式的测试身份证号）
    const testData = {
      name: '张三',
      idcard: '110101199003073333'  // 正确格式的测试身份证号
    };
    
    console.log('测试数据:');
    console.log('- 姓名:', testData.name);
    console.log('- 身份证:', testData.idcard.replace(/(.{6}).*(.{4})/, '$1****$2'));
    
    // 构建GET请求URL
    const requestUrl = `${apiConfig.apiUrl}?idCard=${testData.idcard}&name=${encodeURIComponent(testData.name)}`;
    console.log('\n请求URL:', requestUrl.replace(testData.idcard, testData.idcard.replace(/(.{6}).*(.{4})/, '$1****$2')));
    
    const startTime = Date.now();
    
    try {
      const response = await axios.get(requestUrl, {
        headers: {
          'Authorization': `APPCODE ${apiConfig.appCode}`
        },
        timeout: apiConfig.timeout
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      console.log('\n✓ API调用成功');
      console.log('- 响应时间:', responseTime + 'ms');
      console.log('- HTTP状态:', response.status);
      console.log('- 响应数据:', JSON.stringify(response.data, null, 2));
      
      // 解析响应结果
      if (response.data) {
        console.log('\n响应解析:');

        // 根据实际响应格式解析
        if (response.data.status !== undefined) {
          // 这种API返回status字段
          console.log('- 状态码:', response.data.status);
          console.log('- 消息:', response.data.msg);
          console.log('- 追踪ID:', response.data.traceId);

          if (response.data.status === '200') {
            console.log('✓ 身份验证成功');
          } else if (response.data.status === '205') {
            console.log('✗ 身份证格式不正确');
          } else if (response.data.status === '201') {
            console.log('✗ 身份验证失败（姓名与身份证不匹配）');
          } else {
            console.log('✗ 身份验证失败');
          }
        } else if (response.data.code !== undefined) {
          console.log('- 业务代码:', response.data.code);
          console.log('- 业务消息:', response.data.message || response.data.msg);

          if (response.data.data) {
            const data = response.data.data;
            console.log('- 验证结果:', data.result === '1' ? '通过' : '不通过');
            console.log('- 结果描述:', data.desc);
          }

          if (response.data.code === 200) {
            console.log('✓ 身份验证API调用成功');
          } else {
            console.log('✗ 身份验证API调用失败');
          }
        } else if (response.data.result !== undefined) {
          // 直接返回结果的格式
          console.log('- 验证结果:', response.data.result === '1' ? '通过' : '不通过');
          console.log('- 结果描述:', response.data.desc || response.data.message);
          console.log('✓ 身份验证API调用成功');
        } else {
          console.log('- 未知响应格式，需要进一步分析');
        }
      }
      
    } catch (error) {
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      console.log('\n✗ API调用失败');
      console.log('- 响应时间:', responseTime + 'ms');
      
      if (error.response) {
        console.log('- HTTP状态:', error.response.status);
        console.log('- 错误响应:', JSON.stringify(error.response.data, null, 2));
        
        // 分析错误
        if (error.response.status === 400) {
          console.log('分析: 请求参数错误，请检查姓名和身份证号格式');
        } else if (error.response.status === 401) {
          console.log('分析: 认证失败，请检查AppCode是否正确');
        } else if (error.response.status === 403) {
          const errorMsg = error.response.headers['x-ca-error-message'] || '';
          if (errorMsg.includes('Quota Exhausted')) {
            console.log('分析: 套餐包次数用完，请续购套餐');
          } else if (errorMsg.includes('Unauthorized')) {
            console.log('分析: 服务未被授权，请检查API权限配置');
          } else {
            console.log('分析: 权限不足，请检查API权限配置');
          }
        } else if (error.response.status === 404) {
          console.log('分析: API路径不存在，请检查URL是否正确');
        } else if (error.response.status === 429) {
          console.log('分析: 请求频率过高，请稍后重试');
        } else if (error.response.status >= 500) {
          console.log('分析: 服务器错误，请稍后重试');
        }
        
        // 显示错误头信息
        if (error.response.headers['x-ca-error-message']) {
          console.log('- 错误详情:', error.response.headers['x-ca-error-message']);
        }
      } else if (error.code === 'ECONNABORTED') {
        console.log('- 错误类型: 请求超时');
        console.log('分析: 网络连接超时，请检查网络连接');
      } else {
        console.log('- 错误类型:', error.code || error.name);
        console.log('- 错误信息:', error.message);
      }
    }
    
    console.log('\n=== 测试完成 ===');
    
  } catch (error) {
    console.error('测试过程中发生错误:', error);
  }
}

// 运行测试
testAliyunGETAPI().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('测试失败:', error);
  process.exit(1);
});
