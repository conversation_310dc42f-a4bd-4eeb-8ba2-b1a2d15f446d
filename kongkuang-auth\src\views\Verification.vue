<template>
  <div class="verification-container">
    <h1 class="page-title">实名认证</h1>

    <!-- 认证状态概览 -->
    <div class="verification-overview">
      <n-card class="level-card" :class="{ 'completed': level1Completed }">
        <div class="level-header">
          <n-icon size="24" :color="level1Completed ? '#18a058' : '#2080f0'">
            <shield-checkmark-outline v-if="level1Completed" />
            <shield-outline v-else />
          </n-icon>
          <h3>一级认证</h3>
          <n-tag v-if="level1Completed" type="success" size="small">已完成</n-tag>
          <n-tag v-else type="info" size="small">未完成</n-tag>
        </div>
        <p class="level-description">通过支付宝或微信实名验证，快速完成身份认证</p>
        <div class="level-features">
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>基础身份验证</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>访问基础功能</span>
          </div>
        </div>
        <div class="level-actions">
          <n-button
            v-if="!level1Completed"
            type="primary"
            @click="startLevel1Verification"
            :loading="level1Loading"
          >
            开始认证
          </n-button>
          <n-button v-else disabled>已完成</n-button>
        </div>
      </n-card>

      <n-card class="level-card" :class="{ 'completed': level2Completed, 'disabled': !level1Completed }">
        <div class="level-header">
          <n-icon size="24" :color="level2Completed ? '#18a058' : (level1Completed ? '#2080f0' : '#d0d0d0')">
            <shield-checkmark-outline v-if="level2Completed" />
            <shield-outline v-else />
          </n-icon>
          <h3>二级认证</h3>
          <n-tag v-if="level2Completed" type="success" size="small">已完成</n-tag>
          <n-tag v-else-if="level1Completed" type="warning" size="small">可进行</n-tag>
          <n-tag v-else type="default" size="small">需完成一级认证</n-tag>
        </div>
        <p class="level-description">通过人脸识别进行高级身份验证，免费使用</p>
        <div class="level-features">
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>高级身份验证</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>访问所有功能</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>免费使用</span>
          </div>
        </div>
        <div class="level-actions">
          <n-button
            v-if="!level2Completed && level1Completed"
            type="primary"
            @click="startLevel2Verification"
            :loading="level2Loading"
          >
            开始认证
          </n-button>
          <n-button v-else-if="level2Completed" disabled>已完成</n-button>
          <n-button v-else disabled>需完成一级认证</n-button>
        </div>
      </n-card>
    </div>

    <!-- 一级认证模态框 -->
    <n-modal v-model:show="showLevel1Modal" preset="card" title="一级认证" style="width: 600px; max-width: 90vw;">
      <div class="level1-content">
        <n-alert title="认证说明" type="info" style="margin-bottom: 24px;">
          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。
        </n-alert>

        <div class="payment-options">
          <div
            class="payment-option"
            :class="{ 'selected': selectedPayment === 'alipay' }"
            @click="selectedPayment = 'alipay'"
          >
            <div class="payment-icon">
              <n-icon size="32" color="#1677ff">
                <logo-alipay />
              </n-icon>
            </div>
            <div class="payment-info">
              <h4>支付宝认证</h4>
              <p>通过支付宝实名信息进行验证</p>
              <div class="payment-price">
                <span class="price">¥1.2</span>
                <span class="original-price">¥2.0</span>
              </div>
            </div>
            <div class="payment-badge">
              <n-tag type="success" size="small">推荐</n-tag>
            </div>
          </div>

          <div
            class="payment-option"
            :class="{ 'selected': selectedPayment === 'wechat' }"
            @click="selectedPayment = 'wechat'"
          >
            <div class="payment-icon">
              <n-icon size="32" color="#07c160">
                <logo-wechat />
              </n-icon>
            </div>
            <div class="payment-info">
              <h4>微信认证</h4>
              <p>通过微信实名信息进行验证</p>
              <div class="payment-price">
                <span class="price">¥1.5</span>
              </div>
            </div>
          </div>
        </div>

        <div class="verification-process">
          <h4>认证流程：</h4>
          <div class="process-steps">
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><card-outline /></n-icon>
              <span>选择支付方式</span>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><arrow-forward /></n-icon>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><wallet-outline /></n-icon>
              <span>完成支付</span>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><arrow-forward /></n-icon>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><checkmark-circle-outline /></n-icon>
              <span>认证完成</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="showLevel1Modal = false">取消</n-button>
          <n-button
            type="primary"
            @click="proceedLevel1Payment"
            :disabled="!selectedPayment"
            :loading="level1Loading"
          >
            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 二级认证模态框 -->
    <n-modal v-model:show="showLevel2Modal" preset="card" title="二级认证" style="width: 600px; max-width: 90vw;">
      <div class="level2-content">
        <n-alert title="人脸识别认证" type="info" style="margin-bottom: 24px;">
          二级认证完全免费，通过人脸识别技术验证您的身份信息。
        </n-alert>

        <div class="face-verification-area" v-if="!faceVerificationStarted">
          <div class="face-preview">
            <n-avatar size="120" :src="userStore.user?.avatar || '/default-avatar.png'" />
          </div>
          <div class="face-instructions">
            <h4>人脸识别说明：</h4>
            <ul>
              <li>请确保光线充足，面部清晰可见</li>
              <li>请正对摄像头，保持面部居中</li>
              <li>请勿佩戴帽子、墨镜等遮挡物</li>
              <li>整个过程大约需要3-5秒</li>
            </ul>
          </div>
        </div>

        <div class="face-verification-camera" v-else>
          <div class="camera-container">
            <video ref="videoRef" autoplay muted playsinline></video>
            <canvas ref="canvasRef" style="display: none;"></canvas>
            <div class="camera-overlay">
              <div class="face-frame"></div>
            </div>
          </div>
          <div class="verification-status">
            <n-spin v-if="faceVerifying" size="small">
              <template #description>正在进行人脸识别...</template>
            </n-spin>
            <div v-else-if="faceVerificationResult" class="verification-result">
              <n-icon size="24" :color="faceVerificationResult.success ? '#18a058' : '#d03050'">
                <checkmark-circle-outline v-if="faceVerificationResult.success" />
                <close-circle-outline v-else />
              </n-icon>
              <span>{{ faceVerificationResult.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="showLevel2Modal = false" :disabled="faceVerifying">取消</n-button>
          <n-button
            v-if="!faceVerificationStarted"
            type="primary"
            @click="startFaceVerification"
            :loading="level2Loading"
          >
            开始人脸识别
          </n-button>
          <n-button
            v-else-if="faceVerificationResult?.success"
            type="primary"
            @click="completeFaceVerification"
          >
            完成认证
          </n-button>
          <n-button
            v-else-if="faceVerificationResult && !faceVerificationResult.success"
            type="primary"
            @click="retryFaceVerification"
          >
            重新识别
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  NCard,
  NButton,
  NAlert,
  NIcon,
  NAvatar,
  NModal,
  NTag,
  NSpin,
  useMessage
} from 'naive-ui';
import {
  ShieldOutline,
  ShieldCheckmarkOutline,
  Checkmark,
  LogoAlipay,
  LogoWechat,
  CardOutline,
  WalletOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ArrowForward
} from '@vicons/ionicons5';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';

const router = useRouter();
const message = useMessage();
const userStore = useUserStore();
const apiClient = getApiClient();

// 认证状态
const level1Completed = ref(false);
const level2Completed = ref(false);
const level1Loading = ref(false);
const level2Loading = ref(false);

// 模态框状态
const showLevel1Modal = ref(false);
const showLevel2Modal = ref(false);

// 一级认证相关
const selectedPayment = ref('');

// 二级认证相关
const faceVerificationStarted = ref(false);
const faceVerifying = ref(false);
const faceVerificationResult = ref(null);
const videoRef = ref(null);
const canvasRef = ref(null);
const mediaStream = ref(null);

// 阿里云人脸识别配置
const FACE_API_CONFIG = {
  appKey: '*********',
  appSecret: 'l77vwNhbVtax65GRQz9NnnOxxREz5AZS',
  appCode: 'ac2c8231f12445928b757dd27e67dbce',
  apiUrl: 'https://face.market.alicloudapi.com/face/human_face_compare'
};

// 检查认证状态
const checkVerificationStatus = async () => {
  try {
    const response = await apiClient.get('/users/verification-status');
    level1Completed.value = response.data.level1Completed || false;
    level2Completed.value = response.data.level2Completed || false;
  } catch (error) {
    console.error('获取认证状态失败:', error);
  }
};

// 开始一级认证
const startLevel1Verification = () => {
  showLevel1Modal.value = true;
  selectedPayment.value = 'alipay'; // 默认选择支付宝
};

// 处理一级认证支付
const proceedLevel1Payment = async () => {
  if (!selectedPayment.value) {
    message.warning('请选择支付方式');
    return;
  }

  level1Loading.value = true;

  try {
    // 调用支付API
    const response = await apiClient.post('/verification/level1/payment', {
      paymentMethod: selectedPayment.value,
      amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5
    });

    if (response.data.success) {
      // 跳转到支付页面或处理支付
      window.open(response.data.paymentUrl, '_blank');
      message.success('支付链接已打开，请完成支付');
      showLevel1Modal.value = false;

      // 轮询检查支付状态
      checkPaymentStatus(response.data.orderId);
    }
  } catch (error) {
    message.error(error.response?.data?.message || '发起支付失败');
  } finally {
    level1Loading.value = false;
  }
};

// 检查支付状态
const checkPaymentStatus = async (orderId) => {
  const maxAttempts = 30; // 最多检查30次，每次间隔2秒
  let attempts = 0;

  const checkStatus = async () => {
    try {
      const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);

      if (response.data.status === 'completed') {
        level1Completed.value = true;
        message.success('一级认证完成！');
        return;
      } else if (response.data.status === 'failed') {
        message.error('支付失败，请重试');
        return;
      }

      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(checkStatus, 2000);
      }
    } catch (error) {
      console.error('检查支付状态失败:', error);
    }
  };

  checkStatus();
};

// 开始二级认证
const startLevel2Verification = () => {
  if (!level1Completed.value) {
    message.warning('请先完成一级认证');
    return;
  }
  showLevel2Modal.value = true;
};

// 开始人脸识别
const startFaceVerification = async () => {
  faceVerificationStarted.value = true;
  faceVerifying.value = true;
  faceVerificationResult.value = null;

  try {
    // 获取摄像头权限
    mediaStream.value = await navigator.mediaDevices.getUserMedia({
      video: {
        width: 640,
        height: 480,
        facingMode: 'user'
      }
    });

    if (videoRef.value) {
      videoRef.value.srcObject = mediaStream.value;

      // 等待视频加载
      await new Promise((resolve) => {
        videoRef.value.onloadedmetadata = resolve;
      });

      // 延迟3秒后自动拍照进行识别
      setTimeout(() => {
        captureAndVerifyFace();
      }, 3000);
    }
  } catch (error) {
    console.error('获取摄像头失败:', error);
    message.error('无法访问摄像头，请检查权限设置');
    faceVerifying.value = false;
  }
};

// 拍照并进行人脸识别
const captureAndVerifyFace = async () => {
  if (!videoRef.value || !canvasRef.value) return;

  const canvas = canvasRef.value;
  const video = videoRef.value;
  const context = canvas.getContext('2d');

  // 设置canvas尺寸
  canvas.width = video.videoWidth;
  canvas.height = video.videoHeight;

  // 绘制当前帧到canvas
  context.drawImage(video, 0, 0);

  // 获取base64图片数据
  const imageData = canvas.toDataURL('image/jpeg', 0.8);

  try {
    // 调用阿里云人脸识别API
    const result = await callFaceRecognitionAPI(imageData);

    faceVerifying.value = false;
    faceVerificationResult.value = result;

    if (result.success) {
      message.success('人脸识别成功！');
    } else {
      message.error(result.message || '人脸识别失败，请重试');
    }
  } catch (error) {
    faceVerifying.value = false;
    faceVerificationResult.value = {
      success: false,
      message: '人脸识别服务异常，请稍后重试'
    };
    message.error('人脸识别失败');
  }
};

// 调用阿里云人脸识别API
const callFaceRecognitionAPI = async (imageData) => {
  try {
    // 这里应该调用后端API，由后端调用阿里云服务
    const response = await apiClient.post('/verification/level2/face-recognition', {
      image: imageData,
      userId: userStore.user?.id
    });

    return response.data;
  } catch (error) {
    console.error('人脸识别API调用失败:', error);
    throw error;
  }
};

// 重新进行人脸识别
const retryFaceVerification = () => {
  faceVerificationResult.value = null;
  faceVerifying.value = true;

  setTimeout(() => {
    captureAndVerifyFace();
  }, 1000);
};

// 完成人脸识别认证
const completeFaceVerification = async () => {
  try {
    level2Loading.value = true;

    const response = await apiClient.post('/verification/level2/complete', {
      verificationId: faceVerificationResult.value.verificationId
    });

    if (response.data.success) {
      level2Completed.value = true;
      showLevel2Modal.value = false;
      message.success('二级认证完成！');
    }
  } catch (error) {
    message.error(error.response?.data?.message || '完成认证失败');
  } finally {
    level2Loading.value = false;
  }
};

// 停止摄像头
const stopCamera = () => {
  if (mediaStream.value) {
    mediaStream.value.getTracks().forEach(track => track.stop());
    mediaStream.value = null;
  }
};

// 组件挂载时检查认证状态
onMounted(() => {
  checkVerificationStatus();
});

// 组件卸载时停止摄像头
onUnmounted(() => {
  stopCamera();
});
</script>

<style scoped>
.verification-container {
  padding: 16px;
  min-height: 100%;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: var(--n-text-color-1);
}

.verification-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.level-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.level-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.level-card.completed {
  border: 2px solid #18a058;
  background: linear-gradient(135deg, rgba(24, 160, 88, 0.05) 0%, rgba(24, 160, 88, 0.02) 100%);
}

.level-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.level-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.level-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.level-description {
  color: var(--n-text-color-2);
  margin-bottom: 16px;
  line-height: 1.5;
}

.level-features {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.level-actions {
  display: flex;
  justify-content: flex-end;
}

/* 一级认证模态框样式 */
.level1-content {
  padding: 8px 0;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid var(--n-border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.payment-option:hover {
  border-color: var(--n-primary-color);
  background-color: var(--n-primary-color-hover);
}

.payment-option.selected {
  border-color: var(--n-primary-color);
  background-color: var(--n-primary-color-suppl);
}

.payment-icon {
  margin-right: 16px;
}

.payment-info {
  flex: 1;
}

.payment-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.payment-info p {
  margin: 0 0 8px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.payment-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-error-color);
}

.original-price {
  font-size: 14px;
  color: var(--n-text-color-3);
  text-decoration: line-through;
}

.payment-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.verification-process {
  margin-top: 24px;
}

.verification-process h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--n-text-color-2);
}

/* 二级认证模态框样式 */
.level2-content {
  padding: 8px 0;
}

.face-verification-area {
  text-align: center;
  padding: 24px;
}

.face-preview {
  margin-bottom: 24px;
}

.face-instructions {
  text-align: left;
  max-width: 400px;
  margin: 0 auto;
}

.face-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.face-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.face-instructions li {
  margin-bottom: 8px;
  color: var(--n-text-color-2);
  line-height: 1.5;
}

.face-verification-camera {
  text-align: center;
}

.camera-container {
  position: relative;
  display: inline-block;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.camera-container video {
  width: 480px;
  height: 360px;
  object-fit: cover;
}

.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.face-frame {
  width: 200px;
  height: 240px;
  border: 3px solid #2080f0;
  border-radius: 50%;
  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);
}

.verification-status {
  margin-top: 16px;
  min-height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.verification-result {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-overview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .payment-option {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .payment-icon {
    margin-right: 0;
  }

  .process-steps {
    flex-direction: column;
    gap: 16px;
  }

  .camera-container video {
    width: 320px;
    height: 240px;
  }

  .face-frame {
    width: 150px;
    height: 180px;
  }
}

@media (max-width: 480px) {
  .verification-container {
    padding: 12px;
  }

  .camera-container video {
    width: 280px;
    height: 210px;
  }
}
</style>