<template>
  <div class="verification-container">
    <h1 class="page-title">实名认证</h1>

    <!-- 认证状态概览 -->
    <div class="verification-overview">
      <n-card class="level-card primary-card" :class="{ 'completed': level2Completed }">
        <div class="level-header">
          <n-icon size="24" :color="level2Completed ? '#18a058' : '#2080f0'">
            <shield-checkmark-outline v-if="level2Completed" />
            <shield-outline v-else />
          </n-icon>
          <h3>二级认证</h3>
          <n-tag v-if="level2Completed" type="success" size="small">已完成</n-tag>
          <n-tag v-else type="info" size="small">免费认证</n-tag>
        </div>
        <p class="level-description">通过姓名和身份证号进行二要素验证，完全免费</p>
        <div class="level-features">
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>二要素身份验证</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>访问所有功能</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>完全免费</span>
          </div>
        </div>
        <div class="level-actions">
          <n-button
            v-if="!level2Completed"
            type="primary"
            @click="startLevel2Verification"
            :loading="level2Loading"
          >
            开始认证
          </n-button>
          <n-button v-else disabled>已完成</n-button>
        </div>
      </n-card>

      <n-card class="level-card" :class="{ 'completed': level1Completed, 'disabled': !level2Completed }">
        <div class="level-header">
          <n-icon size="24" :color="level1Completed ? '#18a058' : (level2Completed ? '#2080f0' : '#d0d0d0')">
            <shield-checkmark-outline v-if="level1Completed" />
            <shield-outline v-else />
          </n-icon>
          <h3>一级认证</h3>
          <n-tag v-if="level1Completed" type="success" size="small">已完成</n-tag>
          <n-tag v-else-if="level2Completed" type="warning" size="small">可进行</n-tag>
          <n-tag v-else type="default" size="small">需完成二级认证</n-tag>
        </div>
        <p class="level-description">通过支付宝或微信实名验证，获得更高信任度</p>
        <div class="level-features">
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>支付平台验证</span>
          </div>
          <div class="feature-item">
            <n-icon size="16" color="#18a058"><checkmark /></n-icon>
            <span>更高信任等级</span>
          </div>
        </div>
        <div class="level-actions">
          <n-button
            v-if="!level1Completed && level2Completed"
            type="primary"
            @click="startLevel1Verification"
            :loading="level1Loading"
          >
            开始认证
          </n-button>
          <n-button v-else-if="level1Completed" disabled>已完成</n-button>
          <n-button v-else disabled>需完成二级认证</n-button>
        </div>
      </n-card>
    </div>

    <!-- 一级认证模态框 -->
    <n-modal v-model:show="showLevel1Modal" preset="card" title="一级认证" style="width: 600px; max-width: 90vw;">
      <div class="level1-content">
        <n-alert title="认证说明" type="info" style="margin-bottom: 24px;">
          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。
        </n-alert>

        <div class="payment-options">
          <div
            class="payment-option"
            :class="{ 'selected': selectedPayment === 'alipay' }"
            @click="selectedPayment = 'alipay'"
          >
            <div class="payment-icon">
              <n-icon size="32" color="#1677ff">
                <logo-alipay />
              </n-icon>
            </div>
            <div class="payment-info">
              <h4>支付宝认证</h4>
              <p>通过支付宝实名信息进行验证</p>
              <div class="payment-price">
                <span class="price">¥1.2</span>
                <span class="original-price">¥2.0</span>
              </div>
            </div>
            <div class="payment-badge">
              <n-tag type="success" size="small">推荐</n-tag>
            </div>
          </div>

          <div
            class="payment-option"
            :class="{ 'selected': selectedPayment === 'wechat' }"
            @click="selectedPayment = 'wechat'"
          >
            <div class="payment-icon">
              <n-icon size="32" color="#07c160">
                <logo-wechat />
              </n-icon>
            </div>
            <div class="payment-info">
              <h4>微信认证</h4>
              <p>通过微信实名信息进行验证</p>
              <div class="payment-price">
                <span class="price">¥1.5</span>
              </div>
            </div>
          </div>
        </div>

        <div class="verification-process">
          <h4>认证流程：</h4>
          <div class="process-steps">
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><card-outline /></n-icon>
              <span>选择支付方式</span>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><arrow-forward /></n-icon>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><wallet-outline /></n-icon>
              <span>完成支付</span>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><arrow-forward /></n-icon>
            </div>
            <div class="process-step">
              <n-icon size="20" color="#2080f0"><checkmark-circle-outline /></n-icon>
              <span>认证完成</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="showLevel1Modal = false">取消</n-button>
          <n-button
            type="primary"
            @click="proceedLevel1Payment"
            :disabled="!selectedPayment"
            :loading="level1Loading"
          >
            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}
          </n-button>
        </div>
      </template>
    </n-modal>

    <!-- 二级认证模态框 -->
    <n-modal v-model:show="showLevel2Modal" preset="card" title="二级认证" style="width: 600px; max-width: 90vw;">
      <div class="level2-content">
        <n-alert title="二要素身份验证" type="info" style="margin-bottom: 24px;">
          二级认证完全免费，通过姓名和身份证号进行二要素验证。
        </n-alert>

        <n-form
          ref="level2FormRef"
          :model="level2Form"
          :rules="level2Rules"
          label-placement="top"
          size="medium"
        >
          <n-form-item label="真实姓名" path="realName">
            <n-input
              v-model:value="level2Form.realName"
              placeholder="请输入您的真实姓名"
              :disabled="level2Loading"
            >
              <template #prefix>
                <n-icon><person /></n-icon>
              </template>
            </n-input>
          </n-form-item>

          <n-form-item label="身份证号码" path="idNumber">
            <n-input
              v-model:value="level2Form.idNumber"
              placeholder="请输入您的身份证号码"
              :disabled="level2Loading"
              maxlength="18"
            >
              <template #prefix>
                <n-icon><card-outline /></n-icon>
              </template>
            </n-input>
          </n-form-item>
        </n-form>

        <div class="verification-instructions">
          <h4>验证说明：</h4>
          <ul>
            <li>请确保输入的姓名与身份证上的姓名完全一致</li>
            <li>身份证号码必须是18位有效号码</li>
            <li>验证过程通过权威数据源进行核实</li>
            <li>您的个人信息将被严格保密</li>
          </ul>
        </div>

        <div v-if="level2VerificationResult" class="verification-result-display">
          <n-alert
            :title="level2VerificationResult.success ? '验证成功' : '验证失败'"
            :type="level2VerificationResult.success ? 'success' : 'error'"
          >
            {{ level2VerificationResult.message }}
          </n-alert>
        </div>
      </div>

      <template #footer>
        <div class="modal-footer">
          <n-button @click="showLevel2Modal = false" :disabled="level2Loading">取消</n-button>
          <n-button
            v-if="!level2VerificationResult?.success"
            type="primary"
            @click="submitLevel2Verification"
            :loading="level2Loading"
            :disabled="!isLevel2FormValid"
          >
            开始验证
          </n-button>
          <n-button
            v-else
            type="primary"
            @click="completeLevel2Verification"
          >
            完成认证
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  NCard,
  NButton,
  NAlert,
  NIcon,
  NAvatar,
  NModal,
  NTag,
  NSpin,
  NForm,
  NFormItem,
  NInput,
  useMessage
} from 'naive-ui';
import {
  ShieldOutline,
  ShieldCheckmarkOutline,
  Checkmark,
  LogoAlipay,
  LogoWechat,
  CardOutline,
  WalletOutline,
  CheckmarkCircleOutline,
  CloseCircleOutline,
  ArrowForward,
  Person
} from '@vicons/ionicons5';
import { useUserStore } from '../stores/user';
import { getApiClient } from '../utils/api';

const router = useRouter();
const message = useMessage();
const userStore = useUserStore();
const apiClient = getApiClient();

// 认证状态
const level1Completed = ref(false);
const level2Completed = ref(false);
const level1Loading = ref(false);
const level2Loading = ref(false);

// 模态框状态
const showLevel1Modal = ref(false);
const showLevel2Modal = ref(false);

// 一级认证相关
const selectedPayment = ref('');

// 二级认证相关
const level2FormRef = ref(null);
const level2Form = ref({
  realName: '',
  idNumber: ''
});
const level2VerificationResult = ref(null);

// 二级认证表单验证规则
const level2Rules = {
  realName: [
    {
      required: true,
      message: '请输入真实姓名',
      trigger: ['blur', 'input']
    },
    {
      min: 2,
      max: 20,
      message: '姓名长度应在2-20个字符之间',
      trigger: ['blur', 'input']
    }
  ],
  idNumber: [
    {
      required: true,
      message: '请输入身份证号码',
      trigger: ['blur', 'input']
    },
    {
      validator(rule, value) {
        const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        if (!reg.test(value)) {
          return new Error('请输入正确的身份证号码');
        }
        return true;
      },
      trigger: ['blur', 'input']
    }
  ]
};

// 表单验证状态
const isLevel2FormValid = computed(() => {
  return level2Form.value.realName.trim() &&
         level2Form.value.idNumber.trim() &&
         /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(level2Form.value.idNumber);
});

// 检查认证状态
const checkVerificationStatus = async () => {
  try {
    const response = await apiClient.get('/users/verification-status');
    level1Completed.value = response.data.level1Completed || false;
    level2Completed.value = response.data.level2Completed || false;
  } catch (error) {
    console.error('获取认证状态失败:', error);
  }
};

// 开始一级认证
const startLevel1Verification = () => {
  showLevel1Modal.value = true;
  selectedPayment.value = 'alipay'; // 默认选择支付宝
};

// 处理一级认证支付
const proceedLevel1Payment = async () => {
  if (!selectedPayment.value) {
    message.warning('请选择支付方式');
    return;
  }

  level1Loading.value = true;

  try {
    // 调用支付API
    const response = await apiClient.post('/verification/level1/payment', {
      paymentMethod: selectedPayment.value,
      amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5
    });

    if (response.data.success) {
      // 跳转到支付页面或处理支付
      window.open(response.data.paymentUrl, '_blank');
      message.success('支付链接已打开，请完成支付');
      showLevel1Modal.value = false;

      // 轮询检查支付状态
      checkPaymentStatus(response.data.orderId);
    }
  } catch (error) {
    message.error(error.response?.data?.message || '发起支付失败');
  } finally {
    level1Loading.value = false;
  }
};

// 检查支付状态
const checkPaymentStatus = async (orderId) => {
  const maxAttempts = 30; // 最多检查30次，每次间隔2秒
  let attempts = 0;

  const checkStatus = async () => {
    try {
      const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);

      if (response.data.status === 'completed') {
        level1Completed.value = true;
        message.success('一级认证完成！');
        return;
      } else if (response.data.status === 'failed') {
        message.error('支付失败，请重试');
        return;
      }

      attempts++;
      if (attempts < maxAttempts) {
        setTimeout(checkStatus, 2000);
      }
    } catch (error) {
      console.error('检查支付状态失败:', error);
    }
  };

  checkStatus();
};

// 开始二级认证
const startLevel2Verification = () => {
  showLevel2Modal.value = true;
  level2VerificationResult.value = null;
  // 重置表单
  level2Form.value = {
    realName: '',
    idNumber: ''
  };
};

// 提交二级认证
const submitLevel2Verification = async () => {
  try {
    // 验证表单
    await level2FormRef.value?.validate();

    level2Loading.value = true;
    level2VerificationResult.value = null;

    const response = await apiClient.post('/verification/level2/verify', {
      realName: level2Form.value.realName.trim(),
      idNumber: level2Form.value.idNumber.trim()
    });

    if (response.data.success) {
      level2VerificationResult.value = {
        success: true,
        message: response.data.message || '身份验证成功'
      };
      message.success('二级认证成功！');
    } else {
      level2VerificationResult.value = {
        success: false,
        message: response.data.message || '身份验证失败'
      };
    }
  } catch (error) {
    console.error('二级认证失败:', error);
    level2VerificationResult.value = {
      success: false,
      message: error.response?.data?.message || '身份验证失败，请稍后重试'
    };
    message.error(level2VerificationResult.value.message);
  } finally {
    level2Loading.value = false;
  }
};

// 完成二级认证
const completeLevel2Verification = () => {
  level2Completed.value = true;
  showLevel2Modal.value = false;
  message.success('二级认证已完成！');
};

// 组件挂载时检查认证状态
onMounted(() => {
  checkVerificationStatus();
});
</script>

<style scoped>
.verification-container {
  padding: 16px;
  min-height: 100%;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: var(--n-text-color-1);
}

.verification-overview {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.level-card {
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.level-card:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.level-card.completed {
  border: 2px solid #18a058;
  background: linear-gradient(135deg, rgba(24, 160, 88, 0.05) 0%, rgba(24, 160, 88, 0.02) 100%);
}

.level-card.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.level-card.primary-card {
  border: 2px solid #2080f0;
  background: linear-gradient(135deg, rgba(32, 128, 240, 0.05) 0%, rgba(32, 128, 240, 0.02) 100%);
}

.level-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.level-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  flex: 1;
}

.level-description {
  color: var(--n-text-color-2);
  margin-bottom: 16px;
  line-height: 1.5;
}

.level-features {
  margin-bottom: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--n-text-color-2);
}

.level-actions {
  display: flex;
  justify-content: flex-end;
}

/* 一级认证模态框样式 */
.level1-content {
  padding: 8px 0;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 2px solid var(--n-border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.payment-option:hover {
  border-color: var(--n-primary-color);
  background-color: var(--n-primary-color-hover);
}

.payment-option.selected {
  border-color: var(--n-primary-color);
  background-color: var(--n-primary-color-suppl);
}

.payment-icon {
  margin-right: 16px;
}

.payment-info {
  flex: 1;
}

.payment-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.payment-info p {
  margin: 0 0 8px 0;
  color: var(--n-text-color-2);
  font-size: 14px;
}

.payment-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price {
  font-size: 18px;
  font-weight: 600;
  color: var(--n-error-color);
}

.original-price {
  font-size: 14px;
  color: var(--n-text-color-3);
  text-decoration: line-through;
}

.payment-badge {
  position: absolute;
  top: 8px;
  right: 8px;
}

.verification-process {
  margin-top: 24px;
}

.verification-process h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--n-text-color-2);
}

/* 二级认证模态框样式 */
.level2-content {
  padding: 8px 0;
}

.verification-instructions {
  margin-top: 24px;
  padding: 16px;
  background-color: var(--n-color-target);
  border-radius: 8px;
}

.verification-instructions h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--n-text-color-1);
}

.verification-instructions ul {
  margin: 0;
  padding-left: 20px;
}

.verification-instructions li {
  margin-bottom: 8px;
  color: var(--n-text-color-2);
  line-height: 1.5;
}

.verification-result-display {
  margin-top: 16px;
}



.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .verification-overview {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .payment-option {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .payment-icon {
    margin-right: 0;
  }

  .process-steps {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .verification-container {
    padding: 12px;
  }
}
</style>