const { User, Application, Token, VerificationCode } = require('../models');
const bcrypt = require('bcrypt');
const config = require('../config/config');
const { Op } = require('sequelize');
const { getDefaultAvatar } = require('../utils/avatarUtil');
const level1Cache = require('../utils/level1Cache');
const moment = require('moment');

// 设置moment为中文
moment.locale('zh-cn');

// 格式化时间的辅助函数
const formatDateTime = (dateTime) => {
  if (!dateTime) return null;
  return {
    raw: dateTime,
    formatted: moment(dateTime).format('YYYY-MM-DD HH:mm:ss'),
    relative: moment(dateTime).fromNow(),
    timestamp: moment(dateTime).valueOf()
  };
};

// 计算用户资料完整度
const calculateProfileCompleteness = (user) => {
  let completeness = 0;
  let total = 0;

  // 基本信息 (40%)
  const basicFields = [
    { field: 'username', weight: 10 },
    { field: 'email', weight: 15 },
    { field: 'phone', weight: 15 }
  ];

  basicFields.forEach(({ field, weight }) => {
    total += weight;
    if (user[field]) completeness += weight;
  });

  // 验证状态 (30%)
  const verificationFields = [
    { field: 'is_email_verified', weight: 15 },
    { field: 'is_phone_verified', weight: 15 }
  ];

  verificationFields.forEach(({ field, weight }) => {
    total += weight;
    if (user[field]) completeness += weight;
  });

  // 个人资料 (20%)
  const profileFields = [
    { field: 'nickname', weight: 5 },
    { field: 'avatar', weight: 5 },
    { field: 'bio', weight: 5 },
    { field: 'location', weight: 5 }
  ];

  profileFields.forEach(({ field, weight }) => {
    total += weight;
    if (user[field] && user[field] !== 'default-avatar.png') completeness += weight;
  });

  // 安全设置 (10%)
  total += 10;
  if (user.security_mfa_enabled) completeness += 10;

  return {
    percentage: Math.round((completeness / total) * 100),
    completeness,
    total,
    suggestions: getProfileSuggestions(user)
  };
};

// 获取资料完善建议
const getProfileSuggestions = (user) => {
  const suggestions = [];

  if (!user.phone) suggestions.push('添加手机号码');
  if (!user.is_email_verified) suggestions.push('验证邮箱地址');
  if (!user.is_phone_verified && user.phone) suggestions.push('验证手机号码');
  if (!user.nickname || user.nickname === user.username) suggestions.push('设置个性昵称');
  if (!user.bio) suggestions.push('添加个人简介');
  if (!user.location) suggestions.push('设置所在地区');
  if (!user.security_mfa_enabled) suggestions.push('启用双重认证');

  return suggestions;
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'active': '正常',
    'inactive': '停用',
    'pending': '待审核',
    'suspended': '暂停',
    'deleted': '已删除'
  };
  return statusMap[status] || '未知';
};

// 获取状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    'active': 'success',
    'inactive': 'warning',
    'pending': 'info',
    'suspended': 'danger',
    'deleted': 'secondary'
  };
  return colorMap[status] || 'secondary';
};

// 获取活动描述
const getActivityDescription = (type, contactType) => {
  const descriptions = {
    'login': '登录验证',
    'register': '注册验证',
    'reset_password': '重置密码',
    'change_email': '更换邮箱',
    'change_phone': '更换手机',
    'enable_mfa': '启用双重认证',
    'disable_mfa': '关闭双重认证'
  };

  const contactText = contactType === 'email' ? '邮箱' : '短信';
  return `${descriptions[type] || '未知操作'} (${contactText})`;
};

// 获取活动图标
const getActivityIcon = (type) => {
  const iconMap = {
    'login': 'login',
    'register': 'user-plus',
    'reset_password': 'key',
    'change_email': 'mail',
    'change_phone': 'phone',
    'enable_mfa': 'shield-check',
    'disable_mfa': 'shield-x'
  };
  return iconMap[type] || 'activity';
};

// 获取仪表盘数据
exports.getDashboardData = async (req, res) => {
  try {
    const userId = req.user.id;

    // 查找用户
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 获取应用数量
    const appCount = await Application.count({
      where: { user_id: userId }
    });

    // 获取用户的应用列表
    const applications = await Application.findAll({
      where: { user_id: userId },
      attributes: ['id', 'name', 'status']
    });

    const appIds = applications.map(app => app.id);

    // 获取授权用户数量（通过令牌统计不重复的用户）
    let authorizedUserCount = 0;
    if (appIds.length > 0) {
      const uniqueUsers = await Token.findAll({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          expires_at: {
            [Op.gt]: new Date()
          },
          is_revoked: false
        },
        attributes: ['user_id'],
        group: ['user_id']
      });
      authorizedUserCount = uniqueUsers.length;
    }

    // 获取活跃令牌数量
    let activeTokenCount = 0;
    if (appIds.length > 0) {
      activeTokenCount = await Token.count({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          expires_at: {
            [Op.gt]: new Date()
          },
          is_revoked: false
        }
      });
    }

    // 获取今日新增令牌数量
    const todayStart = moment().startOf('day').toDate();
    let todayTokenCount = 0;
    if (appIds.length > 0) {
      todayTokenCount = await Token.count({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          created_at: {
            [Op.gte]: todayStart
          },
          is_revoked: false
        }
      });
    }

    // 获取本周活跃用户数量
    const weekStart = moment().startOf('week').toDate();
    let weeklyActiveUsers = 0;
    if (appIds.length > 0) {
      const weeklyUsers = await Token.findAll({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          last_used_at: {
            [Op.gte]: weekStart
          },
          is_revoked: false
        },
        attributes: ['user_id'],
        group: ['user_id']
      });
      weeklyActiveUsers = weeklyUsers.length;
    }

    // 计算账户安全评分
    let securityScore = 0;

    // 邮箱已验证 +25分
    if (user.is_email_verified) {
      securityScore += 25;
    }

    // 手机已验证 +25分
    if (user.is_phone_verified) {
      securityScore += 25;
    }

    // 密码强度评估 +30分
    // 由于密码已哈希，我们基于用户创建时间和更新时间来推断密码强度
    const passwordAge = moment().diff(moment(user.updated_at), 'days');
    let passwordStrengthScore = 30;
    if (passwordAge > 90) {
      passwordStrengthScore = 20; // 密码超过90天未更新，降低评分
    } else if (passwordAge > 30) {
      passwordStrengthScore = 25; // 密码超过30天未更新，稍微降低评分
    }
    securityScore += passwordStrengthScore;

    // MFA启用 +20分
    if (user.security_mfa_enabled) {
      securityScore += 20;
    }

    // 获取最近的应用（最多5个）
    const recentApps = await Application.findAll({
      where: { user_id: userId },
      order: [['updated_at', 'DESC']],
      limit: 5,
      attributes: [
        'id', 'name', 'description', 'status', 'logo', 'website', 'type', 'created_at', 'updated_at'
      ]
    });

    // 获取最近的登录活动（基于验证码记录作为活动指标）
    const recentActivity = await VerificationCode.findAll({
      where: {
        [Op.or]: [
          { contact: user.email },
          { contact: user.phone }
        ],
        used: true
      },
      order: [['created_at', 'DESC']],
      limit: 10,
      attributes: ['type', 'contact_type', 'created_at', 'ip']
    });

    // 获取应用使用统计（最近7天）
    const sevenDaysAgo = moment().subtract(7, 'days').toDate();
    const appUsageStats = [];

    for (const app of applications) {
      const dailyUsage = await Token.count({
        where: {
          application_id: app.id,
          last_used_at: {
            [Op.gte]: sevenDaysAgo
          },
          is_revoked: false
        }
      });

      appUsageStats.push({
        appId: app.id,
        appName: app.name,
        usageCount: dailyUsage
      });
    }

    // 获取认证状态信息
    const level1Info = level1Cache.getLevel1Info(userId);
    const verificationStatus = {
      level1Completed: !!level1Info,
      level2Completed: user.level2_verified || false,
      level1Info: level1Info,
      level2Info: user.level2_verified ? {
        realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
        verifiedAt: user.level2_verified_at
      } : null
    };

    // 获取系统通知（这里可以是管理员发布的通知，暂时返回示例）
    const systemNotifications = [
      {
        id: 1,
        title: 'OAuth 应用注册开放',
        content: 'KongKuang ID 现已开放 OAuth 应用注册，在"顶部菜单栏-更多"启用开发者选项。',
        type: 'info',
        created_at: new Date(),
        read: false
      }
    ];

    // 返回仪表盘数据
    res.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar || getDefaultAvatar(user.username),
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified,
        nickname: user.nickname || user.username,
        emailVerified: user.is_email_verified,
        phoneVerified: user.is_phone_verified,
        mfaEnabled: user.security_mfa_enabled,

        // 原始时间字段（保持兼容性）
        createdAt: user.createdAt || user.created_at,
        lastLoginAt: user.last_login,
        lastLoginIp: getClientIP(req),

        // 格式化的时间字段
        registrationTime: formatDateTime(user.createdAt || user.created_at),
        lastLoginTime: formatDateTime(user.last_login),
        updatedTime: formatDateTime(user.updatedAt || user.updated_at),

        // 账户状态信息
        accountAge: user.createdAt ? moment().diff(moment(user.createdAt), 'days') : 0,
        isNewUser: user.createdAt ? moment().diff(moment(user.createdAt), 'days') < 7 : false,
        profileCompleteness: calculateProfileCompleteness(user)
      },
      stats: {
        // 基础统计
        appCount,
        authorizedUserCount,
        activeTokenCount,
        todayTokenCount,
        weeklyActiveUsers,

        // 安全评分
        securityScore: Math.round(securityScore),
        passwordStrengthScore,

        // 活动统计
        deviceCount: 1, // 简化版本，假设只有一个设备
        totalLoginCount: recentActivity.length,
        recentActivityCount: recentActivity.length,

        // 时间统计
        accountAge: user.createdAt ? moment().diff(moment(user.createdAt), 'days') : 0,
        daysSinceLastLogin: user.last_login ? moment().diff(moment(user.last_login), 'days') : null,

        // 账户状态
        accountStatus: user.is_active ? 'active' : 'inactive',
        verificationStatus: {
          email: user.is_email_verified,
          phone: user.is_phone_verified,
          mfa: user.security_mfa_enabled
        },

        // 应用统计详情
        appStats: {
          total: appCount,
          active: applications.filter(app => app.status === 'active').length,
          inactive: applications.filter(app => app.status === 'inactive').length,
          pending: applications.filter(app => app.status === 'pending').length
        },

        // 令牌统计详情
        tokenStats: {
          active: activeTokenCount,
          todayNew: todayTokenCount,
          weeklyActive: weeklyActiveUsers,
          totalAuthorizedUsers: authorizedUserCount
        }
      },
      recentApps: recentApps.map(app => ({
        id: app.id,
        name: app.name,
        description: app.description,
        status: app.status,
        logo: app.logo,
        website: app.website,
        type: app.type,
        createdAt: formatDateTime(app.created_at),
        updatedAt: formatDateTime(app.updated_at),
        // 添加状态显示信息
        statusText: getStatusText(app.status),
        statusColor: getStatusColor(app.status),
        // 添加相对时间
        lastUpdated: formatDateTime(app.updated_at)?.relative || '未知'
      })),
      recentActivity: recentActivity.map(activity => ({
        type: activity.type,
        contactType: activity.contact_type,
        timestamp: formatDateTime(activity.created_at),
        ip: activity.ip || '未知',
        // 添加活动描述
        description: getActivityDescription(activity.type, activity.contact_type),
        icon: getActivityIcon(activity.type)
      })),
      appUsageStats,
      systemNotifications,
      verificationStatus,
      lastLoginTime: user.last_login
    });
  } catch (error) {
    console.error('获取仪表盘数据错误:', error);
    res.status(500).json({
      success: false,
      message: '获取仪表盘数据失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取用户统计趋势数据
exports.getUserStatsTrend = async (req, res) => {
  try {
    const userId = req.user.id;
    const { days = 7 } = req.query;

    // 获取用户的应用列表
    const applications = await Application.findAll({
      where: { user_id: userId },
      attributes: ['id']
    });

    const appIds = applications.map(app => app.id);

    if (appIds.length === 0) {
      return res.json({
        success: true,
        data: {
          tokenTrend: [],
          userTrend: [],
          activityTrend: []
        }
      });
    }

    // 生成日期范围
    const dateRange = [];
    for (let i = parseInt(days) - 1; i >= 0; i--) {
      dateRange.push(moment().subtract(i, 'days').format('YYYY-MM-DD'));
    }

    // 获取每日令牌创建趋势
    const tokenTrend = [];
    for (const date of dateRange) {
      const dayStart = moment(date).startOf('day').toDate();
      const dayEnd = moment(date).endOf('day').toDate();

      const count = await Token.count({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          created_at: {
            [Op.between]: [dayStart, dayEnd]
          }
        }
      });

      tokenTrend.push({
        date,
        count
      });
    }

    // 获取每日活跃用户趋势
    const userTrend = [];
    for (const date of dateRange) {
      const dayStart = moment(date).startOf('day').toDate();
      const dayEnd = moment(date).endOf('day').toDate();

      const users = await Token.findAll({
        where: {
          application_id: {
            [Op.in]: appIds
          },
          last_used_at: {
            [Op.between]: [dayStart, dayEnd]
          },
          is_revoked: false
        },
        attributes: ['user_id'],
        group: ['user_id']
      });

      userTrend.push({
        date,
        count: users.length
      });
    }

    res.json({
      success: true,
      data: {
        tokenTrend,
        userTrend,
        dateRange
      }
    });
  } catch (error) {
    console.error('获取统计趋势错误:', error);
    res.status(500).json({
      success: false,
      message: '获取统计趋势失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取应用详细统计
exports.getAppDetailStats = async (req, res) => {
  try {
    const userId = req.user.id;
    const { appId } = req.params;

    // 验证应用所有权
    const application = await Application.findOne({
      where: {
        id: appId,
        user_id: userId
      }
    });

    if (!application) {
      return res.status(404).json({
        success: false,
        message: '应用不存在或您无权访问'
      });
    }

    // 获取应用的令牌统计
    const totalTokens = await Token.count({
      where: { application_id: appId }
    });

    const activeTokens = await Token.count({
      where: {
        application_id: appId,
        expires_at: {
          [Op.gt]: new Date()
        },
        is_revoked: false
      }
    });

    const revokedTokens = await Token.count({
      where: {
        application_id: appId,
        is_revoked: true
      }
    });

    // 获取授权用户数量
    const authorizedUsers = await Token.findAll({
      where: {
        application_id: appId,
        expires_at: {
          [Op.gt]: new Date()
        },
        is_revoked: false
      },
      attributes: ['user_id'],
      group: ['user_id']
    });

    // 获取最近的令牌活动
    const recentTokens = await Token.findAll({
      where: { application_id: appId },
      order: [['created_at', 'DESC']],
      limit: 10,
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'username', 'email']
      }]
    });

    res.json({
      success: true,
      application: {
        id: application.id,
        name: application.name,
        description: application.description,
        status: application.status
      },
      stats: {
        totalTokens,
        activeTokens,
        revokedTokens,
        authorizedUserCount: authorizedUsers.length
      },
      recentTokens: recentTokens.map(token => ({
        id: token.id,
        scope: token.scope,
        createdAt: token.created_at,
        expiresAt: token.expires_at,
        lastUsedAt: token.last_used_at,
        isRevoked: token.is_revoked,
        user: token.user ? {
          id: token.user.id,
          username: token.user.username,
          email: token.user.email
        } : null
      }))
    });
  } catch (error) {
    console.error('获取应用详细统计错误:', error);
    res.status(500).json({
      success: false,
      message: '获取应用详细统计失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取安全建议
exports.getSecurityRecommendations = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const recommendations = [];

    // 检查邮箱验证
    if (!user.is_email_verified) {
      recommendations.push({
        id: 'verify_email',
        title: '验证邮箱地址',
        description: '验证您的邮箱地址以提高账户安全性，并接收重要的安全通知。',
        priority: 'high',
        action: {
          type: 'link',
          url: '/security',
          text: '立即验证'
        }
      });
    }

    // 检查手机验证
    if (!user.is_phone_verified) {
      recommendations.push({
        id: 'verify_phone',
        title: '验证手机号码',
        description: '绑定并验证手机号码，用于双重认证和账户恢复。',
        priority: 'high',
        action: {
          type: 'link',
          url: '/security',
          text: '立即验证'
        }
      });
    }

    // 检查MFA
    if (!user.security_mfa_enabled) {
      recommendations.push({
        id: 'enable_mfa',
        title: '启用双重认证',
        description: '启用双重认证(MFA)为您的账户添加额外的安全层。',
        priority: 'medium',
        action: {
          type: 'link',
          url: '/security',
          text: '启用MFA'
        }
      });
    }

    // 检查密码更新时间
    const passwordAge = moment().diff(moment(user.updated_at), 'days');
    if (passwordAge > 90) {
      recommendations.push({
        id: 'update_password',
        title: '更新密码',
        description: `您的密码已经${passwordAge}天未更新，建议定期更换密码以保持账户安全。`,
        priority: 'medium',
        action: {
          type: 'link',
          url: '/security',
          text: '更改密码'
        }
      });
    }

    // 检查应用权限
    const appCount = await Application.count({
      where: { user_id: userId }
    });

    if (appCount > 0) {
      const activeTokenCount = await Token.count({
        where: {
          user_id: userId,
          expires_at: {
            [Op.gt]: new Date()
          },
          is_revoked: false
        }
      });

      if (activeTokenCount > 10) {
        recommendations.push({
          id: 'review_permissions',
          title: '检查应用权限',
          description: `您当前有${activeTokenCount}个活跃的应用授权，建议定期检查并撤销不需要的权限。`,
          priority: 'low',
          action: {
            type: 'link',
            url: '/applications',
            text: '管理权限'
          }
        });
      }
    }

    res.json({
      success: true,
      recommendations: recommendations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
    });
  } catch (error) {
    console.error('获取安全建议错误:', error);
    res.status(500).json({
      success: false,
      message: '获取安全建议失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 获取快速操作建议
exports.getQuickActions = async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const quickActions = [];

    // 完善个人资料
    const profileCompleteness = calculateProfileCompleteness(user);
    if (profileCompleteness.percentage < 80) {
      quickActions.push({
        id: 'complete_profile',
        title: '完善个人资料',
        description: `您的资料完整度为${profileCompleteness.percentage}%，完善资料获得更好体验`,
        icon: 'user',
        color: 'success',
        action: {
          type: 'link',
          url: '/profile',
          text: '完善资料'
        }
      });
    }

    // 安全设置
    if (!user.is_email_verified || !user.is_phone_verified || !user.security_mfa_enabled) {
      quickActions.push({
        id: 'security_settings',
        title: '完善安全设置',
        description: '提高账户安全性，启用双重认证和验证联系方式',
        icon: 'shield',
        color: 'warning',
        action: {
          type: 'link',
          url: '/security',
          text: '安全设置'
        }
      });
    }



    // 修改密码
    quickActions.push({
      id: 'change_password',
      title: '修改密码',
      description: '定期更换密码，保护账户安全',
      icon: 'key',
      color: 'warning',
      action: {
        type: 'link',
        url: '/security',
        text: '修改密码'
      }
    });

    // 账户设置
    quickActions.push({
      id: 'account_settings',
      title: '账户设置',
      description: '管理您的账户偏好和隐私设置',
      icon: 'settings',
      color: 'info',
      action: {
        type: 'link',
        url: '/settings',
        text: '进入设置'
      }
    });

    // 帮助中心
    quickActions.push({
      id: 'help_center',
      title: '帮助中心',
      description: '查看常见问题和使用指南',
      icon: 'help-circle',
      color: 'secondary',
      action: {
        type: 'link',
        url: '/help',
        text: '获取帮助'
      }
    });

    res.json({
      success: true,
      quickActions: quickActions.slice(0, 6) // 最多显示6个快速操作
    });
  } catch (error) {
    console.error('获取快速操作错误:', error);
    res.status(500).json({
      success: false,
      message: '获取快速操作失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 工具函数：获取客户端真实IP
function getClientIP(req) {
  const forwarded = req.headers['x-forwarded-for'];
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }

  let ip = req.headers['x-real-ip'] ||
           req.connection.remoteAddress ||
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           '127.0.0.1';

  // 将IPv6本地地址转换为更友好的显示
  if (ip === '::1') {
    return '本地访问 (IPv6)';
  } else if (ip === '127.0.0.1' || ip === '::ffff:127.0.0.1') {
    return '本地访问 (IPv4)';
  }

  return ip;
}