{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, createVNode as _createVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, resolveDynamicComponent as _resolveDynamicComponent, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"dashboard-container\"\n};\nconst _hoisted_2 = {\n  class: \"dashboard-content\"\n};\nconst _hoisted_3 = {\n  class: \"welcome-title\"\n};\nconst _hoisted_4 = {\n  class: \"email-item\"\n};\nconst _hoisted_5 = {\n  class: \"side-cards\"\n};\nconst _hoisted_6 = {\n  class: \"security-dashboard\"\n};\nconst _hoisted_7 = {\n  class: \"steering-wheel\"\n};\nconst _hoisted_8 = {\n  viewBox: \"0 0 200 200\",\n  class: \"wheel-svg\"\n};\nconst _hoisted_9 = [\"stroke\"];\nconst _hoisted_10 = [\"stroke\", \"stroke-dasharray\", \"stroke-dashoffset\"];\nconst _hoisted_11 = [\"stroke\"];\nconst _hoisted_12 = [\"fill\"];\nconst _hoisted_13 = [\"stroke\"];\nconst _hoisted_14 = [\"fill\"];\nconst _hoisted_15 = [\"fill\"];\nconst _hoisted_16 = {\n  class: \"security-items\"\n};\nconst _hoisted_17 = {\n  class: \"item-icon\"\n};\nconst _hoisted_18 = {\n  class: \"item-content\"\n};\nconst _hoisted_19 = {\n  class: \"item-name\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"security-suggestion\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NSpin\"], {\n    show: $setup.loading\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"h2\", _hoisted_3, \"你好, \" + _toDisplayString($setup.userStore.user?.username), 1 /* TEXT */), _createVNode($setup[\"NAlert\"], {\n      title: \"通知\",\n      type: \"info\",\n      bordered: true,\n      class: \"info-alert\"\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\" KongKuang ID 现已开放 OAuth 应用注册, 在\\\"顶部菜单栏-更多\\\"启用开发者选项(需要已完成实名认证). 之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序. 我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解. \")])),\n      _: 1 /* STABLE */,\n      __: [1]\n    }), _createVNode($setup[\"NGrid\"], {\n      \"x-gap\": \"16\",\n      \"y-gap\": \"16\",\n      cols: 2,\n      style: {\n        \"flex\": \"1\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NCard\"], {\n          bordered: false,\n          class: \"user-info-panel\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NDescriptions\"], {\n            \"label-placement\": \"top\",\n            column: 2\n          }, {\n            default: _withCtx(() => [_createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"ID\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.id), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"实名状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: $setup.userStore.user?.level2_verified ? 'success' : 'warning',\n                size: \"small\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.level2_verified ? '已实名' : '未实名'), 1 /* TEXT */)]),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"type\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"注册时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.createdAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录时间\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDateTime($setup.userStore.user?.last_login || $setup.userStore.user?.lastLoginAt)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"最后登录 IP\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.userStore.user?.lastLoginIp || '未知'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"用户状态\"\n            }, {\n              default: _withCtx(() => [_createVNode($setup[\"NTag\"], {\n                bordered: false,\n                type: \"success\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"正常\")])),\n                _: 1 /* STABLE */,\n                __: [2]\n              })]),\n              _: 1 /* STABLE */\n            }), _createVNode($setup[\"NDescriptionsItem\"], {\n              label: \"绑定邮箱\",\n              span: 2\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString($setup.userStore.user?.email), 1 /* TEXT */), _createVNode($setup[\"NButton\"], {\n                text: \"\",\n                type: \"primary\",\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [_createTextVNode(\"换绑\")])),\n                _: 1 /* STABLE */,\n                __: [3]\n              })])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), _createVNode($setup[\"NButton\"], {\n            type: \"primary\",\n            ghost: \"\",\n            onClick: _cache[0] || (_cache[0] = $event => _ctx.$router.push('/security')),\n            style: {\n              \"margin-top\": \"16px\"\n            }\n          }, {\n            default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\" 更改密码 \")])),\n            _: 1 /* STABLE */,\n            __: [4]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NGi\"], {\n        span: 1\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NCard\"], {\n          title: \"可使用 KongKuang ID 登录的服务\",\n          bordered: false,\n          class: \"right-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createVNode($setup[\"NList\"], {\n            \"show-divider\": false\n          }, {\n            default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.recentApps, app => {\n              return _openBlock(), _createBlock($setup[\"NListItem\"], {\n                key: app.id,\n                class: \"service-item\"\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(app.name), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1024 /* DYNAMIC_SLOTS */);\n            }), 128 /* KEYED_FRAGMENT */)), !$setup.recentApps || $setup.recentApps.length === 0 ? (_openBlock(), _createBlock($setup[\"NEmpty\"], {\n              key: 0,\n              description: \"暂无服务\"\n            })) : _createCommentVNode(\"v-if\", true)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"]), _createCommentVNode(\" 账户安全指标卡片 \"), _createVNode($setup[\"NCard\"], {\n          title: \"账户安全指标\",\n          bordered: false,\n          class: \"right-card security-card\",\n          \"theme-overrides\": {\n            color: $setup.isDarkMode ? '#2a2a30' : '#ffffff'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createCommentVNode(\" 方向盘样式的安全指标 \"), _createElementVNode(\"div\", _hoisted_7, [(_openBlock(), _createElementBlock(\"svg\", _hoisted_8, [_createCommentVNode(\" 外圈 \"), _createElementVNode(\"circle\", {\n            cx: \"100\",\n            cy: \"100\",\n            r: \"90\",\n            fill: \"none\",\n            stroke: $setup.isDarkMode ? '#404040' : '#e0e0e0',\n            \"stroke-width\": \"4\"\n          }, null, 8 /* PROPS */, _hoisted_9), _createCommentVNode(\" 安全等级弧线 \"), _createElementVNode(\"circle\", {\n            cx: \"100\",\n            cy: \"100\",\n            r: \"90\",\n            fill: \"none\",\n            stroke: $setup.getSecurityColor(),\n            \"stroke-width\": \"8\",\n            \"stroke-linecap\": \"round\",\n            \"stroke-dasharray\": $setup.getSecurityDashArray(),\n            \"stroke-dashoffset\": $setup.getSecurityDashOffset(),\n            transform: \"rotate(-90 100 100)\",\n            class: \"security-arc\"\n          }, null, 8 /* PROPS */, _hoisted_10), _createCommentVNode(\" 方向盘辐条 \"), _createElementVNode(\"g\", {\n            stroke: $setup.isDarkMode ? '#606060' : '#c0c0c0',\n            \"stroke-width\": \"3\"\n          }, _cache[5] || (_cache[5] = [_createElementVNode(\"line\", {\n            x1: \"100\",\n            y1: \"30\",\n            x2: \"100\",\n            y2: \"70\"\n          }, null, -1 /* CACHED */), _createElementVNode(\"line\", {\n            x1: \"100\",\n            y1: \"130\",\n            x2: \"100\",\n            y2: \"170\"\n          }, null, -1 /* CACHED */), _createElementVNode(\"line\", {\n            x1: \"30\",\n            y1: \"100\",\n            x2: \"70\",\n            y2: \"100\"\n          }, null, -1 /* CACHED */), _createElementVNode(\"line\", {\n            x1: \"130\",\n            y1: \"100\",\n            x2: \"170\",\n            y2: \"100\"\n          }, null, -1 /* CACHED */)]), 8 /* PROPS */, _hoisted_11), _createCommentVNode(\" 中心圆 \"), _createElementVNode(\"circle\", {\n            cx: \"100\",\n            cy: \"100\",\n            r: \"25\",\n            fill: $setup.getSecurityColor(),\n            opacity: \"0.2\"\n          }, null, 8 /* PROPS */, _hoisted_12), _createElementVNode(\"circle\", {\n            cx: \"100\",\n            cy: \"100\",\n            r: \"25\",\n            fill: \"none\",\n            stroke: $setup.getSecurityColor(),\n            \"stroke-width\": \"2\"\n          }, null, 8 /* PROPS */, _hoisted_13), _createCommentVNode(\" 安全等级文字 \"), _createElementVNode(\"text\", {\n            x: \"100\",\n            y: \"95\",\n            \"text-anchor\": \"middle\",\n            fill: $setup.getSecurityColor(),\n            \"font-size\": \"12\",\n            \"font-weight\": \"bold\"\n          }, _toDisplayString($setup.getSecurityLevel()), 9 /* TEXT, PROPS */, _hoisted_14), _createElementVNode(\"text\", {\n            x: \"100\",\n            y: \"110\",\n            \"text-anchor\": \"middle\",\n            fill: $setup.getSecurityColor(),\n            \"font-size\": \"10\"\n          }, _toDisplayString($setup.getSecurityScore()) + \"% \", 9 /* TEXT, PROPS */, _hoisted_15)]))]), _createCommentVNode(\" 安全项目列表 \"), _createElementVNode(\"div\", _hoisted_16, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.securityItems, item => {\n            return _openBlock(), _createElementBlock(\"div\", {\n              class: \"security-item\",\n              key: item.key\n            }, [_createElementVNode(\"div\", _hoisted_17, [_createVNode($setup[\"NIcon\"], {\n              size: 16,\n              color: item.status ? '#18a058' : '#d03050'\n            }, {\n              default: _withCtx(() => [(_openBlock(), _createBlock(_resolveDynamicComponent(item.icon)))]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"color\"])]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n              class: _normalizeClass([\"item-status\", {\n                'status-ok': item.status,\n                'status-warning': !item.status\n              }])\n            }, _toDisplayString(item.status ? '已启用' : '未启用'), 3 /* TEXT, CLASS */)])]);\n          }), 128 /* KEYED_FRAGMENT */))]), _createCommentVNode(\" 安全建议 \"), $setup.getSecurityScore() < 100 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode($setup[\"NAlert\"], {\n            type: \"warning\",\n            bordered: false,\n            size: \"small\"\n          }, {\n            icon: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n              default: _withCtx(() => [_createVNode($setup[\"ShieldOutline\"])]),\n              _: 1 /* STABLE */\n            })]),\n            default: _withCtx(() => [_createTextVNode(\" 建议完善\" + _toDisplayString($setup.getSecuritySuggestions().join('、')) + \"以提高账户安全性 \", 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          })])) : _createCommentVNode(\"v-if\", true)])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"theme-overrides\"])])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "viewBox", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "loading", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "userStore", "user", "username", "title", "type", "bordered", "_cache", "cols", "style", "span", "color", "isDarkMode", "column", "label", "id", "level2_verified", "size", "formatDateTime", "createdAt", "last_login", "lastLoginAt", "lastLoginIp", "_hoisted_4", "email", "text", "ghost", "onClick", "$event", "_ctx", "$router", "push", "_hoisted_5", "_Fragment", "_renderList", "recentApps", "app", "_createBlock", "key", "name", "length", "description", "_createCommentVNode", "_hoisted_6", "_hoisted_7", "_hoisted_8", "cx", "cy", "r", "fill", "stroke", "getSecurityColor", "getSecurityDashArray", "getSecurityDashOffset", "transform", "x1", "y1", "x2", "y2", "opacity", "x", "y", "getSecurityLevel", "_hoisted_14", "getSecurityScore", "_hoisted_15", "_hoisted_16", "securityItems", "item", "_hoisted_17", "status", "_resolveDynamicComponent", "icon", "_hoisted_18", "_hoisted_19", "_normalizeClass", "_hoisted_20", "_withCtx", "getSecuritySuggestions", "join"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"2\" style=\"flex: 1;\">\n          <n-gi :span=\"1\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <n-card title=\"可使用 KongKuang ID 登录的服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                  <n-list :show-divider=\"false\">\n                    <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                       {{ app.name }}\n                    </n-list-item>\n                     <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无服务\" />\n                  </n-list>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card title=\"账户安全指标\" :bordered=\"false\" class=\"right-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"security-dashboard\">\n                  <!-- 方向盘样式的安全指标 -->\n                  <div class=\"steering-wheel\">\n                    <svg viewBox=\"0 0 200 200\" class=\"wheel-svg\">\n                      <!-- 外圈 -->\n                      <circle cx=\"100\" cy=\"100\" r=\"90\" fill=\"none\" :stroke=\"isDarkMode ? '#404040' : '#e0e0e0'\" stroke-width=\"4\"/>\n\n                      <!-- 安全等级弧线 -->\n                      <circle\n                        cx=\"100\"\n                        cy=\"100\"\n                        r=\"90\"\n                        fill=\"none\"\n                        :stroke=\"getSecurityColor()\"\n                        stroke-width=\"8\"\n                        stroke-linecap=\"round\"\n                        :stroke-dasharray=\"getSecurityDashArray()\"\n                        :stroke-dashoffset=\"getSecurityDashOffset()\"\n                        transform=\"rotate(-90 100 100)\"\n                        class=\"security-arc\"\n                      />\n\n                      <!-- 方向盘辐条 -->\n                      <g :stroke=\"isDarkMode ? '#606060' : '#c0c0c0'\" stroke-width=\"3\">\n                        <line x1=\"100\" y1=\"30\" x2=\"100\" y2=\"70\" />\n                        <line x1=\"100\" y1=\"130\" x2=\"100\" y2=\"170\" />\n                        <line x1=\"30\" y1=\"100\" x2=\"70\" y2=\"100\" />\n                        <line x1=\"130\" y1=\"100\" x2=\"170\" y2=\"100\" />\n                      </g>\n\n                      <!-- 中心圆 -->\n                      <circle cx=\"100\" cy=\"100\" r=\"25\" :fill=\"getSecurityColor()\" opacity=\"0.2\"/>\n                      <circle cx=\"100\" cy=\"100\" r=\"25\" fill=\"none\" :stroke=\"getSecurityColor()\" stroke-width=\"2\"/>\n\n                      <!-- 安全等级文字 -->\n                      <text x=\"100\" y=\"95\" text-anchor=\"middle\" :fill=\"getSecurityColor()\" font-size=\"12\" font-weight=\"bold\">\n                        {{ getSecurityLevel() }}\n                      </text>\n                      <text x=\"100\" y=\"110\" text-anchor=\"middle\" :fill=\"getSecurityColor()\" font-size=\"10\">\n                        {{ getSecurityScore() }}%\n                      </text>\n                    </svg>\n                  </div>\n\n                  <!-- 安全项目列表 -->\n                  <div class=\"security-items\">\n                    <div class=\"security-item\" v-for=\"item in securityItems\" :key=\"item.key\">\n                      <div class=\"item-icon\">\n                        <n-icon :size=\"16\" :color=\"item.status ? '#18a058' : '#d03050'\">\n                          <component :is=\"item.icon\" />\n                        </n-icon>\n                      </div>\n                      <div class=\"item-content\">\n                        <div class=\"item-name\">{{ item.name }}</div>\n                        <div class=\"item-status\" :class=\"{ 'status-ok': item.status, 'status-warning': !item.status }\">\n                          {{ item.status ? '已启用' : '未启用' }}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 安全建议 -->\n                  <div class=\"security-suggestion\" v-if=\"getSecurityScore() < 100\">\n                    <n-alert type=\"warning\" :bordered=\"false\" size=\"small\">\n                      <template #icon>\n                        <n-icon><shield-outline /></n-icon>\n                      </template>\n                      建议完善{{ getSecuritySuggestions().join('、') }}以提高账户安全性\n                    </n-alert>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  ShieldOutline,\n  LockClosedOutline,\n  MailOutline,\n  PhonePortraitOutline,\n  KeyOutline,\n  CheckmarkCircleOutline,\n  CloseCircleOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全指标数据\nconst securityItems = computed(() => [\n  {\n    key: 'password',\n    name: '登录密码',\n    status: true, // 用户已设置密码\n    icon: KeyOutline\n  },\n  {\n    key: 'email',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    icon: MailOutline\n  },\n  {\n    key: 'phone',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    icon: PhonePortraitOutline\n  },\n  {\n    key: 'realname',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    icon: ShieldOutline\n  },\n  {\n    key: 'mfa',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    icon: LockClosedOutline\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 计算安全分数\nconst getSecurityScore = () => {\n  const totalItems = securityItems.value.length;\n  const completedItems = securityItems.value.filter(item => item.status).length;\n  return Math.round((completedItems / totalItems) * 100);\n};\n\n// 获取安全等级\nconst getSecurityLevel = () => {\n  const score = getSecurityScore();\n  if (score >= 90) return '优秀';\n  if (score >= 70) return '良好';\n  if (score >= 50) return '一般';\n  return '较低';\n};\n\n// 获取安全等级颜色\nconst getSecurityColor = () => {\n  const score = getSecurityScore();\n  if (score >= 90) return '#18a058'; // 绿色\n  if (score >= 70) return '#2080f0'; // 蓝色\n  if (score >= 50) return '#f0a020'; // 橙色\n  return '#d03050'; // 红色\n};\n\n// 计算SVG弧线参数\nconst getSecurityDashArray = () => {\n  const circumference = 2 * Math.PI * 90; // 半径90的圆周长\n  return `${circumference} ${circumference}`;\n};\n\nconst getSecurityDashOffset = () => {\n  const circumference = 2 * Math.PI * 90;\n  const score = getSecurityScore();\n  return circumference - (score / 100) * circumference;\n};\n\n// 获取安全建议\nconst getSecuritySuggestions = () => {\n  return securityItems.value\n    .filter(item => !item.status)\n    .map(item => item.name);\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  margin-top: 16px;\n}\n\n.security-dashboard {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.steering-wheel {\n  width: 120px;\n  height: 120px;\n  position: relative;\n}\n\n.wheel-svg {\n  width: 100%;\n  height: 100%;\n  transform: rotate(0deg);\n}\n\n.security-arc {\n  transition: stroke-dashoffset 0.6s ease-in-out;\n}\n\n.security-items {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.security-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px 8px;\n  border-radius: 4px;\n  background-color: var(--n-color-target);\n  transition: all 0.3s ease;\n}\n\n.security-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.item-icon {\n  flex-shrink: 0;\n}\n\n.item-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.item-name {\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n}\n\n.item-status {\n  font-size: 11px;\n  margin-top: 2px;\n}\n\n.status-ok {\n  color: var(--n-success-color);\n}\n\n.status-warning {\n  color: var(--n-warning-color);\n}\n\n.security-suggestion {\n  width: 100%;\n  margin-top: 8px;\n}\n\n/* 响应式优化 */\n@media (max-width: 768px) {\n  .steering-wheel {\n    width: 100px;\n    height: 100px;\n  }\n\n  .security-item {\n    padding: 4px 6px;\n  }\n\n  .item-name {\n    font-size: 11px;\n  }\n\n  .item-status {\n    font-size: 10px;\n  }\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;EACOA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAe;;EAwCVA,KAAK,EAAC;AAAY;;EAaxBA,KAAK,EAAC;AAAY;;EAYdA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAgB;;EACpBC,OAAO,EAAC,aAAa;EAACD,KAAK,EAAC;;;;;;;;;;EA0C9BA,KAAK,EAAC;AAAgB;;EAElBA,KAAK,EAAC;AAAW;;EAKjBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;;EASvBA,KAAK,EAAC;;;uBAlI3BE,mBAAA,CAiJM,OAjJNC,UAiJM,GAhJJC,YAAA,CA+ISC,MAAA;IA/IAC,IAAI,EAAED,MAAA,CAAAE;EAAO;sBACpB,MA6IM,CA7INC,mBAAA,CA6IM,OA7INC,UA6IM,GA5IJD,mBAAA,CAAiE,MAAjEE,UAAiE,EAAvC,MAAI,GAAAC,gBAAA,CAAGN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEC,QAAQ,kBAEzDV,YAAA,CAIUC,MAAA;MAJDU,KAAK,EAAC,IAAI;MAACC,IAAI,EAAC,MAAM;MAAEC,QAAQ,EAAE,IAAI;MAAEjB,KAAK,EAAC;;wBAAa,MAIpEkB,MAAA,QAAAA,MAAA,O,iBAJoE,kLAIpE,E;;;QAEAd,YAAA,CAmISC,MAAA;MAnID,OAAK,EAAC,IAAI;MAAC,OAAK,EAAC,IAAI;MAAEc,IAAI,EAAE,CAAC;MAAEC,KAAgB,EAAhB;QAAA;MAAA;;wBACtC,MAyCO,CAzCPhB,YAAA,CAyCOC,MAAA;QAzCAgB,IAAI,EAAE;MAAC;0BACZ,MAuCS,CAvCTjB,YAAA,CAuCSC,MAAA;UAvCAY,QAAQ,EAAE,KAAK;UAAEjB,KAAK,EAAC,iBAAiB;UAAE,iBAAe;YAAAsB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BACrF,MAkCiB,CAlCjBnB,YAAA,CAkCiBC,MAAA;YAjCf,iBAAe,EAAC,KAAK;YACpBmB,MAAM,EAAE;;8BAET,MAEsB,CAFtBpB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAI;gCAC7B,MAAwB,C,kCAArBpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEa,EAAE,iB;;gBAEvBtB,YAAA,CAQsBC,MAAA;cARDoB,KAAK,EAAC;YAAM;gCAC/B,MAMQ,CANRrB,YAAA,CAMQC,MAAA;gBALLY,QAAQ,EAAE,KAAK;gBACfD,IAAI,EAAEX,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe;gBACtCC,IAAI,EAAC;;kCAEL,MAAqD,C,kCAAlDvB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEc,eAAe,iC;;;;gBAGtCvB,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC/B,MAA+C,C,kCAA5CpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEiB,SAAS,kB;;gBAE7C1B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAQ;gCACjC,MAA+E,C,kCAA5EpB,MAAA,CAAAwB,cAAc,CAACxB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEkB,UAAU,IAAI1B,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEmB,WAAW,kB;;gBAE7E5B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAS;gCAClC,MAAyC,C,kCAAtCpB,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEoB,WAAW,yB;;gBAEhC7B,YAAA,CAEsBC,MAAA;cAFDoB,KAAK,EAAC;YAAM;gCAC9B,MAA+D,CAA/DrB,YAAA,CAA+DC,MAAA;gBAAvDY,QAAQ,EAAE,KAAK;gBAAED,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;gBAEzDd,YAAA,CAKqBC,MAAA;cALAoB,KAAK,EAAC,MAAM;cAAEJ,IAAI,EAAE;;gCACxC,MAGE,CAHFb,mBAAA,CAGE,OAHF0B,UAGE,GAFA1B,mBAAA,CAAwC,cAAAG,gBAAA,CAA/BN,MAAA,CAAAO,SAAS,CAACC,IAAI,EAAEsB,KAAK,kBAC7B/B,YAAA,CAAwDC,MAAA;gBAA9C+B,IAAI,EAAJ,EAAI;gBAACpB,IAAI,EAAC,SAAS;gBAACY,IAAI,EAAC;;kCAAQ,MAAEV,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;cAInDd,YAAA,CAEUC,MAAA;YAFAW,IAAI,EAAC,SAAS;YAACqB,KAAK,EAAL,EAAK;YAAEC,OAAK,EAAApB,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAEC,IAAA,CAAAC,OAAO,CAACC,IAAI;YAAetB,KAAyB,EAAzB;cAAA;YAAA;;8BAA0B,MAE7FF,MAAA,QAAAA,MAAA,O,iBAF6F,QAE7F,E;;;;;;;UAIJd,YAAA,CAsFOC,MAAA;QAtFAgB,IAAI,EAAE;MAAC;0BACZ,MAoFM,CApFNb,mBAAA,CAoFM,OApFNmC,UAoFM,GAnFJvC,YAAA,CAOSC,MAAA;UAPDU,KAAK,EAAC,wBAAwB;UAAEE,QAAQ,EAAE,KAAK;UAAEjB,KAAK,EAAC,YAAY;UAAE,iBAAe;YAAAsB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7G,MAKS,CALTnB,YAAA,CAKSC,MAAA;YALA,cAAY,EAAE;UAAK;8BACb,MAAyB,E,kBAAtCH,mBAAA,CAEc0C,SAAA,QAAAC,WAAA,CAFaxC,MAAA,CAAAyC,UAAU,EAAjBC,GAAG;mCAAvBC,YAAA,CAEc3C,MAAA;gBAF0B4C,GAAG,EAAEF,GAAG,CAACrB,EAAE;gBAAE1B,KAAK,EAAC;;kCACxD,MAAc,C,kCAAX+C,GAAG,CAACG,IAAI,iB;;;6CAEG7C,MAAA,CAAAyC,UAAU,IAAIzC,MAAA,CAAAyC,UAAU,CAACK,MAAM,U,cAA/CH,YAAA,CAA4E3C,MAAA;;cAArB+C,WAAW,EAAC;;;;;gDAI1EC,mBAAA,cAAiB,EACjBjD,YAAA,CAwESC,MAAA;UAxEDU,KAAK,EAAC,QAAQ;UAAEE,QAAQ,EAAE,KAAK;UAAEjB,KAAK,EAAC,0BAA0B;UAAE,iBAAe;YAAAsB,KAAA,EAAWjB,MAAA,CAAAkB,UAAU;UAAA;;4BAC7G,MAsEM,CAtENf,mBAAA,CAsEM,OAtEN8C,UAsEM,GArEJD,mBAAA,gBAAmB,EACnB7C,mBAAA,CAwCM,OAxCN+C,UAwCM,I,cAvCJrD,mBAAA,CAsCM,OAtCNsD,UAsCM,GArCJH,mBAAA,QAAW,EACX7C,mBAAA,CAA4G;YAApGiD,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,CAAC,EAAC,IAAI;YAACC,IAAI,EAAC,MAAM;YAAEC,MAAM,EAAExD,MAAA,CAAAkB,UAAU;YAA0B,cAAY,EAAC;+CAEvG8B,mBAAA,YAAe,EACf7C,mBAAA,CAYE;YAXAiD,EAAE,EAAC,KAAK;YACRC,EAAE,EAAC,KAAK;YACRC,CAAC,EAAC,IAAI;YACNC,IAAI,EAAC,MAAM;YACVC,MAAM,EAAExD,MAAA,CAAAyD,gBAAgB;YACzB,cAAY,EAAC,GAAG;YAChB,gBAAc,EAAC,OAAO;YACrB,kBAAgB,EAAEzD,MAAA,CAAA0D,oBAAoB;YACtC,mBAAiB,EAAE1D,MAAA,CAAA2D,qBAAqB;YACzCC,SAAS,EAAC,qBAAqB;YAC/BjE,KAAK,EAAC;gDAGRqD,mBAAA,WAAc,EACd7C,mBAAA,CAKI;YALAqD,MAAM,EAAExD,MAAA,CAAAkB,UAAU;YAA0B,cAAY,EAAC;wCAC3Df,mBAAA,CAA0C;YAApC0D,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC;qCACnC7D,mBAAA,CAA4C;YAAtC0D,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC;qCACpC7D,mBAAA,CAA0C;YAApC0D,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,IAAI;YAACC,EAAE,EAAC;qCAClC7D,mBAAA,CAA4C;YAAtC0D,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC;oEAGtChB,mBAAA,SAAY,EACZ7C,mBAAA,CAA2E;YAAnEiD,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,CAAC,EAAC,IAAI;YAAEC,IAAI,EAAEvD,MAAA,CAAAyD,gBAAgB;YAAIQ,OAAO,EAAC;gDACpE9D,mBAAA,CAA4F;YAApFiD,EAAE,EAAC,KAAK;YAACC,EAAE,EAAC,KAAK;YAACC,CAAC,EAAC,IAAI;YAACC,IAAI,EAAC,MAAM;YAAEC,MAAM,EAAExD,MAAA,CAAAyD,gBAAgB;YAAI,cAAY,EAAC;gDAEvFT,mBAAA,YAAe,EACf7C,mBAAA,CAEO;YAFD+D,CAAC,EAAC,KAAK;YAACC,CAAC,EAAC,IAAI;YAAC,aAAW,EAAC,QAAQ;YAAEZ,IAAI,EAAEvD,MAAA,CAAAyD,gBAAgB;YAAI,WAAS,EAAC,IAAI;YAAC,aAAW,EAAC;8BAC3FzD,MAAA,CAAAoE,gBAAgB,0BAAAC,WAAA,GAErBlE,mBAAA,CAEO;YAFD+D,CAAC,EAAC,KAAK;YAACC,CAAC,EAAC,KAAK;YAAC,aAAW,EAAC,QAAQ;YAAEZ,IAAI,EAAEvD,MAAA,CAAAyD,gBAAgB;YAAI,WAAS,EAAC;8BAC3EzD,MAAA,CAAAsE,gBAAgB,MAAK,IAC1B,uBAAAC,WAAA,E,MAIJvB,mBAAA,YAAe,EACf7C,mBAAA,CAcM,OAdNqE,WAcM,I,kBAbJ3E,mBAAA,CAYM0C,SAAA,QAAAC,WAAA,CAZoCxC,MAAA,CAAAyE,aAAa,EAArBC,IAAI;iCAAtC7E,mBAAA,CAYM;cAZDF,KAAK,EAAC,eAAe;cAAgCiD,GAAG,EAAE8B,IAAI,CAAC9B;gBAClEzC,mBAAA,CAIM,OAJNwE,WAIM,GAHJ5E,YAAA,CAESC,MAAA;cAFAuB,IAAI,EAAE,EAAE;cAAGN,KAAK,EAAEyD,IAAI,CAACE,MAAM;;gCACpC,MAA6B,E,cAA7BjC,YAAA,CAA6BkC,wBAAA,CAAbH,IAAI,CAACI,IAAI,I;;8DAG7B3E,mBAAA,CAKM,OALN4E,WAKM,GAJJ5E,mBAAA,CAA4C,OAA5C6E,WAA4C,EAAA1E,gBAAA,CAAlBoE,IAAI,CAAC7B,IAAI,kBACnC1C,mBAAA,CAEM;cAFDR,KAAK,EAAAsF,eAAA,EAAC,aAAa;gBAAA,aAAwBP,IAAI,CAACE,MAAM;gBAAA,mBAAqBF,IAAI,CAACE;cAAM;gCACtFF,IAAI,CAACE,MAAM,wC;4CAMtB5B,mBAAA,UAAa,EAC0BhD,MAAA,CAAAsE,gBAAgB,Y,cAAvDzE,mBAAA,CAOM,OAPNqF,WAOM,GANJnF,YAAA,CAKUC,MAAA;YALDW,IAAI,EAAC,SAAS;YAAEC,QAAQ,EAAE,KAAK;YAAEW,IAAI,EAAC;;YAClCuD,IAAI,EAAAK,QAAA,CACb,MAAmC,CAAnCpF,YAAA,CAAmCC,MAAA;gCAA3B,MAAkB,CAAlBD,YAAA,CAAkBC,MAAA,mB;;;8BACjB,MACP,C,iBADO,OACP,GAAAM,gBAAA,CAAGN,MAAA,CAAAoF,sBAAsB,GAAGC,IAAI,SAAQ,WAC9C,gB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}