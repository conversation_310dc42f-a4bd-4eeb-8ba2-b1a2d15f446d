const { sequelize } = require('./config/db');

async function migrateUserTable() {
  try {
    console.log('正在连接数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    console.log('正在添加认证相关字段...');
    
    // 添加二级认证相关字段，移除一级认证字段
    const alterQueries = [
      // 添加二级认证字段（如果不存在）
      `ALTER TABLE users ADD COLUMN real_name VARCHAR(50) NULL COMMENT '真实姓名（二级认证）'`,
      `ALTER TABLE users ADD COLUMN id_number VARCHAR(18) NULL COMMENT '身份证号码（二级认证）'`,
      `ALTER TABLE users ADD COLUMN level2_verified BOOLEAN DEFAULT FALSE COMMENT '二级认证状态'`,
      `ALTER TABLE users ADD COLUMN level2_verified_at DATETIME NULL COMMENT '二级认证完成时间'`,
      `ALTER TABLE users ADD COLUMN level2_verification_data TEXT NULL COMMENT '二级认证详细数据（JSON格式）'`
    ];

    // 移除一级认证字段（如果存在）
    const dropQueries = [
      `ALTER TABLE users DROP COLUMN IF EXISTS level1_verified`,
      `ALTER TABLE users DROP COLUMN IF EXISTS level1_verified_at`
    ];
    
    // 执行添加字段的查询
    for (const query of alterQueries) {
      try {
        await sequelize.query(query);
        console.log('添加字段成功:', query.split(' ').slice(0, 6).join(' ') + '...');
      } catch (error) {
        if (error.original && error.original.code === 'ER_DUP_FIELDNAME') {
          console.log('字段已存在，跳过:', query.split(' ').slice(0, 6).join(' ') + '...');
        } else {
          console.error('添加字段失败:', query);
          console.error('错误:', error.message);
        }
      }
    }

    // 执行删除字段的查询
    for (const query of dropQueries) {
      try {
        await sequelize.query(query);
        console.log('删除字段成功:', query.split(' ').slice(0, 6).join(' ') + '...');
      } catch (error) {
        if (error.original && error.original.code === 'ER_CANT_DROP_FIELD_OR_KEY') {
          console.log('字段不存在，跳过:', query.split(' ').slice(0, 6).join(' ') + '...');
        } else {
          console.error('删除字段失败:', query);
          console.error('错误:', error.message);
        }
      }
    }
    
    console.log('数据库迁移完成');
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

migrateUserTable();
