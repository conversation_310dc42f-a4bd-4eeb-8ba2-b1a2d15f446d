const { sequelize } = require('./config/db');

async function migrateUserTable() {
  try {
    console.log('正在连接数据库...');
    await sequelize.authenticate();
    console.log('数据库连接成功');
    
    console.log('正在添加认证相关字段...');
    
    // 添加认证相关字段
    const alterQueries = [
      `ALTER TABLE users ADD COLUMN real_name VARCHAR(50) NULL COMMENT '真实姓名'`,
      `ALTER TABLE users ADD COLUMN id_number VARCHAR(18) NULL COMMENT '身份证号码'`,
      `ALTER TABLE users ADD COLUMN level1_verified BOOLEAN DEFAULT FALSE COMMENT '一级认证状态'`,
      `ALTER TABLE users ADD COLUMN level1_verified_at DATETIME NULL COMMENT '一级认证完成时间'`,
      `ALTER TABLE users ADD COLUMN level2_verified BOOLEAN DEFAULT FALSE COMMENT '二级认证状态'`,
      `ALTER TABLE users ADD COLUMN level2_verified_at DATETIME NULL COMMENT '二级认证完成时间'`
    ];
    
    for (const query of alterQueries) {
      try {
        await sequelize.query(query);
        console.log('执行成功:', query.split(' ').slice(0, 6).join(' ') + '...');
      } catch (error) {
        if (error.original && error.original.code === 'ER_DUP_FIELDNAME') {
          console.log('字段已存在，跳过:', query.split(' ').slice(0, 6).join(' ') + '...');
        } else {
          console.error('执行失败:', query);
          console.error('错误:', error.message);
        }
      }
    }
    
    console.log('数据库迁移完成');
    process.exit(0);
  } catch (error) {
    console.error('迁移失败:', error);
    process.exit(1);
  }
}

migrateUserTable();
