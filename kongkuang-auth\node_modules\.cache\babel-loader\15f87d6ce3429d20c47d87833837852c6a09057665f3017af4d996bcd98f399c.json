{"ast": null, "code": "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, createBlock as _createBlock, renderSlot as _renderSlot, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"app-layout\"\n};\nconst _hoisted_2 = {\n  class: \"header-content\"\n};\nconst _hoisted_3 = {\n  class: \"header-left\"\n};\nconst _hoisted_4 = {\n  class: \"header-center\"\n};\nconst _hoisted_5 = {\n  class: \"header-right\"\n};\nconst _hoisted_6 = {\n  class: \"user-dropdown\"\n};\nconst _hoisted_7 = {\n  class: \"username\"\n};\nconst _hoisted_8 = {\n  class: \"main-content-wrapper\"\n};\nconst _hoisted_9 = {\n  class: \"sub-header\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_menu_outline = _resolveComponent(\"menu-outline\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_n_menu = _resolveComponent(\"n-menu\");\n  const _component_n_avatar = _resolveComponent(\"n-avatar\");\n  const _component_chevron_down = _resolveComponent(\"chevron-down\");\n  const _component_n_dropdown = _resolveComponent(\"n-dropdown\");\n  const _component_n_space = _resolveComponent(\"n-space\");\n  const _component_n_layout_header = _resolveComponent(\"n-layout-header\");\n  const _component_n_breadcrumb_item = _resolveComponent(\"n-breadcrumb-item\");\n  const _component_n_breadcrumb = _resolveComponent(\"n-breadcrumb\");\n  const _component_n_scrollbar = _resolveComponent(\"n-scrollbar\");\n  const _component_n_layout_content = _resolveComponent(\"n-layout-content\");\n  const _component_n_layout = _resolveComponent(\"n-layout\");\n  const _component_n_drawer_content = _resolveComponent(\"n-drawer-content\");\n  const _component_n_drawer = _resolveComponent(\"n-drawer\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_n_layout, null, {\n    default: _withCtx(() => [_createVNode(_component_n_layout_header, {\n      bordered: \"\",\n      class: \"layout-header\"\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n        class: \"logo-container\"\n      }, [_createElementVNode(\"h2\", {\n        class: \"logo-text\"\n      }, \"空旷账号中心\")], -1 /* CACHED */)), _createVNode(_component_n_button, {\n        quaternary: \"\",\n        circle: \"\",\n        size: \"medium\",\n        class: \"mobile-menu-button\",\n        onClick: _cache[0] || (_cache[0] = $event => _ctx.mobileMenuVisible = true)\n      }, {\n        icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_menu_outline)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_4, [_createVNode(_component_n_menu, {\n        mode: \"horizontal\",\n        options: _ctx.menuOptions,\n        value: _ctx.activeKey,\n        \"onUpdate:value\": _ctx.handleMenuUpdate,\n        class: \"horizontal-menu\",\n        \"inline-theme\": false,\n        inverted: false,\n        style: {\n          \"--n-item-border-radius\": \"16px\"\n        }\n      }, null, 8 /* PROPS */, [\"options\", \"value\", \"onUpdate:value\"])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_n_space, {\n        size: 16\n      }, {\n        default: _withCtx(() => [_createVNode(_component_n_dropdown, {\n          options: _ctx.userMenuOptions,\n          onSelect: _ctx.handleUserMenuSelect,\n          trigger: \"click\"\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_n_avatar, {\n            round: \"\",\n            size: \"medium\",\n            src: _ctx.userAvatar || '/default-avatar.png',\n            class: \"user-avatar\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"span\", _hoisted_7, _toDisplayString(_ctx.username), 1 /* TEXT */), _createVNode(_component_n_icon, {\n            class: \"dropdown-icon\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_chevron_down)]),\n            _: 1 /* STABLE */\n          })])]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"options\", \"onSelect\"])]),\n        _: 1 /* STABLE */\n      })])])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_n_layout_content, {\n      class: \"layout-main\",\n      \"native-scrollbar\": true,\n      \"content-style\": \"min-height: calc(100vh - 64px); padding: 0;\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_scrollbar, {\n        style: {\n          \"max-height\": \"calc(100vh - 64px)\"\n        }\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_n_breadcrumb, null, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.breadcrumbItems, (item, index) => {\n            return _openBlock(), _createBlock(_component_n_breadcrumb_item, {\n              key: index\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString(item), 1 /* TEXT */)]),\n              _: 2 /* DYNAMIC */\n            }, 1024 /* DYNAMIC_SLOTS */);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        })]), _renderSlot(_ctx.$slots, \"default\", {}, undefined, true)])]),\n        _: 3 /* FORWARDED */\n      })]),\n      _: 3 /* FORWARDED */\n    })]),\n    _: 3 /* FORWARDED */\n  }), _createCommentVNode(\" 移动端菜单抽屉 \"), _createVNode(_component_n_drawer, {\n    show: _ctx.mobileMenuVisible,\n    \"onUpdate:show\": _cache[1] || (_cache[1] = $event => _ctx.mobileMenuVisible = $event),\n    width: 280,\n    placement: \"left\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_n_drawer_content, {\n      title: \"菜单\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_n_menu, {\n        options: _ctx.menuOptions,\n        value: _ctx.activeKey,\n        \"onUpdate:value\": _ctx.handleMobileMenuSelect\n      }, null, 8 /* PROPS */, [\"options\", \"value\", \"onUpdate:value\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_n_layout", "_component_n_layout_header", "bordered", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_component_n_button", "quaternary", "circle", "size", "onClick", "_cache", "$event", "_ctx", "mobileMenuVisible", "icon", "_withCtx", "_component_n_icon", "_component_menu_outline", "_hoisted_4", "_component_n_menu", "mode", "options", "menuOptions", "value", "active<PERSON><PERSON>", "handleMenuUpdate", "inverted", "style", "_hoisted_5", "_component_n_space", "_component_n_dropdown", "userMenuOptions", "onSelect", "handleUserMenuSelect", "trigger", "_hoisted_6", "_component_n_avatar", "round", "src", "userAvatar", "_hoisted_7", "_toDisplayString", "username", "_component_chevron_down", "_component_n_layout_content", "_component_n_scrollbar", "_hoisted_8", "_hoisted_9", "_component_n_breadcrumb", "_Fragment", "_renderList", "breadcrumbItems", "item", "index", "_createBlock", "_component_n_breadcrumb_item", "key", "_renderSlot", "$slots", "undefined", "_createCommentVNode", "_component_n_drawer", "show", "width", "placement", "_component_n_drawer_content", "title", "handleMobileMenuSelect"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\AppLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"app-layout\">\n    <n-layout>\n      <n-layout-header bordered class=\"layout-header\">\n        <div class=\"header-content\">\n          <div class=\"header-left\">\n            <div class=\"logo-container\">\n              <h2 class=\"logo-text\">空旷账号中心</h2>\n            </div>\n            <n-button\n              quaternary\n              circle\n              size=\"medium\"\n              class=\"mobile-menu-button\"\n              @click=\"mobileMenuVisible = true\"\n            >\n              <template #icon\n                ><n-icon><menu-outline /></n-icon\n              ></template>\n            </n-button>\n          </div>\n          \n          <div class=\"header-center\">\n            <n-menu\n              mode=\"horizontal\"\n              :options=\"menuOptions\"\n              :value=\"activeKey\"\n              @update:value=\"handleMenuUpdate\"\n              class=\"horizontal-menu\"\n              :inline-theme=\"false\"\n              :inverted=\"false\"\n              style=\"--n-item-border-radius: 16px;\"\n            />\n          </div>\n          \n          <div class=\"header-right\">\n            <n-space :size=\"16\">\n              <n-dropdown\n                :options=\"userMenuOptions\"\n                @select=\"handleUserMenuSelect\"\n                trigger=\"click\"\n              >\n                <div class=\"user-dropdown\">\n                  <n-avatar\n                    round\n                    size=\"medium\"\n                    :src=\"userAvatar || '/default-avatar.png'\"\n                    class=\"user-avatar\"\n                  />\n                  <span class=\"username\">{{ username }}</span>\n                  <n-icon class=\"dropdown-icon\"><chevron-down /></n-icon>\n                </div>\n              </n-dropdown>\n            </n-space>\n          </div>\n        </div>\n      </n-layout-header>\n\n      <n-layout-content\n        class=\"layout-main\"\n        :native-scrollbar=\"true\"\n        content-style=\"min-height: calc(100vh - 64px); padding: 0;\"\n      >\n        <n-scrollbar style=\"max-height: calc(100vh - 64px);\">\n          <div class=\"main-content-wrapper\">\n            <div class=\"sub-header\">\n              <n-breadcrumb>\n                <n-breadcrumb-item\n                  v-for=\"(item, index) in breadcrumbItems\"\n                  :key=\"index\"\n                >\n                  {{ item }}\n                </n-breadcrumb-item>\n              </n-breadcrumb>\n            </div>\n            <slot></slot>\n          </div>\n        </n-scrollbar>\n      </n-layout-content>\n    </n-layout>\n\n    <!-- 移动端菜单抽屉 -->\n    <n-drawer v-model:show=\"mobileMenuVisible\" :width=\"280\" placement=\"left\">\n      <n-drawer-content title=\"菜单\">\n        <n-menu\n          :options=\"menuOptions\"\n          :value=\"activeKey\"\n          @update:value=\"handleMobileMenuSelect\"\n        />\n      </n-drawer-content>\n    </n-drawer>\n  </div>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, h } from 'vue'\nimport { useRouter, useRoute } from 'vue-router'\nimport {\n  NLayout,\n  NLayoutHeader,\n  NLayoutContent,\n  NMenu,\n  NBreadcrumb,\n  NBreadcrumbItem,\n  NAvatar,\n  NDropdown,\n  NIcon,\n  NButton,\n  NSpace,\n  NDrawer,\n  NDrawerContent,\n  useMessage\n} from 'naive-ui'\nimport {\n  HomeOutline,\n  PersonOutline,\n  ShieldOutline,\n  AppsOutline,\n  LogOutOutline,\n  ChevronDown,\n  MenuOutline,\n  IdCardOutline\n} from '@vicons/ionicons5'\nimport { useUserStore } from '../stores/user'\n\nfunction renderIcon(icon) {\n  return () => h(NIcon, null, { default: () => h(icon) })\n}\n\nexport default defineComponent({\n  name: 'AppLayout',\n  components: {\n    NLayout,\n    NLayoutHeader,\n    NLayoutContent,\n    NMenu,\n    NBreadcrumb,\n    NBreadcrumbItem,\n    NAvatar,\n    NDropdown,\n    NIcon,\n    NButton,\n    NSpace,\n    NDrawer,\n    NDrawerContent,\n    ChevronDown,\n    MenuOutline,\n    IdCardOutline\n  },\n  setup() {\n    const router = useRouter()\n    const route = useRoute()\n    const message = useMessage()\n    const userStore = useUserStore()\n    const mobileMenuVisible = ref(false)\n    \n    // 用户头像和用户名\n    const userAvatar = computed(() => userStore.user?.avatar || '')\n    const username = computed(() => userStore.user?.nickname || userStore.user?.username || '用户')\n    \n    // 当前激活的菜单项\n    const activeKey = computed(() => {\n      const path = route.path\n      if (path.startsWith('/dashboard')) return 'dashboard'\n      if (path.startsWith('/profile')) return 'profile'\n      if (path.startsWith('/security')) return 'security'\n      if (path.startsWith('/applications')) return 'applications'\n      if (path.startsWith('/verification')) return 'verification'\n      return ''\n    })\n    \n    // 面包屑导航\n    const breadcrumbItems = computed(() => {\n      const path = route.path\n      const items = ['首页']\n      \n      if (path.startsWith('/dashboard')) {\n        items.push('仪表盘')\n      } else if (path.startsWith('/profile')) {\n        items.push('个人资料')\n      } else if (path.startsWith('/security')) {\n        items.push('安全设置')\n      } else if (path.startsWith('/applications')) {\n        items.push('应用管理')\n        if (path.includes('/create')) {\n          items.push('创建应用')\n        } else if (path.match(/\\/applications\\/[^/]+$/)) {\n          items.push('应用详情')\n        }\n      } else if (path.startsWith('/verification')) {\n        items.push('实名认证')\n      }\n      \n      return items\n    })\n    \n    // 顶部菜单选项\n    const menuOptions = [\n      {\n        label: '仪表盘',\n        key: 'dashboard',\n        icon: renderIcon(HomeOutline)\n      },\n      {\n        label: '个人资料',\n        key: 'profile',\n        icon: renderIcon(PersonOutline)\n      },\n      {\n        label: '安全设置',\n        key: 'security',\n        icon: renderIcon(ShieldOutline)\n      },\n      {\n        label: '实名认证',\n        key: 'verification',\n        icon: renderIcon(IdCardOutline)\n      },\n      {\n        label: '应用管理',\n        key: 'applications',\n        icon: renderIcon(AppsOutline)\n      }\n    ]\n    \n    // 用户下拉菜单选项\n    const userMenuOptions = [\n      {\n        label: userStore.user?.username || '用户',\n        key: 'username',\n        disabled: true\n      },\n      {\n        type: 'divider',\n        key: 'd1'\n      },\n      {\n        label: '个人资料',\n        key: 'profile',\n        icon: renderIcon(PersonOutline)\n      },\n      {\n        label: '安全设置',\n        key: 'security',\n        icon: renderIcon(ShieldOutline)\n      },\n      {\n        type: 'divider',\n        key: 'd2'\n      },\n      {\n        label: '退出登录',\n        key: 'logout',\n        icon: renderIcon(LogOutOutline)\n      }\n    ]\n    \n    // 处理菜单点击\n    const handleMenuUpdate = (key) => {\n      switch (key) {\n        case 'dashboard':\n          router.push('/dashboard')\n          break\n        case 'profile':\n          router.push('/profile')\n          break\n        case 'security':\n          router.push('/security')\n          break\n        case 'applications':\n          router.push('/applications')\n          break\n        case 'verification':\n          router.push('/verification')\n          break\n      }\n    }\n    \n    // 处理移动端菜单点击\n    const handleMobileMenuSelect = (key) => {\n      handleMenuUpdate(key)\n      mobileMenuVisible.value = false\n    }\n    \n    // 处理用户菜单选择\n    const handleUserMenuSelect = (key) => {\n      switch (key) {\n        case 'profile':\n          router.push('/profile')\n          break\n        case 'security':\n          router.push('/security')\n          break\n        case 'logout':\n          handleLogout()\n          break\n      }\n    }\n    \n    // 退出登录\n    const handleLogout = async () => {\n      try {\n        await userStore.logout()\n        message.success('退出登录成功')\n        router.push('/login')\n      } catch (error) {\n        message.error('退出登录失败')\n      }\n    }\n    \n    return {\n      userAvatar,\n      username,\n      activeKey,\n      breadcrumbItems,\n      menuOptions,\n      userMenuOptions,\n      handleMenuUpdate,\n      handleUserMenuSelect,\n      mobileMenuVisible,\n      handleMobileMenuSelect\n    }\n  }\n})\n</script>\n\n<style scoped>\n.app-layout {\n  height: 100%;\n  overflow: hidden;\n}\n\n.layout-header {\n  padding: 0 24px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n  z-index: 100;\n  position: relative;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: 64px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n  margin-right: 10px;\n}\n\n.logo-container {\n  display: flex;\n  align-items: center;\n  margin-right: 10px;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n  color: var(--text-color-1);\n  margin: 0;\n  padding: 0;\n  white-space: nowrap;\n}\n\n.mobile-menu-button {\n  display: none;\n}\n\n.header-center {\n  flex-grow: 1;\n  display: flex;\n  justify-content: center;\n  margin-left: -40px; /* 向左偏移，使菜单更靠近Logo */\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  flex-shrink: 0;\n}\n\n.horizontal-menu {\n  display: flex;\n  justify-content: center;\n}\n\n.user-dropdown {\n  display: flex;\n  align-items: center;\n  cursor: pointer;\n  padding: 4px 8px;\n  border-radius: 8px;\n  transition: background-color 0.2s;\n}\n\n.user-dropdown:hover {\n  background-color: var(--hover-color);\n}\n\n.user-avatar {\n  margin-right: 8px;\n  transition: transform 0.2s ease;\n}\n\n.username {\n  font-size: 14px;\n  font-weight: 500;\n  margin-right: 4px;\n  color: var(--text-color-1);\n}\n\n.dropdown-icon {\n  font-size: 16px;\n  color: var(--text-color-3);\n}\n\n.layout-main {\n  background-color: var(--body-color);\n}\n\n.main-content-wrapper {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 24px;\n}\n\n.sub-header {\n  margin-bottom: 24px;\n}\n\n/* 导航菜单样式 */\n:deep(.n-menu .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item-content-header) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item.n-menu-item--selected .n-menu-item-content::before) {\n  border-radius: 16px;\n}\n\n:deep(.n-menu .n-menu-item:hover .n-menu-item-content) {\n  border-radius: 16px;\n}\n\n/* 强制覆盖导航菜单样式 */\n:deep(.n-menu-item-content) {\n  border-radius: 16px !important;\n  padding: 0 16px !important;\n}\n\n:deep(.n-menu-item-content::before) {\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item-content-header) {\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item:hover .n-menu-item-content),\n:deep(.n-menu-item.n-menu-item--selected .n-menu-item-content) {\n  background-color: var(--n-item-color-active) !important;\n  border-radius: 16px !important;\n}\n\n:deep(.n-menu-item) {\n  margin: 0 4px !important;\n}\n\n\n@media (max-width: 992px) {\n  .main-content-wrapper {\n    padding: 16px;\n  }\n}\n\n@media (max-width: 768px) {\n  .mobile-menu-button {\n    display: block;\n  }\n\n  .header-center {\n    display: none;\n  }\n  \n  .logo-text {\n    font-size: 18px;\n  }\n}\n\n@media (max-width: 576px) {\n  .username {\n    display: none;\n  }\n\n  .dropdown-icon {\n    display: none;\n  }\n\n  .user-dropdown {\n    padding: 0;\n  }\n\n  .main-content-wrapper {\n    padding: 12px;\n  }\n\n  .layout-header {\n    padding: 0 16px;\n  }\n  \n  .logo-text {\n    font-size: 16px;\n  }\n}\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAY;;EAGZA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EAiBnBA,KAAK,EAAC;AAAe;;EAarBA,KAAK,EAAC;AAAc;;EAOdA,KAAK,EAAC;AAAe;;EAOlBA,KAAK,EAAC;AAAU;;EAezBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAY;;;;;;;;;;;;;;;;;;uBAhEjCC,mBAAA,CA0FM,OA1FNC,UA0FM,GAzFJC,YAAA,CA6EWC,mBAAA;sBA5ET,MAqDkB,CArDlBD,YAAA,CAqDkBE,0BAAA;MArDDC,QAAQ,EAAR,EAAQ;MAACN,KAAK,EAAC;;wBAC9B,MAmDM,CAnDNO,mBAAA,CAmDM,OAnDNC,UAmDM,GAlDJD,mBAAA,CAeM,OAfNE,UAeM,G,0BAdJF,mBAAA,CAEM;QAFDP,KAAK,EAAC;MAAgB,IACzBO,mBAAA,CAAiC;QAA7BP,KAAK,EAAC;MAAW,GAAC,QAAM,E,qBAE9BG,YAAA,CAUWO,mBAAA;QATTC,UAAU,EAAV,EAAU;QACVC,MAAM,EAAN,EAAM;QACNC,IAAI,EAAC,QAAQ;QACbb,KAAK,EAAC,oBAAoB;QACzBc,OAAK,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,IAAA,CAAAC,iBAAiB;;QAEdC,IAAI,EAAAC,QAAA,CACZ,MACF,CADEjB,YAAA,CACFkB,iBAAA;4BADU,MAAgB,CAAhBlB,YAAA,CAAgBmB,uBAAA,E;;;;YAK/Bf,mBAAA,CAWM,OAXNgB,UAWM,GAVJpB,YAAA,CASEqB,iBAAA;QARAC,IAAI,EAAC,YAAY;QAChBC,OAAO,EAAET,IAAA,CAAAU,WAAW;QACpBC,KAAK,EAAEX,IAAA,CAAAY,SAAS;QAChB,gBAAY,EAAEZ,IAAA,CAAAa,gBAAgB;QAC/B9B,KAAK,EAAC,iBAAiB;QACtB,cAAY,EAAE,KAAK;QACnB+B,QAAQ,EAAE,KAAK;QAChBC,KAAqC,EAArC;UAAA;QAAA;yEAIJzB,mBAAA,CAmBM,OAnBN0B,UAmBM,GAlBJ9B,YAAA,CAiBU+B,kBAAA;QAjBArB,IAAI,EAAE;MAAE;0BAChB,MAea,CAfbV,YAAA,CAeagC,qBAAA;UAdVT,OAAO,EAAET,IAAA,CAAAmB,eAAe;UACxBC,QAAM,EAAEpB,IAAA,CAAAqB,oBAAoB;UAC7BC,OAAO,EAAC;;4BAER,MASM,CATNhC,mBAAA,CASM,OATNiC,UASM,GARJrC,YAAA,CAKEsC,mBAAA;YAJAC,KAAK,EAAL,EAAK;YACL7B,IAAI,EAAC,QAAQ;YACZ8B,GAAG,EAAE1B,IAAA,CAAA2B,UAAU;YAChB5C,KAAK,EAAC;4CAERO,mBAAA,CAA4C,QAA5CsC,UAA4C,EAAAC,gBAAA,CAAlB7B,IAAA,CAAA8B,QAAQ,kBAClC5C,YAAA,CAAuDkB,iBAAA;YAA/CrB,KAAK,EAAC;UAAe;8BAAC,MAAgB,CAAhBG,YAAA,CAAgB6C,uBAAA,E;;;;;;;;QAQ1D7C,YAAA,CAoBmB8C,2BAAA;MAnBjBjD,KAAK,EAAC,aAAa;MAClB,kBAAgB,EAAE,IAAI;MACvB,eAAa,EAAC;;wBAEd,MAcc,CAddG,YAAA,CAcc+C,sBAAA;QAdDlB,KAAuC,EAAvC;UAAA;QAAA;MAAuC;0BAClD,MAYM,CAZNzB,mBAAA,CAYM,OAZN4C,UAYM,GAXJ5C,mBAAA,CASM,OATN6C,UASM,GARJjD,YAAA,CAOekD,uBAAA;4BALX,MAAwC,E,kBAD1CpD,mBAAA,CAKoBqD,SAAA,QAAAC,WAAA,CAJMtC,IAAA,CAAAuC,eAAe,GAA/BC,IAAI,EAAEC,KAAK;iCADrBC,YAAA,CAKoBC,4BAAA;cAHjBC,GAAG,EAAEH;YAAK;gCAEX,MAAU,C,kCAAPD,IAAI,iB;;;;;cAIbK,WAAA,CAAa7C,IAAA,CAAA8C,MAAA,iBAAAC,SAAA,Q;;;;;;MAMrBC,mBAAA,aAAgB,EAChB9D,YAAA,CAQW+D,mBAAA;IAROC,IAAI,EAAElD,IAAA,CAAAC,iBAAiB;yDAAjBD,IAAA,CAAAC,iBAAiB,GAAAF,MAAA;IAAGoD,KAAK,EAAE,GAAG;IAAEC,SAAS,EAAC;;sBAChE,MAMmB,CANnBlE,YAAA,CAMmBmE,2BAAA;MANDC,KAAK,EAAC;IAAI;wBAC1B,MAIE,CAJFpE,YAAA,CAIEqB,iBAAA;QAHCE,OAAO,EAAET,IAAA,CAAAU,WAAW;QACpBC,KAAK,EAAEX,IAAA,CAAAY,SAAS;QAChB,gBAAY,EAAEZ,IAAA,CAAAuD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}