{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createVNode as _createVNode, withCtx as _withCtx, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, Fragment as _Fragment, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"face-payment-result\"\n};\nconst _hoisted_2 = {\n  class: \"result-content\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"result-success\"\n};\nconst _hoisted_4 = {\n  class: \"result-icon\"\n};\nconst _hoisted_5 = {\n  class: \"payment-info\"\n};\nconst _hoisted_6 = {\n  class: \"info-item\"\n};\nconst _hoisted_7 = {\n  class: \"value\"\n};\nconst _hoisted_8 = {\n  class: \"info-item\"\n};\nconst _hoisted_9 = {\n  class: \"value\"\n};\nconst _hoisted_10 = {\n  class: \"info-item\"\n};\nconst _hoisted_11 = {\n  class: \"value\"\n};\nconst _hoisted_12 = {\n  class: \"identity-info\"\n};\nconst _hoisted_13 = {\n  class: \"identity-details\"\n};\nconst _hoisted_14 = {\n  class: \"info-item\"\n};\nconst _hoisted_15 = {\n  class: \"value\"\n};\nconst _hoisted_16 = {\n  class: \"info-item\"\n};\nconst _hoisted_17 = {\n  class: \"value\"\n};\nconst _hoisted_18 = {\n  class: \"notice\"\n};\nconst _hoisted_19 = {\n  class: \"result-failed\"\n};\nconst _hoisted_20 = {\n  class: \"result-icon\"\n};\nconst _hoisted_21 = {\n  class: \"result-message\"\n};\nconst _hoisted_22 = {\n  class: \"failure-reasons\"\n};\nconst _hoisted_23 = {\n  class: \"result-processing\"\n};\nconst _hoisted_24 = {\n  class: \"result-icon\"\n};\nconst _hoisted_25 = {\n  class: \"processing-steps\"\n};\nconst _hoisted_26 = {\n  class: \"modal-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"NModal\"], {\n    show: $setup.visible,\n    \"onUpdate:show\": _cache[0] || (_cache[0] = $event => $setup.visible = $event),\n    preset: \"card\",\n    title: $setup.resultTitle,\n    style: {\n      \"width\": \"500px\",\n      \"max-width\": \"90vw\"\n    },\n    closable: false,\n    \"mask-closable\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [$props.status === 'success' ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.handleConfirm\n    }, {\n      default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\" 确定 \")])),\n      _: 1 /* STABLE */,\n      __: [17]\n    })) : $props.status === 'failed' ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      onClick: $setup.handleRetry\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\" 重试 \")])),\n      _: 1 /* STABLE */,\n      __: [18]\n    })) : _createCommentVNode(\"v-if\", true), $props.status === 'failed' ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 2,\n      type: \"primary\",\n      onClick: $setup.handleConfirm\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\" 关闭 \")])),\n      _: 1 /* STABLE */,\n      __: [19]\n    })) : _createCommentVNode(\"v-if\", true)])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 成功状态 \"), $props.status === 'success' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createVNode($setup[\"NIcon\"], {\n      size: \"64\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"CheckmarkCircleOutline\"])]),\n      _: 1 /* STABLE */\n    })]), _cache[7] || (_cache[7] = _createElementVNode(\"h3\", null, \"识脸支付成功\", -1 /* CACHED */)), _cache[8] || (_cache[8] = _createElementVNode(\"p\", {\n      class: \"result-message\"\n    }, \"恭喜！您已成功完成一级认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"支付方式：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_7, _toDisplayString($props.paymentMethod === 'alipay' ? '支付宝识脸支付' : '微信识脸支付'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_8, [_cache[2] || (_cache[2] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"支付金额：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_9, \"¥\" + _toDisplayString($props.amount), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_10, [_cache[3] || (_cache[3] = _createElementVNode(\"span\", {\n      class: \"label\"\n    }, \"认证时间：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_11, _toDisplayString($setup.formatTime($props.verifiedAt)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_12, [_createVNode($setup[\"NAlert\"], {\n      title: \"获取的实名信息\",\n      type: \"success\",\n      style: {\n        \"margin\": \"16px 0\"\n      }\n    }, {\n      default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, [_cache[4] || (_cache[4] = _createElementVNode(\"span\", {\n        class: \"label\"\n      }, \"真实姓名：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_15, _toDisplayString($setup.maskedName), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_16, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", {\n        class: \"label\"\n      }, \"身份证号：\", -1 /* CACHED */)), _createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.maskedIdNumber), 1 /* TEXT */)])])]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_18, [_createVNode($setup[\"NAlert\"], {\n      title: \"重要提示\",\n      type: \"warning\",\n      style: {\n        \"margin-top\": \"16px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[6] || (_cache[6] = [_createElementVNode(\"p\", null, \"• 一级认证信息将在30分钟后自动清除\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"• 如需永久保存，请完成二级认证\", -1 /* CACHED */), _createElementVNode(\"p\", null, \"• 认证信息仅用于身份验证，严格保护隐私\", -1 /* CACHED */)])),\n      _: 1 /* STABLE */,\n      __: [6]\n    })])])) : $props.status === 'failed' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 失败状态 \"), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_createVNode($setup[\"NIcon\"], {\n      size: \"64\",\n      color: \"#d03050\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"CloseCircleOutline\"])]),\n      _: 1 /* STABLE */\n    })]), _cache[10] || (_cache[10] = _createElementVNode(\"h3\", null, \"识脸支付失败\", -1 /* CACHED */)), _createElementVNode(\"p\", _hoisted_21, _toDisplayString($props.errorMessage || '支付过程中出现问题，请重试'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, [_createVNode($setup[\"NAlert\"], {\n      title: \"可能的原因\",\n      type: \"error\",\n      style: {\n        \"margin\": \"16px 0\"\n      }\n    }, {\n      default: _withCtx(() => _cache[9] || (_cache[9] = [_createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"人脸识别验证失败\"), _createElementVNode(\"li\", null, \"支付账户余额不足\"), _createElementVNode(\"li\", null, \"网络连接异常\"), _createElementVNode(\"li\", null, \"支付平台服务异常\")], -1 /* CACHED */)])),\n      _: 1 /* STABLE */,\n      __: [9]\n    })])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $props.status === 'processing' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createCommentVNode(\" 处理中状态 \"), _createElementVNode(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, [_createVNode($setup[\"NSpin\"], {\n      size: \"large\"\n    })]), _cache[15] || (_cache[15] = _createElementVNode(\"h3\", null, \"正在处理\", -1 /* CACHED */)), _cache[16] || (_cache[16] = _createElementVNode(\"p\", {\n      class: \"result-message\"\n    }, \"正在验证支付结果，请稍候...\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"step\", {\n        active: $setup.currentStep >= 1\n      }])\n    }, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ScanOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[11] || (_cache[11] = _createElementVNode(\"span\", null, \"人脸识别验证\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"step\", {\n        active: $setup.currentStep >= 2\n      }])\n    }, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"WalletOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"处理支付\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"step\", {\n        active: $setup.currentStep >= 3\n      }])\n    }, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"PersonOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"获取实名信息\", -1 /* CACHED */))], 2 /* CLASS */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"step\", {\n        active: $setup.currentStep >= 4\n      }])\n    }, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"CheckmarkCircleOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[14] || (_cache[14] = _createElementVNode(\"span\", null, \"完成认证\", -1 /* CACHED */))], 2 /* CLASS */)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\", \"title\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "show", "visible", "$event", "preset", "title", "resultTitle", "style", "closable", "footer", "_withCtx", "_createElementVNode", "_hoisted_26", "$props", "status", "_createBlock", "type", "onClick", "handleConfirm", "_cache", "handleRetry", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "size", "color", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_toDisplayString", "paymentMethod", "_hoisted_8", "_hoisted_9", "amount", "_hoisted_10", "_hoisted_11", "formatTime", "verifiedAt", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "<PERSON><PERSON><PERSON>", "_hoisted_16", "_hoisted_17", "maskedIdNumber", "_hoisted_18", "_Fragment", "key", "_hoisted_19", "_hoisted_20", "_hoisted_21", "errorMessage", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_normalizeClass", "active", "currentStep"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\FacePaymentResult.vue"], "sourcesContent": ["<template>\n  <div class=\"face-payment-result\">\n    <n-modal \n      v-model:show=\"visible\" \n      preset=\"card\" \n      :title=\"resultTitle\"\n      style=\"width: 500px; max-width: 90vw;\"\n      :closable=\"false\"\n      :mask-closable=\"false\"\n    >\n      <div class=\"result-content\">\n        <!-- 成功状态 -->\n        <div v-if=\"status === 'success'\" class=\"result-success\">\n          <div class=\"result-icon\">\n            <n-icon size=\"64\" color=\"#18a058\">\n              <checkmark-circle-outline />\n            </n-icon>\n          </div>\n          <h3>识脸支付成功</h3>\n          <p class=\"result-message\">恭喜！您已成功完成一级认证</p>\n          \n          <div class=\"payment-info\">\n            <div class=\"info-item\">\n              <span class=\"label\">支付方式：</span>\n              <span class=\"value\">{{ paymentMethod === 'alipay' ? '支付宝识脸支付' : '微信识脸支付' }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">支付金额：</span>\n              <span class=\"value\">¥{{ amount }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">认证时间：</span>\n              <span class=\"value\">{{ formatTime(verifiedAt) }}</span>\n            </div>\n          </div>\n          \n          <div class=\"identity-info\">\n            <n-alert title=\"获取的实名信息\" type=\"success\" style=\"margin: 16px 0;\">\n              <div class=\"identity-details\">\n                <div class=\"info-item\">\n                  <span class=\"label\">真实姓名：</span>\n                  <span class=\"value\">{{ maskedName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"label\">身份证号：</span>\n                  <span class=\"value\">{{ maskedIdNumber }}</span>\n                </div>\n              </div>\n            </n-alert>\n          </div>\n          \n          <div class=\"notice\">\n            <n-alert title=\"重要提示\" type=\"warning\" style=\"margin-top: 16px;\">\n              <p>• 一级认证信息将在30分钟后自动清除</p>\n              <p>• 如需永久保存，请完成二级认证</p>\n              <p>• 认证信息仅用于身份验证，严格保护隐私</p>\n            </n-alert>\n          </div>\n        </div>\n        \n        <!-- 失败状态 -->\n        <div v-else-if=\"status === 'failed'\" class=\"result-failed\">\n          <div class=\"result-icon\">\n            <n-icon size=\"64\" color=\"#d03050\">\n              <close-circle-outline />\n            </n-icon>\n          </div>\n          <h3>识脸支付失败</h3>\n          <p class=\"result-message\">{{ errorMessage || '支付过程中出现问题，请重试' }}</p>\n          \n          <div class=\"failure-reasons\">\n            <n-alert title=\"可能的原因\" type=\"error\" style=\"margin: 16px 0;\">\n              <ul>\n                <li>人脸识别验证失败</li>\n                <li>支付账户余额不足</li>\n                <li>网络连接异常</li>\n                <li>支付平台服务异常</li>\n              </ul>\n            </n-alert>\n          </div>\n        </div>\n        \n        <!-- 处理中状态 -->\n        <div v-else-if=\"status === 'processing'\" class=\"result-processing\">\n          <div class=\"result-icon\">\n            <n-spin size=\"large\" />\n          </div>\n          <h3>正在处理</h3>\n          <p class=\"result-message\">正在验证支付结果，请稍候...</p>\n          \n          <div class=\"processing-steps\">\n            <div class=\"step\" :class=\"{ active: currentStep >= 1 }\">\n              <n-icon size=\"16\"><scan-outline /></n-icon>\n              <span>人脸识别验证</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 2 }\">\n              <n-icon size=\"16\"><wallet-outline /></n-icon>\n              <span>处理支付</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 3 }\">\n              <n-icon size=\"16\"><person-outline /></n-icon>\n              <span>获取实名信息</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n              <n-icon size=\"16\"><checkmark-circle-outline /></n-icon>\n              <span>完成认证</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <template #footer>\n        <div class=\"modal-footer\">\n          <n-button \n            v-if=\"status === 'success'\" \n            type=\"primary\" \n            @click=\"handleConfirm\"\n          >\n            确定\n          </n-button>\n          <n-button \n            v-else-if=\"status === 'failed'\" \n            @click=\"handleRetry\"\n          >\n            重试\n          </n-button>\n          <n-button \n            v-if=\"status === 'failed'\" \n            type=\"primary\" \n            @click=\"handleConfirm\"\n          >\n            关闭\n          </n-button>\n        </div>\n      </template>\n    </n-modal>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted } from 'vue';\nimport {\n  NModal,\n  NIcon,\n  NSpin,\n  NButton,\n  NAlert\n} from 'naive-ui';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  ScanOutline,\n  WalletOutline,\n  PersonOutline\n} from '@vicons/ionicons5';\n\nconst props = defineProps({\n  show: {\n    type: Boolean,\n    default: false\n  },\n  status: {\n    type: String,\n    default: 'processing', // processing, success, failed\n    validator: (value) => ['processing', 'success', 'failed'].includes(value)\n  },\n  paymentMethod: {\n    type: String,\n    default: 'alipay' // alipay, wechat\n  },\n  amount: {\n    type: [String, Number],\n    default: '1.2'\n  },\n  realName: {\n    type: String,\n    default: ''\n  },\n  idNumber: {\n    type: String,\n    default: ''\n  },\n  verifiedAt: {\n    type: [String, Date],\n    default: null\n  },\n  errorMessage: {\n    type: String,\n    default: ''\n  }\n});\n\nconst emit = defineEmits(['update:show', 'confirm', 'retry']);\n\nconst visible = computed({\n  get: () => props.show,\n  set: (value) => emit('update:show', value)\n});\n\nconst currentStep = ref(1);\n\nconst resultTitle = computed(() => {\n  switch (props.status) {\n    case 'success':\n      return '一级认证成功';\n    case 'failed':\n      return '认证失败';\n    case 'processing':\n    default:\n      return '正在认证';\n  }\n});\n\nconst maskedName = computed(() => {\n  if (!props.realName) return '';\n  if (props.realName.length <= 2) return props.realName;\n  return props.realName.charAt(0) + '***' + props.realName.charAt(props.realName.length - 1);\n});\n\nconst maskedIdNumber = computed(() => {\n  if (!props.idNumber) return '';\n  if (props.idNumber.length < 10) return props.idNumber;\n  return props.idNumber.substring(0, 6) + '****' + props.idNumber.substring(props.idNumber.length - 4);\n});\n\nconst formatTime = (time) => {\n  if (!time) return '';\n  const date = new Date(time);\n  return date.toLocaleString('zh-CN');\n};\n\nconst handleConfirm = () => {\n  emit('confirm');\n  visible.value = false;\n};\n\nconst handleRetry = () => {\n  emit('retry');\n  visible.value = false;\n};\n\n// 模拟处理步骤\nwatch(() => props.status, (newStatus) => {\n  if (newStatus === 'processing') {\n    currentStep.value = 1;\n    const stepInterval = setInterval(() => {\n      if (currentStep.value < 4 && props.status === 'processing') {\n        currentStep.value++;\n      } else {\n        clearInterval(stepInterval);\n      }\n    }, 1000);\n  }\n});\n\nonMounted(() => {\n  if (props.status === 'processing') {\n    currentStep.value = 1;\n  }\n});\n</script>\n\n<style scoped>\n.face-payment-result {\n  /* 组件容器样式 */\n}\n\n.result-content {\n  text-align: center;\n  padding: 16px 0;\n}\n\n.result-icon {\n  margin-bottom: 16px;\n}\n\n.result-content h3 {\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.result-message {\n  margin: 0 0 24px 0;\n  color: var(--n-text-color-2);\n  font-size: 14px;\n}\n\n.payment-info,\n.identity-details {\n  text-align: left;\n  margin: 16px 0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid var(--n-border-color);\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.info-item .label {\n  font-weight: 500;\n  color: var(--n-text-color-2);\n}\n\n.info-item .value {\n  font-weight: 600;\n  color: var(--n-text-color-1);\n}\n\n.processing-steps {\n  display: flex;\n  justify-content: space-between;\n  margin: 24px 0;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  transition: all 0.3s ease;\n}\n\n.step.active {\n  color: var(--n-primary-color);\n}\n\n.step span {\n  text-align: center;\n  max-width: 60px;\n  line-height: 1.2;\n}\n\n.failure-reasons ul {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n\n.failure-reasons li {\n  margin: 4px 0;\n  color: var(--n-text-color-2);\n}\n\n.notice p {\n  margin: 4px 0;\n  font-size: 13px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n@media (max-width: 768px) {\n  .processing-steps {\n    flex-direction: column;\n    gap: 16px;\n  }\n  \n  .step {\n    flex-direction: row;\n    justify-content: flex-start;\n    text-align: left;\n  }\n  \n  .step span {\n    max-width: none;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EASvBA,KAAK,EAAC;AAAgB;;;EAEQA,KAAK,EAAC;;;EAChCA,KAAK,EAAC;AAAa;;EAQnBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAIlBA,KAAK,EAAC;AAAe;;EAEjBA,KAAK,EAAC;AAAkB;;EACtBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAEhBA,KAAK,EAAC;AAAW;;EAEdA,KAAK,EAAC;AAAO;;EAMtBA,KAAK,EAAC;AAAQ;;EAUgBA,KAAK,EAAC;AAAe;;EACnDA,KAAK,EAAC;AAAa;;EAMrBA,KAAK,EAAC;AAAgB;;EAEpBA,KAAK,EAAC;AAAiB;;EAaWA,KAAK,EAAC;AAAmB;;EAC3DA,KAAK,EAAC;AAAa;;EAMnBA,KAAK,EAAC;AAAkB;;EAsB1BA,KAAK,EAAC;AAAc;;uBA/G/BC,mBAAA,CAuIM,OAvINC,UAuIM,GAtIJC,YAAA,CAqIUC,MAAA;IApIAC,IAAI,EAAED,MAAA,CAAAE,OAAO;yDAAPF,MAAA,CAAAE,OAAO,GAAAC,MAAA;IACrBC,MAAM,EAAC,MAAM;IACZC,KAAK,EAAEL,MAAA,CAAAM,WAAW;IACnBC,KAAsC,EAAtC;MAAA;MAAA;IAAA,CAAsC;IACrCC,QAAQ,EAAE,KAAK;IACf,eAAa,EAAE;;IAuGLC,MAAM,EAAAC,QAAA,CACf,MAqBM,CArBNC,mBAAA,CAqBM,OArBNC,WAqBM,GAnBIC,MAAA,CAAAC,MAAM,kB,cADdC,YAAA,CAMWf,MAAA;;MAJTgB,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEjB,MAAA,CAAAkB;;wBACT,MAEDC,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;UAEaN,MAAA,CAAAC,MAAM,iB,cADnBC,YAAA,CAKWf,MAAA;;MAHRiB,OAAK,EAAEjB,MAAA,CAAAoB;;wBACT,MAEDD,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;6CAEQN,MAAA,CAAAC,MAAM,iB,cADdC,YAAA,CAMWf,MAAA;;MAJTgB,IAAI,EAAC,SAAS;MACbC,OAAK,EAAEjB,MAAA,CAAAkB;;wBACT,MAEDC,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;;sBA1HJ,MAmGM,CAnGNR,mBAAA,CAmGM,OAnGNU,UAmGM,GAlGJC,mBAAA,UAAa,EACFT,MAAA,CAAAC,MAAM,kB,cAAjBjB,mBAAA,CA8CM,OA9CN0B,UA8CM,GA7CJZ,mBAAA,CAIM,OAJNa,UAIM,GAHJzB,YAAA,CAESC,MAAA;MAFDyB,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAA4B,CAA5B3B,YAAA,CAA4BC,MAAA,4B;;oCAGhCW,mBAAA,CAAe,YAAX,QAAM,qB,0BACVA,mBAAA,CAA2C;MAAxCf,KAAK,EAAC;IAAgB,GAAC,eAAa,qBAEvCe,mBAAA,CAaM,OAbNgB,UAaM,GAZJhB,mBAAA,CAGM,OAHNiB,UAGM,G,0BAFJjB,mBAAA,CAAgC;MAA1Bf,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBe,mBAAA,CAAkF,QAAlFkB,UAAkF,EAAAC,gBAAA,CAA3DjB,MAAA,CAAAkB,aAAa,qD,GAEtCpB,mBAAA,CAGM,OAHNqB,UAGM,G,0BAFJrB,mBAAA,CAAgC;MAA1Bf,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBe,mBAAA,CAAwC,QAAxCsB,UAAwC,EAApB,GAAC,GAAAH,gBAAA,CAAGjB,MAAA,CAAAqB,MAAM,iB,GAEhCvB,mBAAA,CAGM,OAHNwB,WAGM,G,0BAFJxB,mBAAA,CAAgC;MAA1Bf,KAAK,EAAC;IAAO,GAAC,OAAK,qBACzBe,mBAAA,CAAuD,QAAvDyB,WAAuD,EAAAN,gBAAA,CAAhC9B,MAAA,CAAAqC,UAAU,CAACxB,MAAA,CAAAyB,UAAU,kB,KAIhD3B,mBAAA,CAaM,OAbN4B,WAaM,GAZJxC,YAAA,CAWUC,MAAA;MAXDK,KAAK,EAAC,SAAS;MAACW,IAAI,EAAC,SAAS;MAACT,KAAuB,EAAvB;QAAA;MAAA;;wBACtC,MASM,CATNI,mBAAA,CASM,OATN6B,WASM,GARJ7B,mBAAA,CAGM,OAHN8B,WAGM,G,0BAFJ9B,mBAAA,CAAgC;QAA1Bf,KAAK,EAAC;MAAO,GAAC,OAAK,qBACzBe,mBAAA,CAA2C,QAA3C+B,WAA2C,EAAAZ,gBAAA,CAApB9B,MAAA,CAAA2C,UAAU,iB,GAEnChC,mBAAA,CAGM,OAHNiC,WAGM,G,0BAFJjC,mBAAA,CAAgC;QAA1Bf,KAAK,EAAC;MAAO,GAAC,OAAK,qBACzBe,mBAAA,CAA+C,QAA/CkC,WAA+C,EAAAf,gBAAA,CAAxB9B,MAAA,CAAA8C,cAAc,iB;;UAM7CnC,mBAAA,CAMM,OANNoC,WAMM,GALJhD,YAAA,CAIUC,MAAA;MAJDK,KAAK,EAAC,MAAM;MAACW,IAAI,EAAC,SAAS;MAACT,KAAyB,EAAzB;QAAA;MAAA;;wBACnC,MAA0BY,MAAA,QAAAA,MAAA,OAA1BR,mBAAA,CAA0B,WAAvB,qBAAmB,oBACtBA,mBAAA,CAAuB,WAApB,kBAAgB,oBACnBA,mBAAA,CAA2B,WAAxB,sBAAoB,mB;;;cAMbE,MAAA,CAAAC,MAAM,iB,cAAtBjB,mBAAA,CAmBMmD,SAAA;MAAAC,GAAA;IAAA,IApBN3B,mBAAA,UAAa,EACbX,mBAAA,CAmBM,OAnBNuC,WAmBM,GAlBJvC,mBAAA,CAIM,OAJNwC,WAIM,GAHJpD,YAAA,CAESC,MAAA;MAFDyB,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAAwB,CAAxB3B,YAAA,CAAwBC,MAAA,wB;;sCAG5BW,mBAAA,CAAe,YAAX,QAAM,qBACVA,mBAAA,CAAmE,KAAnEyC,WAAmE,EAAAtB,gBAAA,CAAtCjB,MAAA,CAAAwC,YAAY,qCAEzC1C,mBAAA,CASM,OATN2C,WASM,GARJvD,YAAA,CAOUC,MAAA;MAPDK,KAAK,EAAC,OAAO;MAACW,IAAI,EAAC,OAAO;MAACT,KAAuB,EAAvB;QAAA;MAAA;;wBAClC,MAKKY,MAAA,QAAAA,MAAA,OALLR,mBAAA,CAKK,aAJHA,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAiB,YAAb,UAAQ,GACZA,mBAAA,CAAe,YAAX,QAAM,GACVA,mBAAA,CAAiB,YAAb,UAAQ,E;;;+DAOJE,MAAA,CAAAC,MAAM,qB,cAAtBjB,mBAAA,CAyBMmD,SAAA;MAAAC,GAAA;IAAA,IA1BN3B,mBAAA,WAAc,EACdX,mBAAA,CAyBM,OAzBN4C,WAyBM,GAxBJ5C,mBAAA,CAEM,OAFN6C,WAEM,GADJzD,YAAA,CAAuBC,MAAA;MAAfyB,IAAI,EAAC;IAAO,G,+BAEtBd,mBAAA,CAAa,YAAT,MAAI,qB,4BACRA,mBAAA,CAA6C;MAA1Cf,KAAK,EAAC;IAAgB,GAAC,iBAAe,qBAEzCe,mBAAA,CAiBM,OAjBN8C,WAiBM,GAhBJ9C,mBAAA,CAGM;MAHDf,KAAK,EAAA8D,eAAA,EAAC,MAAM;QAAAC,MAAA,EAAmB3D,MAAA,CAAA4D,WAAW;MAAA;QAC7C7D,YAAA,CAA2CC,MAAA;MAAnCyB,IAAI,EAAC;IAAI;wBAAC,MAAgB,CAAhB1B,YAAA,CAAgBC,MAAA,iB;;oCAClCW,mBAAA,CAAmB,cAAb,QAAM,oB,kBAEdA,mBAAA,CAGM;MAHDf,KAAK,EAAA8D,eAAA,EAAC,MAAM;QAAAC,MAAA,EAAmB3D,MAAA,CAAA4D,WAAW;MAAA;QAC7C7D,YAAA,CAA6CC,MAAA;MAArCyB,IAAI,EAAC;IAAI;wBAAC,MAAkB,CAAlB1B,YAAA,CAAkBC,MAAA,mB;;oCACpCW,mBAAA,CAAiB,cAAX,MAAI,oB,kBAEZA,mBAAA,CAGM;MAHDf,KAAK,EAAA8D,eAAA,EAAC,MAAM;QAAAC,MAAA,EAAmB3D,MAAA,CAAA4D,WAAW;MAAA;QAC7C7D,YAAA,CAA6CC,MAAA;MAArCyB,IAAI,EAAC;IAAI;wBAAC,MAAkB,CAAlB1B,YAAA,CAAkBC,MAAA,mB;;oCACpCW,mBAAA,CAAmB,cAAb,QAAM,oB,kBAEdA,mBAAA,CAGM;MAHDf,KAAK,EAAA8D,eAAA,EAAC,MAAM;QAAAC,MAAA,EAAmB3D,MAAA,CAAA4D,WAAW;MAAA;QAC7C7D,YAAA,CAAuDC,MAAA;MAA/CyB,IAAI,EAAC;IAAI;wBAAC,MAA4B,CAA5B1B,YAAA,CAA4BC,MAAA,4B;;oCAC9CW,mBAAA,CAAiB,cAAX,MAAI,oB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}