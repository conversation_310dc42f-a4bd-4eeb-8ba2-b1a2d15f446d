const { connectDB } = require('./config/db');
const { User } = require('./models');

async function testModels() {
  try {
    console.log('正在连接数据库...');
    await connectDB();
    console.log('数据库连接成功');
    
    console.log('正在测试User模型...');
    
    // 测试查询用户
    const user = await User.findOne({
      where: {
        username: '<EMAIL>'
      }
    });
    
    if (user) {
      console.log('用户查询成功:', {
        id: user.id,
        username: user.username,
        email: user.email,
        level1_verified: user.level1_verified,
        level2_verified: user.level2_verified
      });
    } else {
      console.log('未找到用户');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testModels();
