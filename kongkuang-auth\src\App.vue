<template>
  <n-config-provider :theme="currentTheme" :theme-overrides="themeOverrides">
    <n-message-provider>
      <n-dialog-provider>
        <n-loading-bar-provider>
          <!-- 全局错误处理组件 -->
          <error-handler ref="errorHandlerRef" />
        
        <router-view v-slot="{ Component, route }">
          <template v-if="isAuthRoute(route)">
            <component :is="Component" />
          </template>
          <app-layout v-else>
            <component :is="Component" />
          </app-layout>
        </router-view>
        
        <!-- 主题切换按钮 -->
        <div class="theme-switch">
          <n-button circle @click="toggleTheme">
            <template #icon>
              <n-icon>
                <dark-mode-icon v-if="isDarkMode" />
                <light-mode-icon v-else />
              </n-icon>
            </template>
          </n-button>
        </div>
        </n-loading-bar-provider>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script>
import { defineComponent, ref, computed, onMounted, provide } from 'vue'
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NLoadingBarProvider,
  NButton,
  NIcon,
  darkTheme
} from 'naive-ui'
import AppLayout from './components/AppLayout.vue'
import PageScrollbar from './components/PageScrollbar.vue'
import { SunnyOutline as LightModeIcon, MoonOutline as DarkModeIcon } from '@vicons/ionicons5'
import ErrorHandler from './components/ErrorHandler.vue'

export default defineComponent({
  name: 'App',
  components: {
    NConfigProvider,
    NMessageProvider,
    NDialogProvider,
    NLoadingBarProvider,
    NButton,
    NIcon,
    AppLayout,
    PageScrollbar,
    LightModeIcon,
    DarkModeIcon,
    ErrorHandler
  },
  setup() {
    const themeMode = ref(localStorage.getItem('theme') || 'system')
    const isDarkSystem = ref(window.matchMedia('(prefers-color-scheme: dark)').matches)

    const isDarkMode = computed(() => {
      if (themeMode.value === 'system') return isDarkSystem.value
      return themeMode.value === 'dark'
    })

    const currentTheme = computed(() => (isDarkMode.value ? darkTheme : null))

    onMounted(() => {
      const mql = window.matchMedia('(prefers-color-scheme: dark)')
      mql.addEventListener('change', (e) => {
        isDarkSystem.value = e.matches
      })
    })

    const setTheme = (mode) => {
      themeMode.value = mode
      localStorage.setItem('theme', mode)
    }

    const toggleTheme = () => {
      if (themeMode.value === 'system') {
        setTheme(isDarkSystem.value ? 'light' : 'dark')
      } else {
        setTheme(themeMode.value === 'dark' ? 'light' : 'dark')
      }
    }

    const baseThemeOverrides = {
      common: {
        borderRadius: '12px',
        borderRadiusSmall: '8px',
        primaryColor: '#2080f0',
        primaryColorHover: '#4098fc',
        primaryColorPressed: '#1060c9',
        primaryColorSuppl: '#4098fc',
      },
      Button: {
        borderRadius: '8px',
        borderRadiusSmall: '6px',
      },
      Card: {
        borderRadius: '16px',
      },
      Input: {
        borderRadius: '8px',
      },
      Menu: {
        borderRadius: '16px',
        itemBorderRadius: '16px',
      },
    }

    const themeOverrides = computed(() => {
      if (isDarkMode.value) {
        return {
          ...baseThemeOverrides,
          common: {
          ...baseThemeOverrides.common,
          bodyColor: '#101014',
          cardColor: '#2a2a30', // 深色模式下卡片为灰色
          textColor1: 'rgba(255, 255, 255, 0.9)',
          textColor2: 'rgba(255, 255, 255, 0.7)',
          textColor3: 'rgba(255, 255, 255, 0.5)',
          dividerColor: 'rgba(255, 255, 255, 0.12)',
          hoverColor: 'rgba(255, 255, 255, 0.08)',
        },
          Layout: {
            color: '#101014',
            headerColor: '#1a1a1f',
            footerColor: 'transparent',
            siderColor: '#1a1a1f',
          },
          Menu: {
            itemTextColorHorizontal: 'rgba(255, 255, 255, 0.9)',
            itemTextColorHoverHorizontal: 'rgba(255, 255, 255, 1)',
            itemTextColorActiveHorizontal: '#2080f0',
            itemIconColorActiveHorizontal: '#2080f0',
            itemIconColorHorizontal: 'rgba(255, 255, 255, 0.9)',
            itemIconColorHoverHorizontal: 'rgba(255, 255, 255, 1)'
          },
          Card: {
            color: '#2a2a30', // 确保深色模式下卡片为灰色
            colorModal: '#2a2a30',
            colorPopover: '#2a2a30',
            colorEmbedded: '#2a2a30',
            colorTarget: '#2a2a30'
          },
        }
      }
      return {
        ...baseThemeOverrides,
        common: {
          ...baseThemeOverrides.common,
          bodyColor: '#f5f7fa',
          cardColor: '#ffffff', // 浅色模式下卡片为纯白色
          textColor1: 'rgba(0, 0, 0, 0.9)',
          textColor2: 'rgba(0, 0, 0, 0.7)',
          textColor3: 'rgba(0, 0, 0, 0.45)',
          dividerColor: 'rgba(0, 0, 0, 0.09)',
          hoverColor: 'rgba(0, 0, 0, 0.04)',
        },
        Layout: {
          color: '#f5f7fa',
          headerColor: '#ffffff',
          footerColor: 'transparent',
          siderColor: '#ffffff',
        },
        Card: {
          color: '#ffffff', // 确保浅色模式下卡片为纯白色
          colorModal: '#ffffff',
          colorPopover: '#ffffff',
          colorEmbedded: '#ffffff',
          colorTarget: '#ffffff'
        },
      }
    })
    
    const isAuthRoute = (route) => {
      const authRoutes = ['/login', '/register', '/forgot-password', '/reset-password', '/verify-email']
      return authRoutes.includes(route.path)
    }
    
    const errorHandlerRef = ref(null)
    
    provide('$$app', {
      setTheme,
      themeMode,
    })
    
    return {
      isDarkMode,
      currentTheme,
      toggleTheme,
      themeOverrides,
      isAuthRoute,
      errorHandlerRef
    }
  }
})
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
}

#app {
  height: 100%;
}

* {
  box-sizing: border-box;
}

/* 主题切换按钮 */
.theme-switch {
  position: fixed;
  right: 24px;
  bottom: 24px;
  z-index: 1000;
}

.theme-switch .n-button {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 全局强制覆盖导航菜单样式 */
.n-menu-item-content {
  border-radius: 16px !important;
}

.n-menu-item-content::before {
  border-radius: 16px !important;
}

.n-menu-item:hover .n-menu-item-content,
.n-menu-item--selected .n-menu-item-content {
  border-radius: 16px !important;
}
</style>
