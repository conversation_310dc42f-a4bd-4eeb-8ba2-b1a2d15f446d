/**
 * 支付路由 - 处理识脸支付认证
 */

const express = require('express');
const router = express.Router();
const { AlipayFaceAuthService, WechatFaceAuthService } = require('../services/paymentService');
const auth = require('../middlewares/auth');

// 支付宝识脸支付页面（开发环境模拟）
router.get('/alipay/face-auth', async (req, res) => {
  try {
    const { orderId } = req.query;
    
    if (!orderId) {
      return res.status(400).send('缺少订单ID');
    }
    
    // 模拟支付宝识脸支付页面
    const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>支付宝识脸支付</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
                background: linear-gradient(135deg, #1677ff, #69c0ff);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 12px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                max-width: 400px;
                width: 100%;
            }
            .logo {
                width: 60px;
                height: 60px;
                background: #1677ff;
                border-radius: 12px;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
            h1 {
                color: #1677ff;
                margin-bottom: 10px;
                font-size: 24px;
            }
            .order-info {
                background: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                font-size: 14px;
                color: #666;
            }
            .face-area {
                border: 2px dashed #1677ff;
                border-radius: 8px;
                padding: 40px 20px;
                margin: 20px 0;
                background: #f8f9ff;
            }
            .face-icon {
                font-size: 48px;
                color: #1677ff;
                margin-bottom: 10px;
            }
            .btn {
                background: #1677ff;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                margin: 10px;
                transition: background 0.3s;
            }
            .btn:hover {
                background: #0958d9;
            }
            .btn-success {
                background: #52c41a;
            }
            .btn-success:hover {
                background: #389e0d;
            }
            .status {
                margin-top: 20px;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            .status.success {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">支</div>
            <h1>支付宝识脸支付</h1>
            <p>请进行人脸识别以完成实名认证</p>
            
            <div class="order-info">
                <div>订单号: ${orderId}</div>
                <div>认证金额: ¥1.00</div>
                <div>认证类型: 一级实名认证</div>
            </div>
            
            <div class="face-area">
                <div class="face-icon">👤</div>
                <div>请将面部对准摄像头</div>
                <div style="font-size: 12px; color: #999; margin-top: 10px;">
                    系统将自动识别您的身份信息
                </div>
            </div>
            
            <button class="btn" onclick="startFaceAuth()">开始识脸认证</button>
            <button class="btn btn-success" onclick="simulateSuccess()" style="display: none;" id="successBtn">
                模拟认证成功
            </button>
            
            <div id="status"></div>
        </div>
        
        <script>
            function startFaceAuth() {
                document.getElementById('status').innerHTML = 
                    '<div class="status">正在启动摄像头...</div>';
                
                setTimeout(() => {
                    document.getElementById('status').innerHTML = 
                        '<div class="status">请保持面部在框内，正在识别...</div>';
                    document.getElementById('successBtn').style.display = 'inline-block';
                }, 2000);
            }
            
            function simulateSuccess() {
                document.getElementById('status').innerHTML = 
                    '<div class="status success">✓ 识脸认证成功！正在获取实名信息...</div>';
                
                setTimeout(() => {
                    // 关闭窗口并通知父页面
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'FACE_AUTH_SUCCESS',
                            orderId: '${orderId}',
                            paymentMethod: 'alipay'
                        }, '*');
                        window.close();
                    } else {
                        alert('识脸认证成功！请返回原页面查看结果。');
                    }
                }, 1500);
            }
        </script>
    </body>
    </html>
    `;
    
    res.send(html);
  } catch (error) {
    console.error('支付宝识脸支付页面错误:', error);
    res.status(500).send('页面加载失败');
  }
});

// 微信识脸支付页面（开发环境模拟）
router.get('/wechat/face-auth', async (req, res) => {
  try {
    const { orderId } = req.query;
    
    if (!orderId) {
      return res.status(400).send('缺少订单ID');
    }
    
    // 模拟微信识脸支付页面
    const html = `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>微信识脸支付</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: linear-gradient(135deg, #07c160, #38d9a9);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                border-radius: 12px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                max-width: 400px;
                width: 100%;
            }
            .logo {
                width: 60px;
                height: 60px;
                background: #07c160;
                border-radius: 12px;
                margin: 0 auto 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                font-weight: bold;
            }
            h1 {
                color: #07c160;
                margin-bottom: 10px;
                font-size: 24px;
            }
            .order-info {
                background: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                font-size: 14px;
                color: #666;
            }
            .face-area {
                border: 2px dashed #07c160;
                border-radius: 8px;
                padding: 40px 20px;
                margin: 20px 0;
                background: #f0fff4;
            }
            .face-icon {
                font-size: 48px;
                color: #07c160;
                margin-bottom: 10px;
            }
            .btn {
                background: #07c160;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 6px;
                font-size: 16px;
                cursor: pointer;
                margin: 10px;
                transition: background 0.3s;
            }
            .btn:hover {
                background: #059a4f;
            }
            .btn-success {
                background: #52c41a;
            }
            .btn-success:hover {
                background: #389e0d;
            }
            .status {
                margin-top: 20px;
                padding: 10px;
                border-radius: 6px;
                font-weight: bold;
            }
            .status.success {
                background: #f6ffed;
                color: #52c41a;
                border: 1px solid #b7eb8f;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="logo">微</div>
            <h1>微信识脸支付</h1>
            <p>请进行人脸识别以完成实名认证</p>
            
            <div class="order-info">
                <div>订单号: ${orderId}</div>
                <div>认证金额: ¥1.00</div>
                <div>认证类型: 一级实名认证</div>
            </div>
            
            <div class="face-area">
                <div class="face-icon">👤</div>
                <div>请将面部对准摄像头</div>
                <div style="font-size: 12px; color: #999; margin-top: 10px;">
                    系统将自动识别您的身份信息
                </div>
            </div>
            
            <button class="btn" onclick="startFaceAuth()">开始识脸认证</button>
            <button class="btn btn-success" onclick="simulateSuccess()" style="display: none;" id="successBtn">
                模拟认证成功
            </button>
            
            <div id="status"></div>
        </div>
        
        <script>
            function startFaceAuth() {
                document.getElementById('status').innerHTML = 
                    '<div class="status">正在启动摄像头...</div>';
                
                setTimeout(() => {
                    document.getElementById('status').innerHTML = 
                        '<div class="status">请保持面部在框内，正在识别...</div>';
                    document.getElementById('successBtn').style.display = 'inline-block';
                }, 2000);
            }
            
            function simulateSuccess() {
                document.getElementById('status').innerHTML = 
                    '<div class="status success">✓ 识脸认证成功！正在获取实名信息...</div>';
                
                setTimeout(() => {
                    // 关闭窗口并通知父页面
                    if (window.opener) {
                        window.opener.postMessage({
                            type: 'FACE_AUTH_SUCCESS',
                            orderId: '${orderId}',
                            paymentMethod: 'wechat'
                        }, '*');
                        window.close();
                    } else {
                        alert('识脸认证成功！请返回原页面查看结果。');
                    }
                }, 1500);
            }
        </script>
    </body>
    </html>
    `;
    
    res.send(html);
  } catch (error) {
    console.error('微信识脸支付页面错误:', error);
    res.status(500).send('页面加载失败');
  }
});

module.exports = router;
