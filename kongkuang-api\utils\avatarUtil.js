/**
 * 头像工具函数
 */

// 默认头像 - 简单的用户图标SVG，使用Base64编码
const defaultAvatarSvg = `
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#1890ff" width="128" height="128">
  <path d="M0 0h24v24H0z" fill="none"/>
  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
</svg>`;

// 将SVG转换为Base64编码的Data URL
const defaultAvatar = `data:image/svg+xml;base64,${Buffer.from(defaultAvatarSvg).toString('base64')}`;

// 生成随机颜色的头像
const generateColoredAvatar = (username) => {
  // 从用户名生成一个简单的哈希值，用于确定颜色
  let hash = 0;
  for (let i = 0; i < username.length; i++) {
    hash = username.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  // 将哈希值转换为HSL颜色
  const h = Math.abs(hash % 360);
  const s = 70; // 饱和度固定为70%
  const l = 60; // 亮度固定为60%
  
  // 创建带有随机颜色的SVG
  const coloredSvg = `
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="128" height="128">
    <circle cx="50" cy="50" r="50" fill="hsl(${h}, ${s}%, ${l}%)" />
    <text x="50" y="62" font-size="50" text-anchor="middle" fill="white" font-family="Arial, sans-serif">
      ${username.charAt(0).toUpperCase()}
    </text>
  </svg>`;
  
  return `data:image/svg+xml;base64,${Buffer.from(coloredSvg).toString('base64')}`;
};

// 获取默认头像
const getDefaultAvatar = (username) => {
  // 在开发环境中，返回一个简单的URL而不是Base64编码
  if (username) {
    // 使用Gravatar风格的默认头像URL
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(username.charAt(0))}&background=1890ff&color=fff&size=128`;
  }
  return 'https://ui-avatars.com/api/?name=U&background=1890ff&color=fff&size=128';
};

module.exports = {
  defaultAvatar,
  generateColoredAvatar,
  getDefaultAvatar
}; 