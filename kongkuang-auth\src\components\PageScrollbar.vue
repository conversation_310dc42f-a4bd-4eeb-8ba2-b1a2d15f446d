<template>
  <n-scrollbar class="page-scrollbar" :style="scrollbarStyle">
    <slot></slot>
  </n-scrollbar>
</template>

<script>
import { defineComponent, computed } from 'vue'
import { NScrollbar } from 'naive-ui'

export default defineComponent({
  name: 'PageScrollbar',
  components: {
    NScrollbar
  },
  props: {
    height: {
      type: String,
      default: '100vh'
    }
  },
  setup(props) {
    const scrollbarStyle = computed(() => ({
      height: props.height,
      width: '100%'
    }))

    return {
      scrollbarStyle
    }
  }
})
</script>

<style scoped>
.page-scrollbar {
  height: 100vh;
  width: 100%;
}

:deep(.n-scrollbar-content) {
  min-height: 100%;
}
</style>