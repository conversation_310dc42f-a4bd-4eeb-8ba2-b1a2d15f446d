{"ast": null, "code": "import { defineComponent, computed } from 'vue';\nimport { NScrollbar } from 'naive-ui';\nexport default defineComponent({\n  name: 'PageScrollbar',\n  components: {\n    NScrollbar\n  },\n  props: {\n    height: {\n      type: String,\n      default: '100vh'\n    }\n  },\n  setup(props) {\n    const scrollbarStyle = computed(() => ({\n      height: props.height,\n      width: '100%'\n    }));\n    return {\n      scrollbarStyle\n    };\n  }\n});", "map": {"version": 3, "names": ["defineComponent", "computed", "NScrollbar", "name", "components", "props", "height", "type", "String", "default", "setup", "scrollbarStyle", "width"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\PageScrollbar.vue"], "sourcesContent": ["<template>\n  <n-scrollbar class=\"page-scrollbar\" :style=\"scrollbarStyle\">\n    <slot></slot>\n  </n-scrollbar>\n</template>\n\n<script>\nimport { defineComponent, computed } from 'vue'\nimport { NScrollbar } from 'naive-ui'\n\nexport default defineComponent({\n  name: 'PageScrollbar',\n  components: {\n    NScrollbar\n  },\n  props: {\n    height: {\n      type: String,\n      default: '100vh'\n    }\n  },\n  setup(props) {\n    const scrollbarStyle = computed(() => ({\n      height: props.height,\n      width: '100%'\n    }))\n\n    return {\n      scrollbarStyle\n    }\n  }\n})\n</script>\n\n<style scoped>\n.page-scrollbar {\n  height: 100vh;\n  width: 100%;\n}\n\n:deep(.n-scrollbar-content) {\n  min-height: 100%;\n}\n</style>"], "mappings": "AAOA,SAASA,eAAe,EAAEC,QAAO,QAAS,KAAI;AAC9C,SAASC,UAAS,QAAS,UAAS;AAEpC,eAAeF,eAAe,CAAC;EAC7BG,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVF;EACF,CAAC;EACDG,KAAK,EAAE;IACLC,MAAM,EAAE;MACNC,IAAI,EAAEC,MAAM;MACZC,OAAO,EAAE;IACX;EACF,CAAC;EACDC,KAAKA,CAACL,KAAK,EAAE;IACX,MAAMM,cAAa,GAAIV,QAAQ,CAAC,OAAO;MACrCK,MAAM,EAAED,KAAK,CAACC,MAAM;MACpBM,KAAK,EAAE;IACT,CAAC,CAAC;IAEF,OAAO;MACLD;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}