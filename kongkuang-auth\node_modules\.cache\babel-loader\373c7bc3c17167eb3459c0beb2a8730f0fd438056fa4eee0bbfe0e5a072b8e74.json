{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, onMounted, computed } from 'vue';\nimport { NSpin, NGrid, NGi, NCard, NAlert, NDescriptions, NDescriptionsItem, NButton, NTag, NList, NListItem, NIcon, NEmpty, useMessage } from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport { ShieldOutline, LockClosedOutline, MailOutline, PhonePortraitOutline, KeyOutline, CheckmarkCircleOutline, CloseCircleOutline } from '@vicons/ionicons5';\nexport default {\n  __name: 'Dashboard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const userStore = useUserStore();\n    const message = useMessage();\n    const recentApps = ref([]);\n\n    // 安全指标数据\n    const securityItems = computed(() => [{\n      key: 'password',\n      name: '登录密码',\n      status: true,\n      // 用户已设置密码\n      icon: KeyOutline\n    }, {\n      key: 'email',\n      name: '邮箱验证',\n      status: userStore.user?.is_email_verified || false,\n      icon: MailOutline\n    }, {\n      key: 'phone',\n      name: '手机验证',\n      status: userStore.user?.is_phone_verified || false,\n      icon: PhonePortraitOutline\n    }, {\n      key: 'realname',\n      name: '实名认证',\n      status: userStore.user?.level2_verified || false,\n      icon: ShieldOutline\n    }, {\n      key: 'mfa',\n      name: '双因子认证',\n      status: userStore.user?.security_mfa_enabled || false,\n      icon: LockClosedOutline\n    }]);\n\n    // 检查是否为深色模式\n    const isDarkMode = computed(() => {\n      const themeMode = localStorage.getItem('theme') || 'system';\n      if (themeMode === 'system') {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      return themeMode === 'dark';\n    });\n    const formatDateTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      // Using toLocaleString for a more standard format, customize as needed\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).replace(/\\//g, '-');\n    };\n\n    // 计算安全分数\n    const getSecurityScore = () => {\n      const totalItems = securityItems.value.length;\n      const completedItems = securityItems.value.filter(item => item.status).length;\n      return Math.round(completedItems / totalItems * 100);\n    };\n\n    // 获取安全等级\n    const getSecurityLevel = () => {\n      const score = getSecurityScore();\n      if (score >= 90) return '优秀';\n      if (score >= 70) return '良好';\n      if (score >= 50) return '一般';\n      return '较低';\n    };\n\n    // 获取安全等级颜色\n    const getSecurityColor = () => {\n      const score = getSecurityScore();\n      if (score >= 90) return '#18a058'; // 绿色\n      if (score >= 70) return '#2080f0'; // 蓝色\n      if (score >= 50) return '#f0a020'; // 橙色\n      return '#d03050'; // 红色\n    };\n\n    // 计算SVG弧线参数\n    const getSecurityDashArray = () => {\n      const circumference = 2 * Math.PI * 90; // 半径90的圆周长\n      return `${circumference} ${circumference}`;\n    };\n    const getSecurityDashOffset = () => {\n      const circumference = 2 * Math.PI * 90;\n      const score = getSecurityScore();\n      return circumference - score / 100 * circumference;\n    };\n\n    // 获取安全建议\n    const getSecuritySuggestions = () => {\n      return securityItems.value.filter(item => !item.status).map(item => item.name);\n    };\n    const fetchDashboardData = async () => {\n      loading.value = true;\n      try {\n        const apiClient = getApiClient();\n        const response = await apiClient.get('/dashboard');\n        if (response.data && response.data.success) {\n          // 更新用户信息，使用后端返回的格式化数据\n          if (response.data.user) {\n            userStore.user = {\n              ...userStore.user,\n              ...response.data.user,\n              // 使用后端返回的格式化时间，如果没有则使用原始数据\n              createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n              lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n              last_login: response.data.user.last_login,\n              lastLoginIp: response.data.user.lastLoginIp,\n              level2_verified: response.data.user.level2_verified\n            };\n          }\n\n          // 更新应用列表\n          recentApps.value = response.data.recentApps || [];\n          console.log('仪表盘数据加载成功:', response.data);\n        }\n      } catch (error) {\n        console.error(\"Failed to fetch dashboard data:\", error);\n        message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        loading.value = false;\n      }\n    };\n    onMounted(() => {\n      // We need user data, if not present, maybe fetch it or rely on login flow\n      if (!userStore.user) {\n        // This case might happen on a page refresh, you might want to fetch user data\n        // For now, we assume user data is populated from login\n      }\n      fetchDashboardData();\n    });\n    const __returned__ = {\n      loading,\n      userStore,\n      message,\n      recentApps,\n      securityItems,\n      isDarkMode,\n      formatDateTime,\n      getSecurityScore,\n      getSecurityLevel,\n      getSecurityColor,\n      getSecurityDashArray,\n      getSecurityDashOffset,\n      getSecuritySuggestions,\n      fetchDashboardData,\n      ref,\n      onMounted,\n      computed,\n      get NSpin() {\n        return NSpin;\n      },\n      get NGrid() {\n        return NGrid;\n      },\n      get NGi() {\n        return NGi;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NDescriptions() {\n        return NDescriptions;\n      },\n      get NDescriptionsItem() {\n        return NDescriptionsItem;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NList() {\n        return NList;\n      },\n      get NListItem() {\n        return NListItem;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NEmpty() {\n        return NEmpty;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      },\n      get ShieldOutline() {\n        return ShieldOutline;\n      },\n      get LockClosedOutline() {\n        return LockClosedOutline;\n      },\n      get MailOutline() {\n        return MailOutline;\n      },\n      get PhonePortraitOutline() {\n        return PhonePortraitOutline;\n      },\n      get KeyOutline() {\n        return KeyOutline;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "NSpin", "NGrid", "NGi", "NCard", "N<PERSON><PERSON><PERSON>", "NDescriptions", "NDescriptionsItem", "NButton", "NTag", "NList", "NListItem", "NIcon", "NEmpty", "useMessage", "useUserStore", "getApiClient", "ShieldOutline", "LockClosedOutline", "MailOutline", "PhonePortraitOutline", "KeyOutline", "CheckmarkCircleOutline", "CloseCircleOutline", "loading", "userStore", "message", "recentApps", "securityItems", "key", "name", "status", "icon", "user", "is_email_verified", "is_phone_verified", "level2_verified", "security_mfa_enabled", "isDarkMode", "themeMode", "localStorage", "getItem", "window", "matchMedia", "matches", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "getSecurityScore", "totalItems", "value", "length", "completedItems", "filter", "item", "Math", "round", "getSecurityLevel", "score", "getSecurityColor", "getSecurityDashArray", "circumference", "PI", "getSecurityDashOffset", "getSecuritySuggestions", "map", "fetchDashboardData", "apiClient", "response", "get", "data", "success", "createdAt", "registrationTime", "formatted", "lastLoginAt", "lastLoginTime", "last_login", "lastLoginIp", "console", "log", "error"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <n-card title=\"可使用 KongKuang ID 登录的服务\" :bordered=\"false\" class=\"right-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                  <n-list :show-divider=\"false\">\n                    <n-list-item v-for=\"app in recentApps\" :key=\"app.id\" class=\"service-item\">\n                       {{ app.name }}\n                    </n-list-item>\n                     <n-empty v-if=\"!recentApps || recentApps.length === 0\" description=\"暂无服务\" />\n                  </n-list>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card title=\"账户安全指标\" :bordered=\"false\" class=\"right-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"security-dashboard\">\n                  <!-- 方向盘样式的安全指标 -->\n                  <div class=\"steering-wheel\">\n                    <svg viewBox=\"0 0 200 200\" class=\"wheel-svg\">\n                      <!-- 外圈 -->\n                      <circle cx=\"100\" cy=\"100\" r=\"90\" fill=\"none\" :stroke=\"isDarkMode ? '#404040' : '#e0e0e0'\" stroke-width=\"4\"/>\n\n                      <!-- 安全等级弧线 -->\n                      <circle\n                        cx=\"100\"\n                        cy=\"100\"\n                        r=\"90\"\n                        fill=\"none\"\n                        :stroke=\"getSecurityColor()\"\n                        stroke-width=\"8\"\n                        stroke-linecap=\"round\"\n                        :stroke-dasharray=\"getSecurityDashArray()\"\n                        :stroke-dashoffset=\"getSecurityDashOffset()\"\n                        transform=\"rotate(-90 100 100)\"\n                        class=\"security-arc\"\n                      />\n\n                      <!-- 方向盘辐条 -->\n                      <g :stroke=\"isDarkMode ? '#606060' : '#c0c0c0'\" stroke-width=\"3\">\n                        <line x1=\"100\" y1=\"30\" x2=\"100\" y2=\"70\" />\n                        <line x1=\"100\" y1=\"130\" x2=\"100\" y2=\"170\" />\n                        <line x1=\"30\" y1=\"100\" x2=\"70\" y2=\"100\" />\n                        <line x1=\"130\" y1=\"100\" x2=\"170\" y2=\"100\" />\n                      </g>\n\n                      <!-- 中心圆 -->\n                      <circle cx=\"100\" cy=\"100\" r=\"25\" :fill=\"getSecurityColor()\" opacity=\"0.2\"/>\n                      <circle cx=\"100\" cy=\"100\" r=\"25\" fill=\"none\" :stroke=\"getSecurityColor()\" stroke-width=\"2\"/>\n\n                      <!-- 安全等级文字 -->\n                      <text x=\"100\" y=\"95\" text-anchor=\"middle\" :fill=\"getSecurityColor()\" font-size=\"12\" font-weight=\"bold\">\n                        {{ getSecurityLevel() }}\n                      </text>\n                      <text x=\"100\" y=\"110\" text-anchor=\"middle\" :fill=\"getSecurityColor()\" font-size=\"10\">\n                        {{ getSecurityScore() }}%\n                      </text>\n                    </svg>\n                  </div>\n\n                  <!-- 安全项目列表 -->\n                  <div class=\"security-items\">\n                    <div class=\"security-item\" v-for=\"item in securityItems\" :key=\"item.key\">\n                      <div class=\"item-icon\">\n                        <n-icon :size=\"16\" :color=\"item.status ? '#18a058' : '#d03050'\">\n                          <component :is=\"item.icon\" />\n                        </n-icon>\n                      </div>\n                      <div class=\"item-content\">\n                        <div class=\"item-name\">{{ item.name }}</div>\n                        <div class=\"item-status\" :class=\"{ 'status-ok': item.status, 'status-warning': !item.status }\">\n                          {{ item.status ? '已启用' : '未启用' }}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 安全建议 -->\n                  <div class=\"security-suggestion\" v-if=\"getSecurityScore() < 100\">\n                    <n-alert type=\"warning\" :bordered=\"false\" size=\"small\">\n                      <template #icon>\n                        <n-icon><shield-outline /></n-icon>\n                      </template>\n                      建议完善{{ getSecuritySuggestions().join('、') }}以提高账户安全性\n                    </n-alert>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  ShieldOutline,\n  LockClosedOutline,\n  MailOutline,\n  PhonePortraitOutline,\n  KeyOutline,\n  CheckmarkCircleOutline,\n  CloseCircleOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全指标数据\nconst securityItems = computed(() => [\n  {\n    key: 'password',\n    name: '登录密码',\n    status: true, // 用户已设置密码\n    icon: KeyOutline\n  },\n  {\n    key: 'email',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    icon: MailOutline\n  },\n  {\n    key: 'phone',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    icon: PhonePortraitOutline\n  },\n  {\n    key: 'realname',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    icon: ShieldOutline\n  },\n  {\n    key: 'mfa',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    icon: LockClosedOutline\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 计算安全分数\nconst getSecurityScore = () => {\n  const totalItems = securityItems.value.length;\n  const completedItems = securityItems.value.filter(item => item.status).length;\n  return Math.round((completedItems / totalItems) * 100);\n};\n\n// 获取安全等级\nconst getSecurityLevel = () => {\n  const score = getSecurityScore();\n  if (score >= 90) return '优秀';\n  if (score >= 70) return '良好';\n  if (score >= 50) return '一般';\n  return '较低';\n};\n\n// 获取安全等级颜色\nconst getSecurityColor = () => {\n  const score = getSecurityScore();\n  if (score >= 90) return '#18a058'; // 绿色\n  if (score >= 70) return '#2080f0'; // 蓝色\n  if (score >= 50) return '#f0a020'; // 橙色\n  return '#d03050'; // 红色\n};\n\n// 计算SVG弧线参数\nconst getSecurityDashArray = () => {\n  const circumference = 2 * Math.PI * 90; // 半径90的圆周长\n  return `${circumference} ${circumference}`;\n};\n\nconst getSecurityDashOffset = () => {\n  const circumference = 2 * Math.PI * 90;\n  const score = getSecurityScore();\n  return circumference - (score / 100) * circumference;\n};\n\n// 获取安全建议\nconst getSecuritySuggestions = () => {\n  return securityItems.value\n    .filter(item => !item.status)\n    .map(item => item.name);\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n.side-cards {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n  height: 100%;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 安全指标卡片样式 */\n.security-card {\n  margin-top: 16px;\n}\n\n.security-dashboard {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 16px;\n}\n\n.steering-wheel {\n  width: 120px;\n  height: 120px;\n  position: relative;\n}\n\n.wheel-svg {\n  width: 100%;\n  height: 100%;\n  transform: rotate(0deg);\n}\n\n.security-arc {\n  transition: stroke-dashoffset 0.6s ease-in-out;\n}\n\n.security-items {\n  width: 100%;\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.security-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 6px 8px;\n  border-radius: 4px;\n  background-color: var(--n-color-target);\n  transition: all 0.3s ease;\n}\n\n.security-item:hover {\n  background-color: var(--n-color-target-hover);\n}\n\n.item-icon {\n  flex-shrink: 0;\n}\n\n.item-content {\n  flex: 1;\n  min-width: 0;\n}\n\n.item-name {\n  font-size: 12px;\n  font-weight: 500;\n  color: var(--n-text-color-1);\n}\n\n.item-status {\n  font-size: 11px;\n  margin-top: 2px;\n}\n\n.status-ok {\n  color: var(--n-success-color);\n}\n\n.status-warning {\n  color: var(--n-warning-color);\n}\n\n.security-suggestion {\n  width: 100%;\n  margin-top: 8px;\n}\n\n/* 响应式优化 */\n@media (max-width: 768px) {\n  .steering-wheel {\n    width: 100px;\n    height: 100px;\n  }\n\n  .security-item {\n    padding: 4px 6px;\n  }\n\n  .item-name {\n    font-size: 11px;\n  }\n\n  .item-status {\n    font-size: 10px;\n  }\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n</style>"], "mappings": ";;;AAsJA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SACEC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,UAAS,QACJ,UAAU;AACjB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SACEC,aAAa,EACbC,iBAAiB,EACjBC,WAAW,EACXC,oBAAoB,EACpBC,UAAU,EACVC,sBAAsB,EACtBC,kBAAiB,QACZ,mBAAmB;;;;;;;IAE1B,MAAMC,OAAO,GAAG1B,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAM2B,SAAS,GAAGV,YAAY,CAAC,CAAC;IAChC,MAAMW,OAAO,GAAGZ,UAAU,CAAC,CAAC;IAE5B,MAAMa,UAAU,GAAG7B,GAAG,CAAC,EAAE,CAAC;;IAE1B;IACA,MAAM8B,aAAa,GAAG5B,QAAQ,CAAC,MAAM,CACnC;MACE6B,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAE,IAAI;MAAE;MACdC,IAAI,EAAEX;IACR,CAAC,EACD;MACEQ,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEN,SAAS,CAACQ,IAAI,EAAEC,iBAAiB,IAAI,KAAK;MAClDF,IAAI,EAAEb;IACR,CAAC,EACD;MACEU,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEN,SAAS,CAACQ,IAAI,EAAEE,iBAAiB,IAAI,KAAK;MAClDH,IAAI,EAAEZ;IACR,CAAC,EACD;MACES,GAAG,EAAE,UAAU;MACfC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEN,SAAS,CAACQ,IAAI,EAAEG,eAAe,IAAI,KAAK;MAChDJ,IAAI,EAAEf;IACR,CAAC,EACD;MACEY,GAAG,EAAE,KAAK;MACVC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEN,SAAS,CAACQ,IAAI,EAAEI,oBAAoB,IAAI,KAAK;MACrDL,IAAI,EAAEd;IACR,EACD,CAAC;;IAEF;IACA,MAAMoB,UAAU,GAAGtC,QAAQ,CAAC,MAAM;MAChC,MAAMuC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ;MAC3D,IAAIF,SAAS,KAAK,QAAQ,EAAE;QAC1B,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAClE;MACA,OAAOL,SAAS,KAAK,MAAM;IAC7B,CAAC,CAAC;IAEF,MAAMM,cAAc,GAAIC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC;MACA,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACxB,CAAC;;IAED;IACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAMC,UAAU,GAAG/B,aAAa,CAACgC,KAAK,CAACC,MAAM;MAC7C,MAAMC,cAAc,GAAGlC,aAAa,CAACgC,KAAK,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjC,MAAM,CAAC,CAAC8B,MAAM;MAC7E,OAAOI,IAAI,CAACC,KAAK,CAAEJ,cAAc,GAAGH,UAAU,GAAI,GAAG,CAAC;IACxD,CAAC;;IAED;IACA,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAMC,KAAK,GAAGV,gBAAgB,CAAC,CAAC;MAChC,IAAIU,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;MAC7B,MAAMD,KAAK,GAAGV,gBAAgB,CAAC,CAAC;MAChC,IAAIU,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,OAAO,SAAS,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;MACjC,MAAMC,aAAa,GAAG,CAAC,GAAGN,IAAI,CAACO,EAAE,GAAG,EAAE,CAAC,CAAC;MACxC,OAAO,GAAGD,aAAa,IAAIA,aAAa,EAAE;IAC5C,CAAC;IAED,MAAME,qBAAqB,GAAGA,CAAA,KAAM;MAClC,MAAMF,aAAa,GAAG,CAAC,GAAGN,IAAI,CAACO,EAAE,GAAG,EAAE;MACtC,MAAMJ,KAAK,GAAGV,gBAAgB,CAAC,CAAC;MAChC,OAAOa,aAAa,GAAIH,KAAK,GAAG,GAAG,GAAIG,aAAa;IACtD,CAAC;;IAED;IACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;MACnC,OAAO9C,aAAa,CAACgC,KAAI,CACtBG,MAAM,CAACC,IAAI,IAAI,CAACA,IAAI,CAACjC,MAAM,EAC3B4C,GAAG,CAACX,IAAI,IAAIA,IAAI,CAAClC,IAAI,CAAC;IAC3B,CAAC;IAED,MAAM8C,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrCpD,OAAO,CAACoC,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMiB,SAAS,GAAG7D,YAAY,CAAC,CAAC;QAChC,MAAM8D,QAAQ,GAAG,MAAMD,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;QAElD,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UAC1C;UACA,IAAIH,QAAQ,CAACE,IAAI,CAAC/C,IAAI,EAAE;YACtBR,SAAS,CAACQ,IAAI,GAAG;cACf,GAAGR,SAAS,CAACQ,IAAI;cACjB,GAAG6C,QAAQ,CAACE,IAAI,CAAC/C,IAAI;cACrB;cACAiD,SAAS,EAAEJ,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACkD,gBAAgB,EAAEC,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACiD,SAAS;cACzFG,WAAW,EAAEP,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACqD,aAAa,EAAEF,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACoD,WAAW;cAC1FE,UAAU,EAAET,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACsD,UAAU;cACzCC,WAAW,EAAEV,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACuD,WAAW;cAC3CpD,eAAe,EAAE0C,QAAQ,CAACE,IAAI,CAAC/C,IAAI,CAACG;YACtC,CAAC;UACH;;UAEA;UACAT,UAAU,CAACiC,KAAK,GAAGkB,QAAQ,CAACE,IAAI,CAACrD,UAAU,IAAI,EAAE;UAEjD8D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOW,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDjE,OAAO,CAACiE,KAAK,CAAC,aAAa,IAAIA,KAAK,CAACb,QAAQ,EAAEE,IAAI,EAAEtD,OAAO,IAAIiE,KAAK,CAACjE,OAAO,CAAC,CAAC;MACjF,CAAC,SAAS;QACRF,OAAO,CAACoC,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAEG7D,SAAS,CAAC,MAAM;MAClB;MACA,IAAI,CAAC0B,SAAS,CAACQ,IAAI,EAAE;QACnB;QACA;MAAA;MAEF2C,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}