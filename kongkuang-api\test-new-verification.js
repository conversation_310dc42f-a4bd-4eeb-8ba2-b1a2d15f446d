const { connectDB } = require('./config/db');
const { User } = require('./models');
const level1Cache = require('./utils/level1Cache');

async function testNewVerificationSystem() {
  try {
    await connectDB();
    console.log('数据库连接成功');
    
    // 测试用户ID
    const testUserId = 1;
    
    console.log('\n=== 测试新认证系统 ===');
    
    // 1. 测试一级认证缓存
    console.log('\n1. 测试一级认证缓存...');
    
    // 模拟一级认证数据
    const level1Data = {
      verified: true,
      verifiedAt: new Date(),
      paymentMethod: 'alipay',
      realName: '张三',
      idNumber: '110101199001011234',
      orderId: 'L1_test_123',
      amount: 1.2
    };
    
    // 设置一级认证状态
    level1Cache.setLevel1Status(testUserId, level1Data);
    console.log('✓ 一级认证状态已设置');
    
    // 获取一级认证状态
    const level1Status = level1Cache.getLevel1Status(testUserId);
    console.log('一级认证状态:', level1Status ? '已认证' : '未认证');
    
    // 获取一级认证信息（脱敏）
    const level1Info = level1Cache.getLevel1Info(testUserId);
    console.log('一级认证信息:', level1Info);
    
    // 2. 测试二级认证数据库存储
    console.log('\n2. 测试二级认证数据库存储...');
    
    const user = await User.findByPk(testUserId);
    if (user) {
      console.log('用户信息:');
      console.log('- ID:', user.id);
      console.log('- 用户名:', user.username);
      console.log('- 二级认证状态:', user.level2_verified);
      console.log('- 真实姓名:', user.real_name);
      console.log('- 身份证号:', user.id_number ? user.id_number.replace(/(.{6}).*(.{4})/, '$1****$2') : null);
      console.log('- 认证时间:', user.level2_verified_at);
      
      if (user.level2_verification_data) {
        const verificationData = JSON.parse(user.level2_verification_data);
        console.log('- 认证详情:', {
          verifiedAt: verificationData.verifiedAt,
          apiProvider: verificationData.apiProvider,
          confidence: verificationData.confidence
        });
      }
    }
    
    // 3. 测试缓存统计
    console.log('\n3. 缓存统计信息...');
    const stats = level1Cache.getCacheStats();
    console.log('缓存统计:', stats);
    
    // 4. 测试认证状态检查
    console.log('\n4. 认证状态检查...');
    console.log('一级认证状态:', level1Cache.isLevel1Verified(testUserId));
    console.log('二级认证状态:', user ? user.level2_verified : false);
    
    // 5. 测试缓存过期（模拟）
    console.log('\n5. 测试缓存管理...');
    console.log('当前缓存大小:', level1Cache.getCacheStats().total);
    
    // 清理测试缓存
    level1Cache.clearLevel1Status(testUserId);
    console.log('测试缓存已清理');
    console.log('清理后缓存大小:', level1Cache.getCacheStats().total);
    
    console.log('\n=== 测试完成 ===');
    console.log('✓ 一级认证：使用内存缓存，不持久化');
    console.log('✓ 二级认证：高强度存储到数据库');
    console.log('✓ 数据安全：敏感信息脱敏处理');
    console.log('✓ 缓存管理：自动过期和清理');
    
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testNewVerificationSystem();
