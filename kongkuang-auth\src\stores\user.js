import { defineStore } from 'pinia'
import axios from 'axios'
import { getApiClient } from '../utils/api'
import { getAuthConfig } from '../utils/config'

// 获取API客户端
const apiClient = getApiClient();

// 获取认证配置
const getAuth = () => getAuthConfig();

// 用户状态管理
export const useUserStore = defineStore('user', {
  state: () => {
    const auth = getAuth();
    return {
      token: localStorage.getItem(auth.tokenKey) || '',
      user: JSON.parse(localStorage.getItem(auth.userKey) || 'null'),
      loading: false,
      error: null
    };
  },
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    currentUser: (state) => state.user,
    isLoading: (state) => state.loading,
    hasError: (state) => !!state.error,
    authHeader: (state) => ({ Authorization: `Bearer ${state.token}` })
  },
  
  actions: {
    // 设置认证头
    setAuthHeader() {
      axios.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
    },
    
    // 清除认证头
    clearAuthHeader() {
      delete axios.defaults.headers.common['Authorization']
    },
    
    // 保存令牌和用户信息
    saveAuth(token, user) {
      const auth = getAuth();
      this.token = token;
      this.user = user;
      localStorage.setItem(auth.tokenKey, token);
      localStorage.setItem(auth.userKey, JSON.stringify(user));
      this.setAuthHeader();
    },
    
    // 清除认证信息
    clearAuth() {
      const auth = getAuth();
      this.token = '';
      this.user = null;
      localStorage.removeItem(auth.tokenKey);
      localStorage.removeItem(auth.userKey);
      this.clearAuthHeader();
    },
    
    // 注册
    async register(userData) {
      this.loading = true;
      this.error = null;
      
      try {
        // 根据注册类型处理请求路径
        let endpoint = '/auth/register';
        if (userData.registerType === 'phone') {
          endpoint = '/auth/register-by-phone';
        } else if (userData.registerType === 'email') {
          endpoint = '/auth/register-by-email';
        }
        
        const response = await apiClient.post(endpoint, userData);
        const { token, user } = response.data;
        this.saveAuth(token, user);
        return response.data;
      } catch (error) {
        this.loading = false;
        console.error('注册错误:', error);

        // 根据错误状态码提供更具体的错误信息
        if (error.response?.status === 400) {
          const message = error.response.data?.message;
          if (message?.includes('用户名已存在')) {
            this.error = '用户名已被使用，请选择其他用户名';
          } else if (message?.includes('邮箱已被注册')) {
            this.error = '该邮箱已被注册，请使用其他邮箱或直接登录';
          } else if (message?.includes('验证码')) {
            this.error = '验证码无效或已过期，请重新获取验证码';
          } else {
            this.error = message || '注册信息有误，请检查后重试';
          }
        } else {
          this.error = error.response?.data?.message || '注册失败，请稍后重试';
        }

        throw error;
      }
    },
    
    // 登录
    async login(credentials) {
      try {
        this.loading = true;
        const response = await getApiClient().post('/auth/login', {
          username: credentials.account,
          password: credentials.password
        });
        
        if (response.data.success) {
          const { token, user } = response.data;
          this.saveAuth(token, user);
          return response.data;
        } else {
          this.loading = false;
          this.error = response.data.message || '登录失败';
          throw new Error(response.data.message || '登录失败');
        }
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '登录失败';
        throw error;
      }
    },
    
    // 退出登录
    async logout() {
      try {
        await apiClient.post('/auth/logout');
      } catch (error) {
        console.error('Logout error:', error);
      } finally {
        this.clearAuth();
      }
    },
    
    // 获取用户资料
    async fetchUserProfile() {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.get('/users/profile');
        this.user = response.data.user;
        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '获取用户资料失败';
        
        // 如果是认证错误，登出
        if (error.response?.status === 401) {
          this.logout();
        }
        
        throw error;
      }
    },
    
    // 更新用户资料
    async updateProfile(profileData) {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.put('/users/profile', profileData);
        this.user = response.data.user;
        localStorage.setItem(getAuth().userKey, JSON.stringify(this.user));
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '更新用户资料失败';
        throw error;
      }
    },
    
    // 更新密码
    async updatePassword(passwordData) {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.put('/auth/password', passwordData);
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '更新密码失败';
        throw error;
      }
    },
    
    // 请求重置密码
    async forgotPassword(email) {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.post('/auth/forgot-password', { email });
        
        this.loading = false;
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '请求重置密码失败';
        throw error;
      }
    },
    
    // 重置密码
    async resetPassword(token, password) {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.post('/auth/reset-password/' + token, { password });
        
        this.loading = false;
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '重置密码失败';
        throw error;
      }
    },
    
    // 验证邮箱
    async verifyEmail(token) {
      this.loading = true;
      this.error = null;
      
      try {
        const response = await apiClient.get('/auth/verify-email/' + token);
        
        this.loading = false;
        return response.data;
      } catch (error) {
        this.loading = false;
        this.error = error.response?.data?.message || '邮箱验证失败';
        throw error;
      }
    }
  }
}) 