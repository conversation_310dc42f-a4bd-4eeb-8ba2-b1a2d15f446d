{"ast": null, "code": "import { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue';\nimport { NButton, NIcon } from 'naive-ui';\nimport { ChevronBack, ChevronForward } from '@vicons/ionicons5';\nexport default defineComponent({\n  name: 'AnnouncementBanner',\n  components: {\n    NButton,\n    NIcon,\n    ChevronBack,\n    ChevronForward\n  },\n  setup() {\n    const currentIndex = ref(0);\n    const autoPlayTimer = ref(null);\n\n    // 公告数据\n    const announcements = ref([{\n      title: '欢迎使用空旷账户',\n      description: '感谢您选择空旷账户中心，我们致力于为您提供安全、便捷的身份认证服务。'\n    }, {\n      title: '共同创造良好的网络环境',\n      description: '请遵守相关法律法规，文明上网，共同维护健康的网络环境。'\n    }]);\n\n    // 当前公告\n    const currentAnnouncement = computed(() => {\n      return announcements.value[currentIndex.value] || announcements.value[0];\n    });\n\n    // 下一个公告\n    const nextAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = (currentIndex.value + 1) % announcements.value.length;\n      }\n    };\n\n    // 上一个公告\n    const previousAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = currentIndex.value === 0 ? announcements.value.length - 1 : currentIndex.value - 1;\n      }\n    };\n\n    // 跳转到指定公告\n    const goToAnnouncement = index => {\n      currentIndex.value = index;\n    };\n\n    // 自动播放\n    const startAutoPlay = () => {\n      if (announcements.value.length > 1) {\n        autoPlayTimer.value = setInterval(() => {\n          nextAnnouncement();\n        }, 5000); // 5秒切换一次\n      }\n    };\n\n    // 停止自动播放\n    const stopAutoPlay = () => {\n      if (autoPlayTimer.value) {\n        clearInterval(autoPlayTimer.value);\n        autoPlayTimer.value = null;\n      }\n    };\n    onMounted(() => {\n      startAutoPlay();\n    });\n    onUnmounted(() => {\n      stopAutoPlay();\n    });\n    return {\n      announcements,\n      currentIndex,\n      currentAnnouncement,\n      nextAnnouncement,\n      previousAnnouncement,\n      goToAnnouncement\n    };\n  }\n});", "map": {"version": 3, "names": ["defineComponent", "ref", "computed", "onMounted", "onUnmounted", "NButton", "NIcon", "ChevronBack", "ChevronForward", "name", "components", "setup", "currentIndex", "autoPlayTimer", "announcements", "title", "description", "currentAnnouncement", "value", "nextAnnouncement", "length", "previousAnnouncement", "goToAnnouncement", "index", "startAutoPlay", "setInterval", "stopAutoPlay", "clearInterval"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\AnnouncementBanner.vue"], "sourcesContent": ["<template>\n  <div class=\"announcement-banner\">\n    <div class=\"banner-container\">\n      <!-- 左右切换按钮 -->\n      <n-button \n        circle \n        quaternary \n        size=\"small\" \n        class=\"nav-button nav-button-left\"\n        @click=\"previousAnnouncement\"\n        :disabled=\"announcements.length <= 1\"\n      >\n        <template #icon>\n          <n-icon><chevron-back /></n-icon>\n        </template>\n      </n-button>\n      \n      <!-- 公告内容 -->\n      <div class=\"announcement-content\">\n        <div class=\"announcement-text\">\n          <h3 class=\"announcement-title\">{{ currentAnnouncement.title }}</h3>\n          <p class=\"announcement-description\">{{ currentAnnouncement.description }}</p>\n        </div>\n      </div>\n      \n      <!-- 右切换按钮 -->\n      <n-button \n        circle \n        quaternary \n        size=\"small\" \n        class=\"nav-button nav-button-right\"\n        @click=\"nextAnnouncement\"\n        :disabled=\"announcements.length <= 1\"\n      >\n        <template #icon>\n          <n-icon><chevron-forward /></n-icon>\n        </template>\n      </n-button>\n    </div>\n    \n    <!-- 指示器 -->\n    <div class=\"indicators\" v-if=\"announcements.length > 1\">\n      <div \n        v-for=\"(_, index) in announcements\" \n        :key=\"index\"\n        class=\"indicator\"\n        :class=\"{ active: index === currentIndex }\"\n        @click=\"goToAnnouncement(index)\"\n      ></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue'\nimport { NButton, NIcon } from 'naive-ui'\nimport { ChevronBack, ChevronForward } from '@vicons/ionicons5'\n\nexport default defineComponent({\n  name: 'AnnouncementBanner',\n  components: {\n    NButton,\n    NIcon,\n    ChevronBack,\n    ChevronForward\n  },\n  setup() {\n    const currentIndex = ref(0)\n    const autoPlayTimer = ref(null)\n    \n    // 公告数据\n    const announcements = ref([\n      {\n        title: '欢迎使用空旷账户',\n        description: '感谢您选择空旷账户中心，我们致力于为您提供安全、便捷的身份认证服务。'\n      },\n      {\n        title: '共同创造良好的网络环境',\n        description: '请遵守相关法律法规，文明上网，共同维护健康的网络环境。'\n      }\n    ])\n    \n    // 当前公告\n    const currentAnnouncement = computed(() => {\n      return announcements.value[currentIndex.value] || announcements.value[0]\n    })\n    \n    // 下一个公告\n    const nextAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = (currentIndex.value + 1) % announcements.value.length\n      }\n    }\n    \n    // 上一个公告\n    const previousAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = currentIndex.value === 0 \n          ? announcements.value.length - 1 \n          : currentIndex.value - 1\n      }\n    }\n    \n    // 跳转到指定公告\n    const goToAnnouncement = (index) => {\n      currentIndex.value = index\n    }\n    \n    // 自动播放\n    const startAutoPlay = () => {\n      if (announcements.value.length > 1) {\n        autoPlayTimer.value = setInterval(() => {\n          nextAnnouncement()\n        }, 5000) // 5秒切换一次\n      }\n    }\n    \n    // 停止自动播放\n    const stopAutoPlay = () => {\n      if (autoPlayTimer.value) {\n        clearInterval(autoPlayTimer.value)\n        autoPlayTimer.value = null\n      }\n    }\n    \n    onMounted(() => {\n      startAutoPlay()\n    })\n    \n    onUnmounted(() => {\n      stopAutoPlay()\n    })\n    \n    return {\n      announcements,\n      currentIndex,\n      currentAnnouncement,\n      nextAnnouncement,\n      previousAnnouncement,\n      goToAnnouncement\n    }\n  }\n})\n</script>\n\n<style scoped>\n.announcement-banner {\n  position: relative;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 24px;\n  color: white;\n  overflow: hidden;\n}\n\n.announcement-banner::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"10\" cy=\"60\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"90\" cy=\"40\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  pointer-events: none;\n}\n\n.banner-container {\n  display: flex;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.announcement-content {\n  flex: 1;\n  text-align: center;\n  padding: 0 40px;\n}\n\n.announcement-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.announcement-description {\n  font-size: 14px;\n  margin: 0;\n  opacity: 0.9;\n  line-height: 1.5;\n}\n\n.nav-button {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 2;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  transition: all 0.3s ease;\n}\n\n.nav-button:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-50%) scale(1.1);\n}\n\n.nav-button-left {\n  left: 10px;\n}\n\n.nav-button-right {\n  right: 10px;\n}\n\n.indicators {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n  margin-top: 16px;\n  position: relative;\n  z-index: 1;\n}\n\n.indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.4);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.indicator.active {\n  background: white;\n  transform: scale(1.2);\n}\n\n.indicator:hover {\n  background: rgba(255, 255, 255, 0.7);\n}\n\n/* 深色模式适配 */\n:deep(.n-config-provider.n-config-provider--dark) .announcement-banner {\n  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .announcement-banner {\n    padding: 16px;\n    margin-bottom: 16px;\n  }\n  \n  .announcement-content {\n    padding: 0 30px;\n  }\n  \n  .announcement-title {\n    font-size: 20px;\n  }\n  \n  .announcement-description {\n    font-size: 13px;\n  }\n  \n  .nav-button-left {\n    left: 8px;\n  }\n  \n  .nav-button-right {\n    right: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .announcement-content {\n    padding: 0 25px;\n  }\n  \n  .announcement-title {\n    font-size: 18px;\n  }\n  \n  .announcement-description {\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": "AAsDA,SAASA,eAAe,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAU,QAAS,KAAI;AAC3E,SAASC,OAAO,EAAEC,KAAI,QAAS,UAAS;AACxC,SAASC,WAAW,EAAEC,cAAa,QAAS,mBAAkB;AAE9D,eAAeR,eAAe,CAAC;EAC7BS,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVL,OAAO;IACPC,KAAK;IACLC,WAAW;IACXC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,YAAW,GAAIX,GAAG,CAAC,CAAC;IAC1B,MAAMY,aAAY,GAAIZ,GAAG,CAAC,IAAI;;IAE9B;IACA,MAAMa,aAAY,GAAIb,GAAG,CAAC,CACxB;MACEc,KAAK,EAAE,UAAU;MACjBC,WAAW,EAAE;IACf,CAAC,EACD;MACED,KAAK,EAAE,aAAa;MACpBC,WAAW,EAAE;IACf,EACD;;IAED;IACA,MAAMC,mBAAkB,GAAIf,QAAQ,CAAC,MAAM;MACzC,OAAOY,aAAa,CAACI,KAAK,CAACN,YAAY,CAACM,KAAK,KAAKJ,aAAa,CAACI,KAAK,CAAC,CAAC;IACzE,CAAC;;IAED;IACA,MAAMC,gBAAe,GAAIA,CAAA,KAAM;MAC7B,IAAIL,aAAa,CAACI,KAAK,CAACE,MAAK,GAAI,CAAC,EAAE;QAClCR,YAAY,CAACM,KAAI,GAAI,CAACN,YAAY,CAACM,KAAI,GAAI,CAAC,IAAIJ,aAAa,CAACI,KAAK,CAACE,MAAK;MAC3E;IACF;;IAEA;IACA,MAAMC,oBAAmB,GAAIA,CAAA,KAAM;MACjC,IAAIP,aAAa,CAACI,KAAK,CAACE,MAAK,GAAI,CAAC,EAAE;QAClCR,YAAY,CAACM,KAAI,GAAIN,YAAY,CAACM,KAAI,KAAM,IACxCJ,aAAa,CAACI,KAAK,CAACE,MAAK,GAAI,IAC7BR,YAAY,CAACM,KAAI,GAAI;MAC3B;IACF;;IAEA;IACA,MAAMI,gBAAe,GAAKC,KAAK,IAAK;MAClCX,YAAY,CAACM,KAAI,GAAIK,KAAI;IAC3B;;IAEA;IACA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1B,IAAIV,aAAa,CAACI,KAAK,CAACE,MAAK,GAAI,CAAC,EAAE;QAClCP,aAAa,CAACK,KAAI,GAAIO,WAAW,CAAC,MAAM;UACtCN,gBAAgB,CAAC;QACnB,CAAC,EAAE,IAAI,GAAE;MACX;IACF;;IAEA;IACA,MAAMO,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIb,aAAa,CAACK,KAAK,EAAE;QACvBS,aAAa,CAACd,aAAa,CAACK,KAAK;QACjCL,aAAa,CAACK,KAAI,GAAI,IAAG;MAC3B;IACF;IAEAf,SAAS,CAAC,MAAM;MACdqB,aAAa,CAAC;IAChB,CAAC;IAEDpB,WAAW,CAAC,MAAM;MAChBsB,YAAY,CAAC;IACf,CAAC;IAED,OAAO;MACLZ,aAAa;MACbF,YAAY;MACZK,mBAAmB;MACnBE,gBAAgB;MAChBE,oBAAoB;MACpBC;IACF;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}