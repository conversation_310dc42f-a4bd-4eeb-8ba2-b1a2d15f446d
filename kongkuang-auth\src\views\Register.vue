<template>
  <div class="register-container" :style="{ backgroundColor: bgColor }">
    <page-scrollbar class="register-scrollbar">
      <div class="register-content">
        <n-card class="register-card">
          <div class="card-header">
            <div class="logo-container">
              <img src="/logo.png" alt="Logo" class="logo" />
            </div>
            <h2 class="title">创建账户</h2>
          </div>
          
          <!-- 注册方式选择 -->
          <div class="register-type">
            <n-tabs v-model:value="registerType" type="line" animated>
              <n-tab-pane name="phone" tab="手机号注册">
                <n-form
                  ref="phoneFormRef"
                  :model="phoneForm"
                  :rules="phoneRules"
                  label-placement="top"
                  @submit.prevent="handlePhoneSubmit"
                >
                  <n-form-item path="username" label="用户名">
                    <n-input
                      v-model:value="phoneForm.username"
                      placeholder="请输入用户名"
                      :disabled="loading"
                    >
                      <template #prefix>
                        <n-icon><person /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="phone" label="手机号">
                    <n-input-group>
                      <n-input
                        v-model:value="phoneForm.phone"
                        placeholder="请输入手机号码"
                        :disabled="loading || sendingCode"
                      >
                        <template #prefix>
                          <n-icon><call /></n-icon>
                        </template>
                      </n-input>
                      <n-button 
                        type="primary" 
                        ghost 
                        :disabled="!canSendCode || loading || sendingCode" 
                        :loading="sendingCode"
                        @click="handleSendVerificationCode('phone')"
                      >
                        {{ sendCodeButtonText }}
                      </n-button>
                    </n-input-group>
                  </n-form-item>
                  
                  <n-form-item path="code" label="验证码">
                    <n-input
                      v-model:value="phoneForm.code"
                      placeholder="请输入短信验证码"
                      :disabled="loading"
                    >
                      <template #prefix>
                        <n-icon><shield /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="password" label="密码">
                    <n-input
                      v-model:value="phoneForm.password"
                      type="password"
                      placeholder="请输入密码"
                      :disabled="loading"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <n-icon><lock-closed /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="confirmPassword" label="确认密码">
                    <n-input
                      v-model:value="phoneForm.confirmPassword"
                      type="password"
                      placeholder="请再次输入密码"
                      :disabled="loading"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <n-icon><lock-closed /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <div class="password-strength" v-if="phoneForm.password">
                    <div class="strength-label">密码强度:</div>
                    <n-progress
                      type="line"
                      :percentage="getPasswordStrength(phoneForm.password).percentage"
                      :color="getPasswordStrength(phoneForm.password).color"
                      :indicator-placement="'inside'"
                    />
                    <div class="strength-tips">
                      <n-tag :type="getPasswordStrength(phoneForm.password).type" size="small">
                        {{ getPasswordStrength(phoneForm.password).text }}
                      </n-tag>
                      <span class="tips-text">{{ getPasswordStrength(phoneForm.password).tips }}</span>
                    </div>
                  </div>
                  
                  <div class="form-agreement">
                    <n-checkbox v-model:checked="agreement">
                      我已阅读并同意
                      <n-button text type="primary" @click.stop="showAgreement('terms')">
                        服务条款
                      </n-button>
                      和
                      <n-button text type="primary" @click.stop="showAgreement('privacy')">
                        隐私政策
                      </n-button>
                    </n-checkbox>
                  </div>
                  
                  <div class="form-actions">
                    <n-button
                      type="primary"
                      block
                      :loading="loading"
                      :disabled="loading || !agreement || !isPhoneFormValid"
                      :class="{ 'button-disabled': !agreement || !isPhoneFormValid }"
                      @click="handlePhoneSubmit"
                    >
                      注册
                    </n-button>
                  </div>
                </n-form>
              </n-tab-pane>
              
              <n-tab-pane name="email" tab="邮箱注册">
                <n-form
                  ref="emailFormRef"
                  :model="emailForm"
                  :rules="emailRules"
                  label-placement="top"
                  @submit.prevent="handleEmailSubmit"
                >
                  <n-form-item path="username" label="用户名">
                    <n-input
                      v-model:value="emailForm.username"
                      placeholder="请输入用户名"
                      :disabled="loading"
                    >
                      <template #prefix>
                        <n-icon><person /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="email" label="邮箱">
                    <n-input-group>
                      <n-input
                        v-model:value="emailForm.email"
                        placeholder="请输入邮箱"
                        :disabled="loading || sendingCode"
                      >
                        <template #prefix>
                          <n-icon><mail /></n-icon>
                        </template>
                      </n-input>
                      <n-button 
                        type="primary" 
                        ghost 
                        :disabled="!canSendEmailCode || loading || sendingCode" 
                        :loading="sendingCode"
                        @click="handleSendVerificationCode('email')"
                      >
                        {{ sendCodeButtonText }}
                      </n-button>
                    </n-input-group>
                  </n-form-item>
                  
                  <n-form-item path="code" label="验证码">
                    <n-input
                      v-model:value="emailForm.code"
                      placeholder="请输入邮箱验证码"
                      :disabled="loading"
                    >
                      <template #prefix>
                        <n-icon><shield /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="password" label="密码">
                    <n-input
                      v-model:value="emailForm.password"
                      type="password"
                      placeholder="请输入密码"
                      :disabled="loading"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <n-icon><lock-closed /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <n-form-item path="confirmPassword" label="确认密码">
                    <n-input
                      v-model:value="emailForm.confirmPassword"
                      type="password"
                      placeholder="请再次输入密码"
                      :disabled="loading"
                      show-password-on="click"
                    >
                      <template #prefix>
                        <n-icon><lock-closed /></n-icon>
                      </template>
                    </n-input>
                  </n-form-item>
                  
                  <div class="password-strength" v-if="emailForm.password">
                    <div class="strength-label">密码强度:</div>
                    <n-progress
                      type="line"
                      :percentage="getPasswordStrength(emailForm.password).percentage"
                      :color="getPasswordStrength(emailForm.password).color"
                      :indicator-placement="'inside'"
                    />
                    <div class="strength-tips">
                      <n-tag :type="getPasswordStrength(emailForm.password).type" size="small">
                        {{ getPasswordStrength(emailForm.password).text }}
                      </n-tag>
                      <span class="tips-text">{{ getPasswordStrength(emailForm.password).tips }}</span>
                    </div>
                  </div>
                  
                  <div class="form-agreement">
                    <n-checkbox v-model:checked="agreement">
                      我已阅读并同意
                      <n-button text type="primary" @click.stop="showAgreement('terms')">
                        服务条款
                      </n-button>
                      和
                      <n-button text type="primary" @click.stop="showAgreement('privacy')">
                        隐私政策
                      </n-button>
                    </n-checkbox>
                  </div>
                  
                  <div class="form-actions">
                    <n-button
                      type="primary"
                      block
                      :loading="loading"
                      :disabled="loading || !agreement || !isEmailFormValid"
                      :class="{ 'button-disabled': !agreement || !isEmailFormValid }"
                      @click="handleEmailSubmit"
                    >
                      注册
                    </n-button>
                  </div>
                </n-form>
              </n-tab-pane>
            </n-tabs>
          </div>
          
          <div class="form-footer">
            <span>已有账号? </span>
            <n-button text @click="$router.push('/login')">
              立即登录
            </n-button>
          </div>
        </n-card>
      </div>
    </page-scrollbar>
    
    <!-- 服务条款对话框 -->
    <n-modal
      v-model:show="showTermsModal"
      preset="card"
      title="服务条款"
      style="width: 600px; max-width: 90vw"
    >
      <n-scrollbar style="max-height: 60vh">
        <div class="agreement-content">
          <h3>空旷账户中心服务条款</h3>
          <p>欢迎使用空旷账户中心服务！</p>
          <p>请仔细阅读以下条款，这些条款适用于您使用空旷账户中心提供的所有服务。使用我们的服务即表示您同意这些条款。</p>
          
          <h4>1. 服务描述</h4>
          <p>空旷账户中心提供统一的身份认证服务，允许用户通过一个账号访问空旷网络的所有服务和应用。</p>
          
          <h4>2. 账户责任</h4>
          <p>您需要对保护您的账户安全负责，包括保护密码和限制对您计算机的访问。您同意对您账户下的所有活动负责。</p>
          
          <h4>3. 隐私与数据</h4>
          <p>我们重视您的隐私。我们将根据隐私政策收集和使用您的信息，您可以随时查看我们的隐私政策。</p>
          
          <h4>4. 禁止行为</h4>
          <p>您不得使用我们的服务进行任何违法或滥用行为，包括但不限于散布垃圾邮件、恶意软件或进行网络攻击。</p>
          
          <h4>5. 服务变更与终止</h4>
          <p>我们可能会不时更改、暂停或终止服务的部分或全部内容。我们也可能根据自己的判断，随时停止向您提供服务。</p>
          
          <h4>6. 免责声明</h4>
          <p>我们的服务按"现状"提供，不提供任何明示或暗示的保证。</p>
          
          <h4>7. 责任限制</h4>
          <p>在法律允许的最大范围内，我们不对任何直接、间接、附带、特殊、后果性或惩罚性损害负责。</p>
          
          <h4>8. 条款变更</h4>
          <p>我们可能会不时更新这些条款。继续使用我们的服务表示您接受修改后的条款。</p>
        </div>
      </n-scrollbar>
      <template #footer>
        <n-button @click="showTermsModal = false">关闭</n-button>
      </template>
    </n-modal>
    
    <!-- 隐私政策对话框 -->
    <n-modal
      v-model:show="showPrivacyModal"
      preset="card"
      title="隐私政策"
      style="width: 600px; max-width: 90vw"
    >
      <n-scrollbar style="max-height: 60vh">
        <div class="agreement-content">
          <h3>空旷账户中心隐私政策</h3>
          <p>本隐私政策说明了我们如何收集、使用和保护您的个人信息。</p>
          
          <h4>1. 信息收集</h4>
          <p>我们收集的信息包括但不限于：您提供的个人信息（如姓名、电子邮件地址）、设备信息、日志信息和位置信息。</p>
          
          <h4>2. 信息使用</h4>
          <p>我们使用收集的信息来提供、维护和改进我们的服务，开发新的服务，保护我们的用户，以及根据法律要求提供信息。</p>
          
          <h4>3. 信息共享</h4>
          <p>我们不会与第三方共享您的个人信息，除非：获得您的同意、用于处理外部数据、出于法律原因、或在业务转让的情况下。</p>
          
          <h4>4. 信息安全</h4>
          <p>我们采取合理的安全措施来保护您的个人信息不被未经授权的访问、更改、披露或销毁。</p>
          
          <h4>5. 数据保留</h4>
          <p>我们会在必要的时间内保留您的个人信息，以实现本隐私政策中描述的目的，除非法律要求或允许更长的保留期。</p>
          
          <h4>6. 您的权利</h4>
          <p>您有权访问、更正、删除您的个人信息，以及限制或反对某些处理活动。</p>
          
          <h4>7. 儿童隐私</h4>
          <p>我们的服务不面向13岁以下的儿童。如果我们发现收集了13岁以下儿童的个人信息，我们会采取措施尽快删除这些信息。</p>
          
          <h4>8. 政策变更</h4>
          <p>我们可能会不时更新本隐私政策。我们会在网站上发布任何变更，并在进行重大变更时通知您。</p>
        </div>
      </n-scrollbar>
      <template #footer>
        <n-button @click="showPrivacyModal = false">关闭</n-button>
      </template>
    </n-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, inject, onUnmounted, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { 
  NCard, 
  NForm, 
  NFormItem, 
  NInput, 
  NInputGroup,
  NButton,
  NCheckbox,
  NProgress,
  NTag,
  NModal,
  NIcon,
  NScrollbar,
  NTabs,
  NTabPane,
  useMessage,
  useThemeVars
} from 'naive-ui'
import { 
  Person, 
  Mail,
  LockClosed,
  Call,
  Shield
} from '@vicons/ionicons5'
import { useUserStore } from '../stores/user'
import axios from 'axios'
import { handleFormSubmit, withErrorHandling } from '../utils/errorHandlers'
import { getApiClient } from '../utils/api'; // 导入getApiClient

export default defineComponent({
  name: 'RegisterPage',
  components: {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NInputGroup,
    NButton,
    NCheckbox,
    NProgress,
    NTag,
    NModal,
    NIcon,
    NScrollbar,
    NTabs,
    NTabPane,
    Person,
    Mail,
    LockClosed,
    Call,
    Shield
  },
  setup() {
    const router = useRouter()
    const message = useMessage()
    const userStore = useUserStore()
    const themeVars = useThemeVars()
    const app = inject('$$app') || {}
    const showError = app.showError || (() => {})
    
    // 表单引用
    const emailFormRef = ref(null)
    const phoneFormRef = ref(null)
    
    // 加载状态
    const loading = ref(false)
    const sendingCode = ref(false)
    
    // 协议同意
    const agreement = ref(false)
    
    // 模态框显示状态
    const showTermsModal = ref(false)
    const showPrivacyModal = ref(false)
    
    // 注册类型
    const registerType = ref('phone') // 默认手机号注册
    
    // 倒计时
    const countdown = ref(0)
    const timer = ref(null)
    const sendCodeButtonText = computed(() => {
      return countdown.value > 0 ? `${countdown.value}秒后重发` : '发送验证码'
    })
    
    // 表单数据
    const phoneForm = reactive({
      username: '',
      phone: '',
      code: '',
      password: '',
      confirmPassword: ''
    })
    
    const emailForm = reactive({
      username: '',
      email: '',
      code: '',
      password: '',
      confirmPassword: ''
    })
    
    // 判断是否可以发送验证码
    const canSendCode = computed(() => {
      return phoneForm.phone && /^1[3-9]\d{9}$/.test(phoneForm.phone) && countdown.value === 0
    })
    
    const canSendEmailCode = computed(() => {
      return emailForm.email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailForm.email) && countdown.value === 0
    })
    
    // 判断表单是否有效
    const isPhoneFormValid = computed(() => {
      return phoneForm.username && phoneForm.phone && phoneForm.code && phoneForm.password && 
        phoneForm.confirmPassword && (phoneForm.password === phoneForm.confirmPassword) &&
        /^1[3-9]\d{9}$/.test(phoneForm.phone) && 
        /^\d{4,6}$/.test(phoneForm.code)
    })
    
    const isEmailFormValid = computed(() => {
      return emailForm.username && emailForm.email && emailForm.code && emailForm.password && 
        emailForm.confirmPassword && (emailForm.password === emailForm.confirmPassword) &&
        /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailForm.email) && 
        /^\d{4,6}$/.test(emailForm.code)
    })
    
    // 强制重置状态
    const resetState = () => {
      countdown.value = 0;
      sendingCode.value = false;
      if (timer.value) {
        clearInterval(timer.value);
        timer.value = null;
      }
    }

    // 在组件挂载时重置状态
    onMounted(() => {
      resetState();
    });
    
    // 表单验证规则
    const phoneRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和连字符', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { pattern: /^\d{4,6}$/, message: '验证码格式不正确', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, message: '密码长度不能少于8个字符', trigger: 'blur' },
        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value) => {
            return value === phoneForm.password
          },
          message: '两次输入的密码不一致',
          trigger: 'blur'
        }
      ]
    }
    
    const emailRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度应在3-20个字符之间', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9_-]+$/, message: '用户名只能包含字母、数字、下划线和连字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入有效的邮箱地址', trigger: 'blur' }
      ],
      code: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { pattern: /^\d{4,6}$/, message: '验证码格式不正确', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 8, message: '密码长度不能少于8个字符', trigger: 'blur' },
        { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        {
          validator: (rule, value) => {
            return value === emailForm.password
          },
          message: '两次输入的密码不一致',
          trigger: 'blur'
        }
      ]
    }
    
    // 显示协议
    const showAgreement = (type) => {
      if (type === 'terms') {
        showTermsModal.value = true
      } else if (type === 'privacy') {
        showPrivacyModal.value = true
      }
    }
    
    // 发送验证码
    const handleSendVerificationCode = async (type) => {
      if ((type === 'phone' && !canSendCode.value) || 
          (type === 'email' && !canSendEmailCode.value) || 
          sendingCode.value) {
        return
      }
      
      const apiClient = getApiClient(); // 获取配置好的axios实例
      
      await withErrorHandling(
        async () => {
          // 发送验证码请求
          if (type === 'phone') {
            await apiClient.post('/auth/send-phone-code', { phone: phoneForm.phone })
            // 开始倒计时
            startCountdown()
            return `验证码已发送至手机 ${phoneForm.phone}`
          } else {
            await apiClient.post('/auth/send-email-code', { email: emailForm.email })
            // 开始倒计时
            startCountdown()
            return `验证码已发送至邮箱 ${emailForm.email}`
          }
        },
        {
          loading: sendingCode, // 将sendingCode传递给loading参数
          showMessage: message,
          showError,
          successMessage: type === 'phone' 
            ? `验证码已发送至手机 ${phoneForm.phone}` 
            : `验证码已发送至邮箱 ${emailForm.email}`,
          errorMessage: '发送验证码失败，请稍后重试'
        }
      )
    }
    
    // 倒计时函数
    const startCountdown = () => {
      countdown.value = 60
      if (timer.value) {
        clearInterval(timer.value)
      }
      
      timer.value = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer.value)
          timer.value = null
        }
      }, 1000)
    }
    
    // 密码强度计算
    const getPasswordStrength = (password) => {
      if (!password) {
        return {
          percentage: 0,
          color: '#c0c0c0',
          text: '无',
          tips: '请输入密码',
          type: 'default'
        }
      }
      
      let strength = 0
      let tips = []
      
      // 长度检查
      if (password.length >= 8) {
        strength += 20
      } else {
        tips.push('密码长度至少为8个字符')
      }
      
      // 包含数字
      if (/\d/.test(password)) {
        strength += 20
      } else {
        tips.push('包含数字')
      }
      
      // 包含小写字母
      if (/[a-z]/.test(password)) {
        strength += 20
      } else {
        tips.push('包含小写字母')
      }
      
      // 包含大写字母
      if (/[A-Z]/.test(password)) {
        strength += 20
      } else {
        tips.push('包含大写字母')
      }
      
      // 包含特殊字符
      if (/[^a-zA-Z0-9]/.test(password)) {
        strength += 20
      } else {
        tips.push('包含特殊字符')
      }
      
      // 强度等级
      let text = '弱'
      let color = '#d03050'
      let type = 'error'
      
      if (strength >= 80) {
        text = '强'
        color = '#18a058'
        type = 'success'
      } else if (strength >= 60) {
        text = '中'
        color = '#2080f0'
        type = 'info'
      } else if (strength >= 40) {
        text = '一般'
        color = '#f0a020'
        type = 'warning'
      }
      
      return {
        percentage: strength,
        color,
        text,
        tips: tips.length > 0 ? `建议: ${tips.join('、')}` : '密码强度良好',
        type
      }
    }
    
    // 检查协议
    const checkAgreement = () => {
      if (!agreement.value) {
        message.warning('请阅读并同意服务条款和隐私政策')
        return false
      }
      return true
    }
    
    // 手机号注册提交
    const handlePhoneSubmit = (e) => {
      e.preventDefault()
      
      if (!checkAgreement()) {
        return
      }
      
      handleFormSubmit(
        phoneFormRef,
        async () => {
          await userStore.register({
            username: phoneForm.username,
            phone: phoneForm.phone,
            code: phoneForm.code,
            password: phoneForm.password,
            registerType: 'phone'
          })
          
          router.push('/dashboard')
          return true
        },
        {
          loading,
          showMessage: message,
          showError,
          successMessage: '注册成功',
          errorMessage: '注册失败，请稍后重试',
          validateErrorMessage: '请填写完整的注册信息'
        }
      )
    }
    
    // 邮箱注册提交
    const handleEmailSubmit = (e) => {
      e.preventDefault()
      
      if (!checkAgreement()) {
        return
      }
      
      handleFormSubmit(
        emailFormRef,
        async () => {
          await userStore.register({
            username: emailForm.username,
            email: emailForm.email,
            code: emailForm.code,
            password: emailForm.password,
            registerType: 'email'
          })
          
          router.push('/dashboard')
          return true
        },
        {
          loading,
          showMessage: message,
          showError,
          successMessage: '注册成功',
          errorMessage: '注册失败，请稍后重试',
          validateErrorMessage: '请填写完整的注册信息'
        }
      )
    }
    
    // 背景色计算
    const bgColor = computed(() => {
      return themeVars.value.bodyColor
    })
    
    // 清理定时器
    onUnmounted(() => {
      if (timer.value) {
        clearInterval(timer.value)
        timer.value = null
      }
    })
    
    return {
      // 表单引用
      emailFormRef,
      phoneFormRef,
      
      // 表单数据
      emailForm,
      phoneForm,
      
      // 表单规则
      emailRules,
      phoneRules,
      
      // 状态
      loading,
      agreement,
      registerType,
      sendingCode,
      countdown,
      sendCodeButtonText,
      canSendCode,
      canSendEmailCode,
      isPhoneFormValid,
      isEmailFormValid,
      
      // 模态框
      showTermsModal,
      showPrivacyModal,
      
      // 方法
      showAgreement,
      handleSendVerificationCode,
      handlePhoneSubmit,
      handleEmailSubmit,
      getPasswordStrength,
      
      // 计算属性
      bgColor
    }
  }
})
</script>

<style scoped>
.register-container {
  height: 100vh;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.register-scrollbar {
  height: 100%;
  width: 100%;
}

.register-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 40px 20px;
}

.register-card {
  width: 450px;
  max-width: 95%;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transition: box-shadow 0.3s ease;
}

.register-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-header {
  text-align: center;
  margin-bottom: 24px;
}

.logo-container {
  margin-bottom: 16px;
}

.logo {
  height: 60px;
}

.title {
  margin: 0;
  font-size: 24px;
  font-weight: 500;
  color: var(--primary-color);
}

.register-type {
  margin-bottom: 24px;
}

/* 输入框动画效果 */
:deep(.n-input) {
  transition: all 0.3s ease;
}

:deep(.n-input:focus-within) {
  transform: translateY(-2px);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.password-strength {
  margin-top: 16px;
  margin-bottom: 16px;
}

.strength-label {
  font-size: 14px;
  margin-bottom: 4px;
}

.strength-tips {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.tips-text {
  margin-left: 8px;
  font-size: 12px;
  opacity: 0.45;
}

.form-agreement {
  margin-bottom: 24px;
}

.form-actions {
  margin-bottom: 16px;
}

.form-footer {
  text-align: center;
  margin-bottom: 16px;
}

.agreement-content {
  padding-right: 16px;
}

.agreement-content h3 {
  margin-top: 0;
}

.agreement-content h4 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.agreement-content p {
  margin-bottom: 10px;
  line-height: 1.6;
}

/* 按钮动画效果 */
:deep(.n-button) {
  transition: transform 0.2s ease;
}

:deep(.n-button:not(.n-button--disabled):hover) {
  transform: translateY(-2px);
}

/* 标签页动画效果 */
:deep(.n-tabs-tab) {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 深色模式适配 */
:deep(.n-config-provider.n-config-provider--dark) .register-card {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.25);
}

:deep(.n-config-provider.n-config-provider--dark) .register-card:hover {
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.4);
}

.button-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style> 

