const { User, VerificationCode } = require('../models');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const config = require('../config/config');
const { generateCode } = require('../utils/smsUtil');
const { sendVerificationEmail, sendEmailVerification } = require('../utils/emailUtil');
const { sendSmsCode } = require('../utils/smsUtil');
const { v4: uuidv4 } = require('uuid');
const { Op } = require('sequelize');
const moment = require('moment');

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign({ id: userId }, config.jwt.secret, {
    expiresIn: config.jwt.expires_in
  });
};

// 注册用户 - 邮箱
exports.registerByEmail = async (req, res) => {
  try {
    console.log('邮箱注册请求:', req.body);
    const { username, email, password, code } = req.body;

    // 验证必填字段
    if (!username || !email || !password || !code) {
      console.log('缺少必填字段:', { username: !!username, email: !!email, password: !!password, code: !!code });
      return res.status(400).json({
        success: false,
        message: '请填写完整的注册信息'
      });
    }

    // 验证验证码
    console.log('查找验证码:', { email, code, type: 'register', contact_type: 'email' });
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: email,
        code: code,
        type: 'register',
        contact_type: 'email',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });

    if (!verificationCode) {
      console.log('验证码无效或已过期');
      return res.status(400).json({
        success: false,
        message: '验证码无效或已过期'
      });
    }

    console.log('验证码验证成功');
    
    // 检查用户名和邮箱是否已存在
    console.log('检查用户是否存在:', { username, email });
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { username: username },
          { email: email }
        ]
      }
    });

    if (existingUser) {
      console.log('用户已存在:', {
        existingUsername: existingUser.username,
        existingEmail: existingUser.email,
        inputUsername: username,
        inputEmail: email
      });
      if (existingUser.username === username) {
        return res.status(400).json({
          success: false,
          message: '用户名已存在'
        });
      } else {
        return res.status(400).json({
          success: false,
          message: '邮箱已被注册'
        });
      }
    }

    console.log('用户不存在，可以注册');
    
    // 创建用户
    console.log('开始创建用户:', { username, email });
    const user = await User.create({
      username,
      email,
      password,
      is_email_verified: true // 使用验证码注册，邮箱已验证
    });

    console.log('用户创建成功:', { id: user.id, username: user.username, email: user.email });

    // 标记验证码为已使用
    verificationCode.used = true;
    await verificationCode.save();
    console.log('验证码已标记为已使用');

    // 生成令牌
    const token = generateToken(user.id);
    console.log('令牌生成成功');
    
    // 返回用户信息和令牌
    res.status(201).json({
      success: true,
      message: '注册成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        avatar: user.avatar,
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified,
        created_at: user.createdAt
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 注册用户 - 手机
exports.registerByPhone = async (req, res) => {
  try {
    const { username, phone, password, code } = req.body;
    
    // 验证验证码
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: phone,
        code: code,
        type: 'register',
        contact_type: 'phone',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (!verificationCode) {
      return res.status(400).json({
        success: false,
        message: '验证码无效或已过期'
      });
    }
    
    // 检查用户名和手机号是否已存在
    const existingUser = await User.findOne({
      where: {
        [Op.or]: [
          { username: username },
          { phone: phone }
        ]
      }
    });
    
    if (existingUser) {
      if (existingUser.username === username) {
        return res.status(400).json({
          success: false,
          message: '用户名已存在'
        });
      } else {
        return res.status(400).json({
          success: false,
          message: '手机号已被注册'
        });
      }
    }
    
    // 创建用户
    const user = await User.create({
      username,
      phone,
      password,
      is_phone_verified: true // 使用验证码注册，手机号已验证
    });
    
    // 标记验证码为已使用
    verificationCode.used = true;
    await verificationCode.save();
    
    // 生成令牌
    const token = generateToken(user.id);
    
    // 返回用户信息和令牌
    res.status(201).json({
      success: true,
      message: '注册成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified,
        created_at: user.createdAt
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '注册失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 登录 - 账号密码
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;

    // 查找用户，首先尝试用户名，然后是邮箱，最后是手机号
    let user = await User.findOne({ where: { username: username } });
    if (!user) {
      user = await User.findOne({ where: { email: username } });
    }
    if (!user) {
      user = await User.findOne({ where: { phone: username } });
    }

    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    // 检查密码
    const isPasswordValid = await user.matchPassword(password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '密码错误'
      });
    }
    
    // 更新最后登录时间
    user.last_login = new Date();
    await user.save();
    
    // 生成令牌
    const token = generateToken(user.id);
    
    // 返回用户信息和令牌
    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified,
        last_login: user.last_login
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 登录 - 手机验证码
exports.loginByPhoneCode = async (req, res) => {
  try {
    const { phone, code } = req.body;
    
    // 验证验证码
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: phone,
        code: code,
        type: 'login',
        contact_type: 'phone',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (!verificationCode) {
      return res.status(400).json({
        success: false,
        message: '验证码无效或已过期'
      });
    }
    
    // 查找用户
    let user = await User.findOne({ where: { phone } });
    
    // 如果用户不存在，则创建新用户
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '该手机号尚未注册，请先注册'
      });
    }
    
    // 标记验证码为已使用
    verificationCode.used = true;
    await verificationCode.save();
    
    // 更新最后登录时间
    user.last_login = new Date();
    await user.save();
    
    // 生成令牌
    const token = generateToken(user.id);
    
    // 返回用户信息和令牌
    res.json({
      success: true,
      message: '登录成功',
      token,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified,
        last_login: user.last_login
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 发送验证码 - 邮箱
exports.sendEmailCode = async (req, res) => {
  try {
    const { email, type = 'register' } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    
    // 检查发送冷却时间
    const lastVerification = await VerificationCode.findOne({
      where: {
        contact: email,
        contact_type: 'email',
        type: type,
        created_at: {
          [Op.gt]: moment().subtract(config.verification_code.cooldown_seconds, 'seconds').toDate()
        }
      },
      order: [['created_at', 'DESC']]
    });
    
    if (lastVerification) {
      const nextAllowedTime = moment(lastVerification.createdAt)
        .add(config.verification_code.cooldown_seconds, 'seconds');
        
      const waitSeconds = Math.ceil(moment.duration(nextAllowedTime.diff(moment())).asSeconds());
      
      return res.status(429).json({
        success: false,
        message: `请求过于频繁，请${waitSeconds}秒后重试`,
        wait_seconds: waitSeconds
      });
    }
    
    // 生成验证码
    const code = generateCode(6);
    
    // 设置过期时间
    const expiresAt = moment().add(config.verification_code.expire_minutes, 'minutes').toDate();
    
    // 保存验证码到数据库
    const verificationCode = await VerificationCode.create({
      contact: email,
      code,
      type,
      contact_type: 'email',
      expires_at: expiresAt,
      used: false,
      ip: ipAddress
    });
    
    // 发送验证码邮件
    const result = await sendVerificationEmail(email, code, type);
    
    if (!result.success) {
      // 如果发送失败，删除验证码记录
      await verificationCode.destroy();
      
      return res.status(500).json({
        success: false,
        message: '邮件发送失败',
        error: result.error
      });
    }
    
    res.json({
      success: true,
      message: '验证码已发送',
      expires_in: config.verification_code.expire_minutes * 60
    });
  } catch (error) {
    console.error('发送验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '发送验证码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 发送验证码 - 手机
exports.sendSmsCode = async (req, res) => {
  try {
    const { phone, type = 'register' } = req.body;
    const ipAddress = req.ip || req.connection.remoteAddress;
    
    // 检查发送冷却时间
    const lastVerification = await VerificationCode.findOne({
      where: {
        contact: phone,
        contact_type: 'phone',
        type: type,
        created_at: {
          [Op.gt]: moment().subtract(config.verification_code.cooldown_seconds, 'seconds').toDate()
        }
      },
      order: [['created_at', 'DESC']]
    });
    
    if (lastVerification) {
      const nextAllowedTime = moment(lastVerification.createdAt)
        .add(config.verification_code.cooldown_seconds, 'seconds');
        
      const waitSeconds = Math.ceil(moment.duration(nextAllowedTime.diff(moment())).asSeconds());
      
      return res.status(429).json({
        success: false,
        message: `请求过于频繁，请${waitSeconds}秒后重试`,
        wait_seconds: waitSeconds
      });
    }
    
    // 生成验证码
    const code = generateCode(6);
    
    // 设置过期时间
    const expiresAt = moment().add(config.verification_code.expire_minutes, 'minutes').toDate();
    
    // 保存验证码到数据库
    const verificationCode = await VerificationCode.create({
      contact: phone,
      code,
      type,
      contact_type: 'phone',
      expires_at: expiresAt,
      used: false,
      ip: ipAddress
    });
    
    // 发送短信验证码
    const result = await sendSmsCode(phone, code);
    
    if (!result.success) {
      // 如果发送失败，删除验证码记录
      await verificationCode.destroy();
      
      return res.status(500).json({
        success: false,
        message: '短信发送失败',
        error: result.error
      });
    }
    
    res.json({
      success: true,
      message: '验证码已发送',
      expires_in: config.verification_code.expire_minutes * 60
    });
  } catch (error) {
    console.error('发送验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '发送验证码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 请求重置密码
exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;
    
    // 查找用户
    const user = await User.findOne({ where: { email } });
    
    if (!user) {
      // 出于安全考虑，即使用户不存在也返回成功
      return res.json({
        success: true,
        message: '如果该邮箱已注册，重置密码邮件已发送'
      });
    }
    
    // 生成重置令牌
    const resetToken = uuidv4();
    
    // 设置重置令牌和过期时间
    user.reset_password_token = resetToken;
    user.reset_password_expires = moment().add(24, 'hours').toDate();
    await user.save();
    
    // 发送重置密码邮件
    const verificationLink = `${config.frontend.url}/reset-password?token=${resetToken}`;
    
    // 这里可以发送带有链接的邮件，或者使用验证码
    // 为简化，这里使用与发送验证码相似的逻辑，但发送链接
    const result = await sendEmailVerification(email, resetToken, user.username);
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: '邮件发送失败',
        error: result.error
      });
    }
    
    res.json({
      success: true,
      message: '重置密码邮件已发送'
    });
  } catch (error) {
    console.error('请求重置密码错误:', error);
    res.status(500).json({
      success: false,
      message: '请求重置密码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 重置密码
exports.resetPassword = async (req, res) => {
  try {
    const { token, password } = req.body;
    
    // 查找用户
    const user = await User.findOne({
      where: {
        reset_password_token: token,
        reset_password_expires: { [Op.gt]: new Date() }
      }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '密码重置链接无效或已过期'
      });
    }
    
    // 更新密码
    user.password = password;
    user.reset_password_token = null;
    user.reset_password_expires = null;
    await user.save();
    
    res.json({
      success: true,
      message: '密码已重置'
    });
  } catch (error) {
    console.error('重置密码错误:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 通过手机重置密码
exports.resetPasswordByPhone = async (req, res) => {
  try {
    const { phone, token, password } = req.body;
    
    // 查找用户
    const user = await User.findOne({
      where: {
        phone: phone,
        reset_password_token: token,
        reset_password_expires: { [Op.gt]: new Date() }
      }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '密码重置链接无效或已过期'
      });
    }
    
    // 更新密码
    user.password = password;
    user.reset_password_token = null;
    user.reset_password_expires = null;
    await user.save();
    
    res.json({
      success: true,
      message: '密码已重置'
    });
  } catch (error) {
    console.error('通过手机重置密码错误:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 更新密码
exports.updatePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;
    
    // 查找用户
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    // 检查当前密码
    const isPasswordValid = await user.matchPassword(currentPassword);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '当前密码错误'
      });
    }
    
    // 更新密码
    user.password = newPassword;
    await user.save();
    
    res.json({
      success: true,
      message: '密码已更新'
    });
  } catch (error) {
    console.error('更新密码错误:', error);
    res.status(500).json({
      success: false,
      message: '更新密码失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 验证用户令牌
exports.verifyToken = async (req, res) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供认证令牌'
      });
    }
    
    // 验证令牌
    const decoded = jwt.verify(token, config.jwt.secret);
    
    // 查找用户
    const user = await User.findByPk(decoded.id);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌'
      });
    }
    
    res.json({
      success: true,
      message: '令牌有效',
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        phone: user.phone,
        avatar: user.avatar,
        role: user.role,
        is_email_verified: user.is_email_verified,
        is_phone_verified: user.is_phone_verified
      }
    });
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌'
      });
    }
    
    if (error instanceof jwt.TokenExpiredError) {
      return res.status(401).json({
        success: false,
        message: '令牌已过期'
      });
    }
    
    console.error('验证令牌错误:', error);
    res.status(500).json({
      success: false,
      message: '验证令牌失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 退出登录
exports.logout = (req, res) => {
  res.json({
    success: true,
    message: '退出登录成功'
  });
}; 

// 验证手机验证码
exports.verifyPhoneCode = async (req, res) => {
  try {
    const { phone, code, type = 'register' } = req.body;
    
    // 验证验证码
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: phone,
        code: code,
        type: type,
        contact_type: 'phone',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (!verificationCode) {
      return res.status(400).json({
        success: false,
        message: '验证码无效或已过期'
      });
    }
    
    // 标记验证码为已使用
    verificationCode.used = true;
    await verificationCode.save();
    
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } catch (error) {
    console.error('验证手机验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '验证码验证失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 验证邮箱验证码
exports.verifyEmailCode = async (req, res) => {
  try {
    const { email, code, type = 'register' } = req.body;
    
    // 验证验证码
    const verificationCode = await VerificationCode.findOne({
      where: {
        contact: email,
        code: code,
        type: type,
        contact_type: 'email',
        used: false,
        expires_at: { [Op.gt]: new Date() }
      }
    });
    
    if (!verificationCode) {
      return res.status(400).json({
        success: false,
        message: '验证码无效或已过期'
      });
    }
    
    // 标记验证码为已使用
    verificationCode.used = true;
    await verificationCode.save();
    
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } catch (error) {
    console.error('验证邮箱验证码错误:', error);
    res.status(500).json({
      success: false,
      message: '验证码验证失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
}; 

// 验证重置密码令牌
exports.validateResetToken = async (req, res) => {
  try {
    const { token } = req.body;
    
    // 查找用户
    const user = await User.findOne({
      where: {
        reset_password_token: token,
        reset_password_expires: { [Op.gt]: new Date() }
      }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '密码重置链接无效或已过期'
      });
    }
    
    res.json({
      success: true,
      message: '重置令牌有效',
      email: user.email
    });
  } catch (error) {
    console.error('验证重置令牌错误:', error);
    res.status(500).json({
      success: false,
      message: '验证重置令牌失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
};

// 验证手机重置密码令牌
exports.validatePhoneResetToken = async (req, res) => {
  try {
    const { phone, token } = req.body;
    
    // 查找用户
    const user = await User.findOne({
      where: {
        phone: phone,
        reset_password_token: token,
        reset_password_expires: { [Op.gt]: new Date() }
      }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '密码重置链接无效或已过期'
      });
    }
    
    res.json({
      success: true,
      message: '重置令牌有效',
      phone: user.phone
    });
  } catch (error) {
    console.error('验证手机重置令牌错误:', error);
    res.status(500).json({
      success: false,
      message: '验证重置令牌失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
}; 

// 发送邮箱验证邮件
exports.sendVerificationEmail = async (req, res) => {
  try {
    const userId = req.user.id;
    
    // 查找用户
    const user = await User.findByPk(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    if (user.is_email_verified) {
      return res.status(400).json({
        success: false,
        message: '邮箱已验证'
      });
    }
    
    if (!user.email) {
      return res.status(400).json({
        success: false,
        message: '用户未设置邮箱'
      });
    }
    
    // 生成验证令牌
    const verificationToken = uuidv4();
    
    // 保存验证令牌
    user.email_verification_token = verificationToken;
    await user.save();
    
    // 发送验证邮件
    const verificationLink = `${config.frontend.url}/verify-email?token=${verificationToken}`;
    const result = await sendEmailVerification(user.email, verificationToken, user.username);
    
    if (!result.success) {
      return res.status(500).json({
        success: false,
        message: '邮件发送失败',
        error: result.error
      });
    }
    
    res.json({
      success: true,
      message: '验证邮件已发送'
    });
  } catch (error) {
    console.error('发送验证邮件错误:', error);
    res.status(500).json({
      success: false,
      message: '发送验证邮件失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
}; 

// 验证邮箱
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.body;
    
    // 查找用户
    const user = await User.findOne({
      where: {
        email_verification_token: token
      }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: '验证链接无效或已过期'
      });
    }
    
    // 更新用户邮箱验证状态
    user.is_email_verified = true;
    user.email_verification_token = null;
    await user.save();
    
    res.json({
      success: true,
      message: '邮箱验证成功'
    });
  } catch (error) {
    console.error('验证邮箱错误:', error);
    res.status(500).json({
      success: false,
      message: '验证邮箱失败',
      error: config.server.env === 'development' ? error.message : undefined
    });
  }
}; 