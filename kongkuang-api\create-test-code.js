const { VerificationCode } = require('./models');
const { connectDB } = require('./config/db');
const moment = require('moment');

async function createTestCode() {
  try {
    await connectDB();
    
    // 删除旧的验证码
    await VerificationCode.destroy({
      where: {
        contact: '<EMAIL>',
        contact_type: 'email'
      }
    });
    
    // 创建新的验证码
    const code = await VerificationCode.create({
      contact: '<EMAIL>',
      code: '123456',
      type: 'register',
      contact_type: 'email',
      expires_at: moment().add(10, 'minutes').toDate(),
      used: false,
      ip: '127.0.0.1'
    });
    
    console.log('测试验证码已创建:', code.code);
    console.log('邮箱:', code.contact);
    console.log('过期时间:', code.expires_at);
    
    process.exit(0);
  } catch (error) {
    console.error('创建验证码失败:', error);
    process.exit(1);
  }
}

createTestCode();
