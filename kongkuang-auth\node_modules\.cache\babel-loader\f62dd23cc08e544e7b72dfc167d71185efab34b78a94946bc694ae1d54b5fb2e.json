{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"announcement-banner\"\n};\nconst _hoisted_2 = {\n  class: \"banner-container\"\n};\nconst _hoisted_3 = {\n  class: \"announcement-content\"\n};\nconst _hoisted_4 = {\n  class: \"announcement-text\"\n};\nconst _hoisted_5 = {\n  class: \"announcement-title\"\n};\nconst _hoisted_6 = {\n  class: \"announcement-description\"\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"indicators\"\n};\nconst _hoisted_8 = [\"onClick\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_chevron_back = _resolveComponent(\"chevron-back\");\n  const _component_n_icon = _resolveComponent(\"n-icon\");\n  const _component_n_button = _resolveComponent(\"n-button\");\n  const _component_chevron_forward = _resolveComponent(\"chevron-forward\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" 左右切换按钮 \"), _createVNode(_component_n_button, {\n    circle: \"\",\n    quaternary: \"\",\n    size: \"small\",\n    class: \"nav-button nav-button-left\",\n    onClick: _ctx.previousAnnouncement,\n    disabled: _ctx.announcements.length <= 1\n  }, {\n    icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_chevron_back)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"]), _createCommentVNode(\" 公告内容 \"), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"h3\", _hoisted_5, _toDisplayString(_ctx.currentAnnouncement.title), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_6, _toDisplayString(_ctx.currentAnnouncement.description), 1 /* TEXT */)])]), _createCommentVNode(\" 右切换按钮 \"), _createVNode(_component_n_button, {\n    circle: \"\",\n    quaternary: \"\",\n    size: \"small\",\n    class: \"nav-button nav-button-right\",\n    onClick: _ctx.nextAnnouncement,\n    disabled: _ctx.announcements.length <= 1\n  }, {\n    icon: _withCtx(() => [_createVNode(_component_n_icon, null, {\n      default: _withCtx(() => [_createVNode(_component_chevron_forward)]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onClick\", \"disabled\"])]), _createCommentVNode(\" 指示器 \"), _ctx.announcements.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.announcements, (_, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index,\n      class: _normalizeClass([\"indicator\", {\n        active: index === _ctx.currentIndex\n      }]),\n      onClick: $event => _ctx.goToAnnouncement(index)\n    }, null, 10 /* CLASS, PROPS */, _hoisted_8);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_createVNode", "_component_n_button", "circle", "quaternary", "size", "onClick", "_ctx", "previousAnnouncement", "disabled", "announcements", "length", "icon", "_withCtx", "_component_n_icon", "_component_chevron_back", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_toDisplayString", "currentAnnouncement", "title", "_hoisted_6", "description", "nextAnnouncement", "_component_chevron_forward", "_hoisted_7", "_Fragment", "_renderList", "_", "index", "key", "_normalizeClass", "active", "currentIndex", "$event", "goToAnnouncement"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\components\\AnnouncementBanner.vue"], "sourcesContent": ["<template>\n  <div class=\"announcement-banner\">\n    <div class=\"banner-container\">\n      <!-- 左右切换按钮 -->\n      <n-button \n        circle \n        quaternary \n        size=\"small\" \n        class=\"nav-button nav-button-left\"\n        @click=\"previousAnnouncement\"\n        :disabled=\"announcements.length <= 1\"\n      >\n        <template #icon>\n          <n-icon><chevron-back /></n-icon>\n        </template>\n      </n-button>\n      \n      <!-- 公告内容 -->\n      <div class=\"announcement-content\">\n        <div class=\"announcement-text\">\n          <h3 class=\"announcement-title\">{{ currentAnnouncement.title }}</h3>\n          <p class=\"announcement-description\">{{ currentAnnouncement.description }}</p>\n        </div>\n      </div>\n      \n      <!-- 右切换按钮 -->\n      <n-button \n        circle \n        quaternary \n        size=\"small\" \n        class=\"nav-button nav-button-right\"\n        @click=\"nextAnnouncement\"\n        :disabled=\"announcements.length <= 1\"\n      >\n        <template #icon>\n          <n-icon><chevron-forward /></n-icon>\n        </template>\n      </n-button>\n    </div>\n    \n    <!-- 指示器 -->\n    <div class=\"indicators\" v-if=\"announcements.length > 1\">\n      <div \n        v-for=\"(_, index) in announcements\" \n        :key=\"index\"\n        class=\"indicator\"\n        :class=\"{ active: index === currentIndex }\"\n        @click=\"goToAnnouncement(index)\"\n      ></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { defineComponent, ref, computed, onMounted, onUnmounted } from 'vue'\nimport { NButton, NIcon } from 'naive-ui'\nimport { ChevronBack, ChevronForward } from '@vicons/ionicons5'\n\nexport default defineComponent({\n  name: 'AnnouncementBanner',\n  components: {\n    NButton,\n    NIcon,\n    ChevronBack,\n    ChevronForward\n  },\n  setup() {\n    const currentIndex = ref(0)\n    const autoPlayTimer = ref(null)\n    \n    // 公告数据\n    const announcements = ref([\n      {\n        title: '欢迎使用空旷账户',\n        description: '感谢您选择空旷账户中心，我们致力于为您提供安全、便捷的身份认证服务。'\n      },\n      {\n        title: '共同创造良好的网络环境',\n        description: '请遵守相关法律法规，文明上网，共同维护健康的网络环境。'\n      }\n    ])\n    \n    // 当前公告\n    const currentAnnouncement = computed(() => {\n      return announcements.value[currentIndex.value] || announcements.value[0]\n    })\n    \n    // 下一个公告\n    const nextAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = (currentIndex.value + 1) % announcements.value.length\n      }\n    }\n    \n    // 上一个公告\n    const previousAnnouncement = () => {\n      if (announcements.value.length > 1) {\n        currentIndex.value = currentIndex.value === 0 \n          ? announcements.value.length - 1 \n          : currentIndex.value - 1\n      }\n    }\n    \n    // 跳转到指定公告\n    const goToAnnouncement = (index) => {\n      currentIndex.value = index\n    }\n    \n    // 自动播放\n    const startAutoPlay = () => {\n      if (announcements.value.length > 1) {\n        autoPlayTimer.value = setInterval(() => {\n          nextAnnouncement()\n        }, 5000) // 5秒切换一次\n      }\n    }\n    \n    // 停止自动播放\n    const stopAutoPlay = () => {\n      if (autoPlayTimer.value) {\n        clearInterval(autoPlayTimer.value)\n        autoPlayTimer.value = null\n      }\n    }\n    \n    onMounted(() => {\n      startAutoPlay()\n    })\n    \n    onUnmounted(() => {\n      stopAutoPlay()\n    })\n    \n    return {\n      announcements,\n      currentIndex,\n      currentAnnouncement,\n      nextAnnouncement,\n      previousAnnouncement,\n      goToAnnouncement\n    }\n  }\n})\n</script>\n\n<style scoped>\n.announcement-banner {\n  position: relative;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 12px;\n  padding: 20px;\n  margin-bottom: 24px;\n  color: white;\n  overflow: hidden;\n}\n\n.announcement-banner::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"rgba(255,255,255,0.1)\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"10\" cy=\"60\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/><circle cx=\"90\" cy=\"40\" r=\"0.5\" fill=\"rgba(255,255,255,0.05)\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n  pointer-events: none;\n}\n\n.banner-container {\n  display: flex;\n  align-items: center;\n  position: relative;\n  z-index: 1;\n}\n\n.announcement-content {\n  flex: 1;\n  text-align: center;\n  padding: 0 40px;\n}\n\n.announcement-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin: 0 0 8px 0;\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.announcement-description {\n  font-size: 14px;\n  margin: 0;\n  opacity: 0.9;\n  line-height: 1.5;\n}\n\n.nav-button {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 2;\n  background: rgba(255, 255, 255, 0.2);\n  backdrop-filter: blur(10px);\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  color: white;\n  transition: all 0.3s ease;\n}\n\n.nav-button:hover {\n  background: rgba(255, 255, 255, 0.3);\n  transform: translateY(-50%) scale(1.1);\n}\n\n.nav-button-left {\n  left: 10px;\n}\n\n.nav-button-right {\n  right: 10px;\n}\n\n.indicators {\n  display: flex;\n  justify-content: center;\n  gap: 8px;\n  margin-top: 16px;\n  position: relative;\n  z-index: 1;\n}\n\n.indicator {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background: rgba(255, 255, 255, 0.4);\n  cursor: pointer;\n  transition: all 0.3s ease;\n}\n\n.indicator.active {\n  background: white;\n  transform: scale(1.2);\n}\n\n.indicator:hover {\n  background: rgba(255, 255, 255, 0.7);\n}\n\n/* 深色模式适配 */\n:deep(.n-config-provider.n-config-provider--dark) .announcement-banner {\n  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .announcement-banner {\n    padding: 16px;\n    margin-bottom: 16px;\n  }\n  \n  .announcement-content {\n    padding: 0 30px;\n  }\n  \n  .announcement-title {\n    font-size: 20px;\n  }\n  \n  .announcement-description {\n    font-size: 13px;\n  }\n  \n  .nav-button-left {\n    left: 8px;\n  }\n  \n  .nav-button-right {\n    right: 8px;\n  }\n}\n\n@media (max-width: 480px) {\n  .announcement-content {\n    padding: 0 25px;\n  }\n  \n  .announcement-title {\n    font-size: 18px;\n  }\n  \n  .announcement-description {\n    font-size: 12px;\n  }\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAAkB;;EAgBtBA,KAAK,EAAC;AAAsB;;EAC1BA,KAAK,EAAC;AAAmB;;EACxBA,KAAK,EAAC;AAAoB;;EAC3BA,KAAK,EAAC;AAA0B;;;EAoBpCA,KAAK,EAAC;;;;;;;;uBAxCbC,mBAAA,CAiDM,OAjDNC,UAiDM,GAhDJC,mBAAA,CAoCM,OApCNC,UAoCM,GAnCJC,mBAAA,YAAe,EACfC,YAAA,CAWWC,mBAAA;IAVTC,MAAM,EAAN,EAAM;IACNC,UAAU,EAAV,EAAU;IACVC,IAAI,EAAC,OAAO;IACZV,KAAK,EAAC,4BAA4B;IACjCW,OAAK,EAAEC,IAAA,CAAAC,oBAAoB;IAC3BC,QAAQ,EAAEF,IAAA,CAAAG,aAAa,CAACC,MAAM;;IAEpBC,IAAI,EAAAC,QAAA,CACb,MAAiC,CAAjCZ,YAAA,CAAiCa,iBAAA;wBAAzB,MAAgB,CAAhBb,YAAA,CAAgBc,uBAAA,E;;;;8CAI5Bf,mBAAA,UAAa,EACbF,mBAAA,CAKM,OALNkB,UAKM,GAJJlB,mBAAA,CAGM,OAHNmB,UAGM,GAFJnB,mBAAA,CAAmE,MAAnEoB,UAAmE,EAAAC,gBAAA,CAAjCZ,IAAA,CAAAa,mBAAmB,CAACC,KAAK,kBAC3DvB,mBAAA,CAA6E,KAA7EwB,UAA6E,EAAAH,gBAAA,CAAtCZ,IAAA,CAAAa,mBAAmB,CAACG,WAAW,iB,KAI1EvB,mBAAA,WAAc,EACdC,YAAA,CAWWC,mBAAA;IAVTC,MAAM,EAAN,EAAM;IACNC,UAAU,EAAV,EAAU;IACVC,IAAI,EAAC,OAAO;IACZV,KAAK,EAAC,6BAA6B;IAClCW,OAAK,EAAEC,IAAA,CAAAiB,gBAAgB;IACvBf,QAAQ,EAAEF,IAAA,CAAAG,aAAa,CAACC,MAAM;;IAEpBC,IAAI,EAAAC,QAAA,CACb,MAAoC,CAApCZ,YAAA,CAAoCa,iBAAA;wBAA5B,MAAmB,CAAnBb,YAAA,CAAmBwB,0BAAA,E;;;;gDAKjCzB,mBAAA,SAAY,EACkBO,IAAA,CAAAG,aAAa,CAACC,MAAM,Q,cAAlDf,mBAAA,CAQM,OARN8B,UAQM,I,kBAPJ9B,mBAAA,CAMO+B,SAAA,QAAAC,WAAA,CALgBrB,IAAA,CAAAG,aAAa,GAA1BmB,CAAC,EAAEC,KAAK;yBADlBlC,mBAAA,CAMO;MAJJmC,GAAG,EAAED,KAAK;MACXnC,KAAK,EAAAqC,eAAA,EAAC,WAAW;QAAAC,MAAA,EACCH,KAAK,KAAKvB,IAAA,CAAA2B;MAAY;MACvC5B,OAAK,EAAA6B,MAAA,IAAE5B,IAAA,CAAA6B,gBAAgB,CAACN,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}