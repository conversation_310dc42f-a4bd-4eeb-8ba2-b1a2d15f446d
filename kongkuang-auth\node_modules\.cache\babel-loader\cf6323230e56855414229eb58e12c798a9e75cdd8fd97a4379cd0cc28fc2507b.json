{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, openBlock as _openBlock, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"verification-container\"\n};\nconst _hoisted_2 = {\n  class: \"verification-overview\"\n};\nconst _hoisted_3 = {\n  class: \"level-header\"\n};\nconst _hoisted_4 = {\n  class: \"level-features\"\n};\nconst _hoisted_5 = {\n  class: \"feature-item\"\n};\nconst _hoisted_6 = {\n  class: \"feature-item\"\n};\nconst _hoisted_7 = {\n  class: \"level-actions\"\n};\nconst _hoisted_8 = {\n  class: \"level-header\"\n};\nconst _hoisted_9 = {\n  class: \"level-features\"\n};\nconst _hoisted_10 = {\n  class: \"feature-item\"\n};\nconst _hoisted_11 = {\n  class: \"feature-item\"\n};\nconst _hoisted_12 = {\n  class: \"feature-item\"\n};\nconst _hoisted_13 = {\n  class: \"level-actions\"\n};\nconst _hoisted_14 = {\n  class: \"level1-content\"\n};\nconst _hoisted_15 = {\n  class: \"payment-options\"\n};\nconst _hoisted_16 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_17 = {\n  class: \"payment-badge\"\n};\nconst _hoisted_18 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_19 = {\n  class: \"verification-process\"\n};\nconst _hoisted_20 = {\n  class: \"process-steps\"\n};\nconst _hoisted_21 = {\n  class: \"process-step\"\n};\nconst _hoisted_22 = {\n  class: \"process-step\"\n};\nconst _hoisted_23 = {\n  class: \"process-step\"\n};\nconst _hoisted_24 = {\n  class: \"process-step\"\n};\nconst _hoisted_25 = {\n  class: \"process-step\"\n};\nconst _hoisted_26 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_27 = {\n  class: \"level2-content\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"face-verification-area\"\n};\nconst _hoisted_29 = {\n  class: \"face-preview\"\n};\nconst _hoisted_30 = {\n  key: 1,\n  class: \"face-verification-camera\"\n};\nconst _hoisted_31 = {\n  class: \"camera-container\"\n};\nconst _hoisted_32 = {\n  ref: \"videoRef\",\n  autoplay: \"\",\n  muted: \"\",\n  playsinline: \"\"\n};\nconst _hoisted_33 = {\n  ref: \"canvasRef\",\n  style: {\n    \"display\": \"none\"\n  }\n};\nconst _hoisted_34 = {\n  class: \"verification-status\"\n};\nconst _hoisted_35 = {\n  key: 1,\n  class: \"verification-result\"\n};\nconst _hoisted_36 = {\n  class: \"modal-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_shield_checkmark_outline = _resolveComponent(\"shield-checkmark-outline\");\n  const _component_shield_outline = _resolveComponent(\"shield-outline\");\n  const _component_n_tag = _resolveComponent(\"n-tag\");\n  const _component_checkmark = _resolveComponent(\"checkmark\");\n  const _component_logo_alipay = _resolveComponent(\"logo-alipay\");\n  const _component_logo_wechat = _resolveComponent(\"logo-wechat\");\n  const _component_card_outline = _resolveComponent(\"card-outline\");\n  const _component_arrow_forward = _resolveComponent(\"arrow-forward\");\n  const _component_wallet_outline = _resolveComponent(\"wallet-outline\");\n  const _component_checkmark_circle_outline = _resolveComponent(\"checkmark-circle-outline\");\n  const _component_n_modal = _resolveComponent(\"n-modal\");\n  const _component_n_spin = _resolveComponent(\"n-spin\");\n  const _component_close_circle_outline = _resolveComponent(\"close-circle-outline\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[42] || (_cache[42] = _createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"实名认证\", -1 /* CACHED */)), _createCommentVNode(\" 认证状态概览 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode($setup[\"NCard\"], {\n    class: _normalizeClass([\"level-card\", {\n      'completed': _ctx.level1Completed\n    }])\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"NIcon\"], {\n      size: \"24\",\n      color: _ctx.level1Completed ? '#18a058' : '#2080f0'\n    }, {\n      default: _withCtx(() => [_ctx.level1Completed ? (_openBlock(), _createBlock(_component_shield_checkmark_outline, {\n        key: 0\n      })) : (_openBlock(), _createBlock(_component_shield_outline, {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"color\"]), _cache[8] || (_cache[8] = _createElementVNode(\"h3\", null, \"一级认证\", -1 /* CACHED */)), _ctx.level1Completed ? (_openBlock(), _createBlock(_component_n_tag, {\n      key: 0,\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [6]\n    })) : (_openBlock(), _createBlock(_component_n_tag, {\n      key: 1,\n      type: \"info\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[7] || (_cache[7] = [_createTextVNode(\"未完成\")])),\n      _: 1 /* STABLE */,\n      __: [7]\n    }))]), _cache[13] || (_cache[13] = _createElementVNode(\"p\", {\n      class: \"level-description\"\n    }, \"通过支付宝或微信实名验证，快速完成身份认证\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark)]),\n      _: 1 /* STABLE */\n    }), _cache[9] || (_cache[9] = _createElementVNode(\"span\", null, \"基础身份验证\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_6, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark)]),\n      _: 1 /* STABLE */\n    }), _cache[10] || (_cache[10] = _createElementVNode(\"span\", null, \"访问基础功能\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_7, [!_ctx.level1Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: _ctx.startLevel1Verification,\n      loading: _ctx.level1Loading\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\" 开始认证 \")])),\n      _: 1 /* STABLE */,\n      __: [11]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])) : (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }))])]),\n    _: 1 /* STABLE */,\n    __: [13]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode($setup[\"NCard\"], {\n    class: _normalizeClass([\"level-card\", {\n      'completed': _ctx.level2Completed,\n      'disabled': !_ctx.level1Completed\n    }])\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_8, [_createVNode($setup[\"NIcon\"], {\n      size: \"24\",\n      color: _ctx.level2Completed ? '#18a058' : _ctx.level1Completed ? '#2080f0' : '#d0d0d0'\n    }, {\n      default: _withCtx(() => [_ctx.level2Completed ? (_openBlock(), _createBlock(_component_shield_checkmark_outline, {\n        key: 0\n      })) : (_openBlock(), _createBlock(_component_shield_outline, {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"color\"]), _cache[17] || (_cache[17] = _createElementVNode(\"h3\", null, \"二级认证\", -1 /* CACHED */)), _ctx.level2Completed ? (_openBlock(), _createBlock(_component_n_tag, {\n      key: 0,\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [14]\n    })) : _ctx.level1Completed ? (_openBlock(), _createBlock(_component_n_tag, {\n      key: 1,\n      type: \"warning\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"可进行\")])),\n      _: 1 /* STABLE */,\n      __: [15]\n    })) : (_openBlock(), _createBlock(_component_n_tag, {\n      key: 2,\n      type: \"default\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"需完成一级认证\")])),\n      _: 1 /* STABLE */,\n      __: [16]\n    }))]), _cache[24] || (_cache[24] = _createElementVNode(\"p\", {\n      class: \"level-description\"\n    }, \"通过人脸识别进行高级身份验证，免费使用\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark)]),\n      _: 1 /* STABLE */\n    }), _cache[18] || (_cache[18] = _createElementVNode(\"span\", null, \"高级身份验证\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_11, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark)]),\n      _: 1 /* STABLE */\n    }), _cache[19] || (_cache[19] = _createElementVNode(\"span\", null, \"访问所有功能\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_12, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark)]),\n      _: 1 /* STABLE */\n    }), _cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"免费使用\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_13, [!_ctx.level2Completed && _ctx.level1Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: _ctx.startLevel2Verification,\n      loading: _ctx.level2Loading\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\" 开始认证 \")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])) : _ctx.level2Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    })) : (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 2,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"需完成一级认证\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }))])]),\n    _: 1 /* STABLE */,\n    __: [24]\n  }, 8 /* PROPS */, [\"class\"])]), _createCommentVNode(\" 一级认证模态框 \"), _createVNode(_component_n_modal, {\n    show: _ctx.showLevel1Modal,\n    \"onUpdate:show\": _cache[3] || (_cache[3] = $event => _ctx.showLevel1Modal = $event),\n    preset: \"card\",\n    title: \"一级认证\",\n    style: {\n      \"width\": \"600px\",\n      \"max-width\": \"90vw\"\n    }\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createVNode($setup[\"NButton\"], {\n      onClick: _cache[2] || (_cache[2] = $event => _ctx.showLevel1Modal = false)\n    }, {\n      default: _withCtx(() => _cache[33] || (_cache[33] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [33]\n    }), _createVNode($setup[\"NButton\"], {\n      type: \"primary\",\n      onClick: _ctx.proceedLevel1Payment,\n      disabled: !_ctx.selectedPayment,\n      loading: _ctx.level1Loading\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 确认支付 \" + _toDisplayString(_ctx.selectedPayment === 'alipay' ? '¥1.2' : '¥1.5'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"onClick\", \"disabled\", \"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createVNode($setup[\"NAlert\"], {\n      title: \"认证说明\",\n      type: \"info\",\n      style: {\n        \"margin-bottom\": \"24px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\" 选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。 \")])),\n      _: 1 /* STABLE */,\n      __: [25]\n    }), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"payment-option\", {\n        'selected': _ctx.selectedPayment === 'alipay'\n      }]),\n      onClick: _cache[0] || (_cache[0] = $event => _ctx.selectedPayment = 'alipay')\n    }, [_createElementVNode(\"div\", _hoisted_16, [_createVNode($setup[\"NIcon\"], {\n      size: \"32\",\n      color: \"#1677ff\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_logo_alipay)]),\n      _: 1 /* STABLE */\n    })]), _cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n      class: \"payment-info\"\n    }, [_createElementVNode(\"h4\", null, \"支付宝认证\"), _createElementVNode(\"p\", null, \"通过支付宝实名信息进行验证\"), _createElementVNode(\"div\", {\n      class: \"payment-price\"\n    }, [_createElementVNode(\"span\", {\n      class: \"price\"\n    }, \"¥1.2\"), _createElementVNode(\"span\", {\n      class: \"original-price\"\n    }, \"¥2.0\")])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_n_tag, {\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"推荐\")])),\n      _: 1 /* STABLE */,\n      __: [26]\n    })])], 2 /* CLASS */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"payment-option\", {\n        'selected': _ctx.selectedPayment === 'wechat'\n      }]),\n      onClick: _cache[1] || (_cache[1] = $event => _ctx.selectedPayment = 'wechat')\n    }, [_createElementVNode(\"div\", _hoisted_18, [_createVNode($setup[\"NIcon\"], {\n      size: \"32\",\n      color: \"#07c160\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_logo_wechat)]),\n      _: 1 /* STABLE */\n    })]), _cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n      class: \"payment-info\"\n    }, [_createElementVNode(\"h4\", null, \"微信认证\"), _createElementVNode(\"p\", null, \"通过微信实名信息进行验证\"), _createElementVNode(\"div\", {\n      class: \"payment-price\"\n    }, [_createElementVNode(\"span\", {\n      class: \"price\"\n    }, \"¥1.5\")])], -1 /* CACHED */))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[32] || (_cache[32] = _createElementVNode(\"h4\", null, \"认证流程：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_card_outline)]),\n      _: 1 /* STABLE */\n    }), _cache[29] || (_cache[29] = _createElementVNode(\"span\", null, \"选择支付方式\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_22, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_arrow_forward)]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_23, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_wallet_outline)]),\n      _: 1 /* STABLE */\n    }), _cache[30] || (_cache[30] = _createElementVNode(\"span\", null, \"完成支付\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_24, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_arrow_forward)]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_25, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_checkmark_circle_outline)]),\n      _: 1 /* STABLE */\n    }), _cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"认证完成\", -1 /* CACHED */))])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"]), _createCommentVNode(\" 二级认证模态框 \"), _createVNode(_component_n_modal, {\n    show: _ctx.showLevel2Modal,\n    \"onUpdate:show\": _cache[5] || (_cache[5] = $event => _ctx.showLevel2Modal = $event),\n    preset: \"card\",\n    title: \"二级认证\",\n    style: {\n      \"width\": \"600px\",\n      \"max-width\": \"90vw\"\n    }\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_36, [_createVNode($setup[\"NButton\"], {\n      onClick: _cache[4] || (_cache[4] = $event => _ctx.showLevel2Modal = false),\n      disabled: _ctx.faceVerifying\n    }, {\n      default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [38]\n    }, 8 /* PROPS */, [\"disabled\"]), !_ctx.faceVerificationStarted ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.startFaceVerification,\n      loading: _ctx.level2Loading\n    }, {\n      default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\" 开始人脸识别 \")])),\n      _: 1 /* STABLE */,\n      __: [39]\n    }, 8 /* PROPS */, [\"loading\"])) : _ctx.faceVerificationResult?.success ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      type: \"primary\",\n      onClick: _ctx.completeFaceVerification\n    }, {\n      default: _withCtx(() => _cache[40] || (_cache[40] = [_createTextVNode(\" 完成认证 \")])),\n      _: 1 /* STABLE */,\n      __: [40]\n    }, 8 /* PROPS */, [\"onClick\"])) : _ctx.faceVerificationResult && !_ctx.faceVerificationResult.success ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 2,\n      type: \"primary\",\n      onClick: _ctx.retryFaceVerification\n    }, {\n      default: _withCtx(() => _cache[41] || (_cache[41] = [_createTextVNode(\" 重新识别 \")])),\n      _: 1 /* STABLE */,\n      __: [41]\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_27, [_createVNode($setup[\"NAlert\"], {\n      title: \"人脸识别认证\",\n      type: \"info\",\n      style: {\n        \"margin-bottom\": \"24px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\" 二级认证完全免费，通过人脸识别技术验证您的身份信息。 \")])),\n      _: 1 /* STABLE */,\n      __: [34]\n    }), !_ctx.faceVerificationStarted ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createElementVNode(\"div\", _hoisted_29, [_createVNode($setup[\"NAvatar\"], {\n      size: \"120\",\n      src: $setup.userStore.user?.avatar || '/default-avatar.png'\n    }, null, 8 /* PROPS */, [\"src\"])]), _cache[35] || (_cache[35] = _createElementVNode(\"div\", {\n      class: \"face-instructions\"\n    }, [_createElementVNode(\"h4\", null, \"人脸识别说明：\"), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"请确保光线充足，面部清晰可见\"), _createElementVNode(\"li\", null, \"请正对摄像头，保持面部居中\"), _createElementVNode(\"li\", null, \"请勿佩戴帽子、墨镜等遮挡物\"), _createElementVNode(\"li\", null, \"整个过程大约需要3-5秒\")])], -1 /* CACHED */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createElementVNode(\"video\", _hoisted_32, null, 512 /* NEED_PATCH */), _createElementVNode(\"canvas\", _hoisted_33, null, 512 /* NEED_PATCH */), _cache[36] || (_cache[36] = _createElementVNode(\"div\", {\n      class: \"camera-overlay\"\n    }, [_createElementVNode(\"div\", {\n      class: \"face-frame\"\n    })], -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_34, [_ctx.faceVerifying ? (_openBlock(), _createBlock(_component_n_spin, {\n      key: 0,\n      size: \"small\"\n    }, {\n      description: _withCtx(() => _cache[37] || (_cache[37] = [_createTextVNode(\"正在进行人脸识别...\")])),\n      _: 1 /* STABLE */\n    })) : _ctx.faceVerificationResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createVNode($setup[\"NIcon\"], {\n      size: \"24\",\n      color: _ctx.faceVerificationResult.success ? '#18a058' : '#d03050'\n    }, {\n      default: _withCtx(() => [_ctx.faceVerificationResult.success ? (_openBlock(), _createBlock(_component_checkmark_circle_outline, {\n        key: 0\n      })) : (_openBlock(), _createBlock(_component_close_circle_outline, {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"color\"]), _createElementVNode(\"span\", null, _toDisplayString(_ctx.faceVerificationResult.message), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])]))])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "ref", "autoplay", "muted", "playsinline", "style", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_createVNode", "$setup", "_normalizeClass", "_ctx", "level1Completed", "_hoisted_3", "size", "color", "_createBlock", "_component_shield_checkmark_outline", "key", "_component_shield_outline", "_component_n_tag", "type", "_cache", "_hoisted_4", "_hoisted_5", "_component_checkmark", "_hoisted_6", "_hoisted_7", "onClick", "startLevel1Verification", "loading", "level1Loading", "disabled", "level2Completed", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "startLevel2Verification", "level2Loading", "_component_n_modal", "show", "showLevel1Modal", "$event", "preset", "title", "footer", "_withCtx", "_hoisted_26", "proceedLevel1Payment", "selectedPayment", "_toDisplayString", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_component_logo_alipay", "_hoisted_17", "_hoisted_18", "_component_logo_wechat", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_component_card_outline", "_hoisted_22", "_component_arrow_forward", "_hoisted_23", "_component_wallet_outline", "_hoisted_24", "_hoisted_25", "_component_checkmark_circle_outline", "showLevel2Modal", "_hoisted_36", "faceVerifying", "faceVerificationStarted", "startFaceVerification", "faceVerificationResult", "success", "completeFaceVerification", "retryFaceVerification", "_hoisted_27", "_hoisted_28", "_hoisted_29", "src", "userStore", "user", "avatar", "_hoisted_30", "_hoisted_31", "_hoisted_32", "_hoisted_33", "_hoisted_34", "_component_n_spin", "description", "_hoisted_35", "_component_close_circle_outline", "message"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Verification.vue"], "sourcesContent": ["<template>\r\n  <div class=\"verification-container\">\r\n    <h1 class=\"page-title\">实名认证</h1>\r\n\r\n    <!-- 认证状态概览 -->\r\n    <div class=\"verification-overview\">\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level1Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level1Completed ? '#18a058' : '#2080f0'\">\r\n            <shield-checkmark-outline v-if=\"level1Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>一级认证</h3>\r\n          <n-tag v-if=\"level1Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else type=\"info\" size=\"small\">未完成</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过支付宝或微信实名验证，快速完成身份认证</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>基础身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问基础功能</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level1Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel1Verification\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else disabled>已完成</n-button>\r\n        </div>\r\n      </n-card>\r\n\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level2Completed, 'disabled': !level1Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level2Completed ? '#18a058' : (level1Completed ? '#2080f0' : '#d0d0d0')\">\r\n            <shield-checkmark-outline v-if=\"level2Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>二级认证</h3>\r\n          <n-tag v-if=\"level2Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else-if=\"level1Completed\" type=\"warning\" size=\"small\">可进行</n-tag>\r\n          <n-tag v-else type=\"default\" size=\"small\">需完成一级认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过人脸识别进行高级身份验证，免费使用</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>高级身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问所有功能</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>免费使用</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level2Completed && level1Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else-if=\"level2Completed\" disabled>已完成</n-button>\r\n          <n-button v-else disabled>需完成一级认证</n-button>\r\n        </div>\r\n      </n-card>\r\n    </div>\r\n\r\n    <!-- 一级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel1Modal\" preset=\"card\" title=\"一级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level1-content\">\r\n        <n-alert title=\"认证说明\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。\r\n        </n-alert>\r\n\r\n        <div class=\"payment-options\">\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'alipay' }\"\r\n            @click=\"selectedPayment = 'alipay'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#1677ff\">\r\n                <logo-alipay />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>支付宝认证</h4>\r\n              <p>通过支付宝实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.2</span>\r\n                <span class=\"original-price\">¥2.0</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-badge\">\r\n              <n-tag type=\"success\" size=\"small\">推荐</n-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'wechat' }\"\r\n            @click=\"selectedPayment = 'wechat'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#07c160\">\r\n                <logo-wechat />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>微信认证</h4>\r\n              <p>通过微信实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.5</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"verification-process\">\r\n          <h4>认证流程：</h4>\r\n          <div class=\"process-steps\">\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><card-outline /></n-icon>\r\n              <span>选择支付方式</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><wallet-outline /></n-icon>\r\n              <span>完成支付</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><checkmark-circle-outline /></n-icon>\r\n              <span>认证完成</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel1Modal = false\">取消</n-button>\r\n          <n-button\r\n            type=\"primary\"\r\n            @click=\"proceedLevel1Payment\"\r\n            :disabled=\"!selectedPayment\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 二级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel2Modal\" preset=\"card\" title=\"二级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level2-content\">\r\n        <n-alert title=\"人脸识别认证\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          二级认证完全免费，通过人脸识别技术验证您的身份信息。\r\n        </n-alert>\r\n\r\n        <div class=\"face-verification-area\" v-if=\"!faceVerificationStarted\">\r\n          <div class=\"face-preview\">\r\n            <n-avatar size=\"120\" :src=\"userStore.user?.avatar || '/default-avatar.png'\" />\r\n          </div>\r\n          <div class=\"face-instructions\">\r\n            <h4>人脸识别说明：</h4>\r\n            <ul>\r\n              <li>请确保光线充足，面部清晰可见</li>\r\n              <li>请正对摄像头，保持面部居中</li>\r\n              <li>请勿佩戴帽子、墨镜等遮挡物</li>\r\n              <li>整个过程大约需要3-5秒</li>\r\n            </ul>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"face-verification-camera\" v-else>\r\n          <div class=\"camera-container\">\r\n            <video ref=\"videoRef\" autoplay muted playsinline></video>\r\n            <canvas ref=\"canvasRef\" style=\"display: none;\"></canvas>\r\n            <div class=\"camera-overlay\">\r\n              <div class=\"face-frame\"></div>\r\n            </div>\r\n          </div>\r\n          <div class=\"verification-status\">\r\n            <n-spin v-if=\"faceVerifying\" size=\"small\">\r\n              <template #description>正在进行人脸识别...</template>\r\n            </n-spin>\r\n            <div v-else-if=\"faceVerificationResult\" class=\"verification-result\">\r\n              <n-icon size=\"24\" :color=\"faceVerificationResult.success ? '#18a058' : '#d03050'\">\r\n                <checkmark-circle-outline v-if=\"faceVerificationResult.success\" />\r\n                <close-circle-outline v-else />\r\n              </n-icon>\r\n              <span>{{ faceVerificationResult.message }}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel2Modal = false\" :disabled=\"faceVerifying\">取消</n-button>\r\n          <n-button\r\n            v-if=\"!faceVerificationStarted\"\r\n            type=\"primary\"\r\n            @click=\"startFaceVerification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始人脸识别\r\n          </n-button>\r\n          <n-button\r\n            v-else-if=\"faceVerificationResult?.success\"\r\n            type=\"primary\"\r\n            @click=\"completeFaceVerification\"\r\n          >\r\n            完成认证\r\n          </n-button>\r\n          <n-button\r\n            v-else-if=\"faceVerificationResult && !faceVerificationResult.success\"\r\n            type=\"primary\"\r\n            @click=\"retryFaceVerification\"\r\n          >\r\n            重新识别\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport { \r\n  NCard, \r\n  NSteps, \r\n  NStep, \r\n  NForm, \r\n  NFormItem, \r\n  NInput, \r\n  NButton, \r\n  NUpload,\r\n  NAlert,\r\n  NIcon,\r\n  NAvatar,\r\n  NProgress,\r\n  useMessage\r\n} from 'naive-ui';\r\nimport { useUserStore } from '../stores/user';\r\n\r\nconst router = useRouter();\r\nconst message = useMessage();\r\nconst userStore = useUserStore();\r\n\r\n// 步骤状态\r\nconst currentStep = ref(0);\r\nconst stepStatus = ref('process');\r\n\r\n// 表单数据\r\nconst formRef = ref(null);\r\nconst formValue = ref({\r\n  realName: '',\r\n  idNumber: ''\r\n});\r\n\r\n// 表单验证规则\r\nconst rules = {\r\n  realName: {\r\n    required: true,\r\n    message: '请输入真实姓名',\r\n    trigger: ['blur', 'input']\r\n  },\r\n  idNumber: [\r\n    {\r\n      required: true,\r\n      message: '请输入身份证号码',\r\n      trigger: ['blur', 'input']\r\n    },\r\n    {\r\n      validator(rule, value) {\r\n        // 简单的身份证号码验证，实际项目中应使用更严格的验证\r\n        const reg = /(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/;\r\n        if (!reg.test(value)) {\r\n          return new Error('请输入正确的身份证号码');\r\n        }\r\n        return true;\r\n      },\r\n      trigger: ['blur', 'input']\r\n    }\r\n  ]\r\n};\r\n\r\n// 上传文件列表\r\nconst frontIdCardFiles = ref([]);\r\nconst backIdCardFiles = ref([]);\r\nconst canProceed = computed(() => frontIdCardFiles.value.length > 0 && backIdCardFiles.value.length > 0);\r\n\r\n// 人脸验证状态\r\nconst faceVerified = ref(false);\r\n\r\n// 处理上传\r\nconst handleFrontUpload = (options) => {\r\n  frontIdCardFiles.value = options.fileList;\r\n};\r\n\r\nconst handleBackUpload = (options) => {\r\n  backIdCardFiles.value = options.fileList;\r\n};\r\n\r\n// 开始人脸验证\r\nconst startFaceVerification = () => {\r\n  // 实际项目中应调用人脸识别API\r\n  message.info('正在调用人脸识别服务...');\r\n  setTimeout(() => {\r\n    faceVerified.value = true;\r\n    message.success('人脸验证通过');\r\n  }, 2000);\r\n};\r\n\r\n// 下一步\r\nconst nextStep = async () => {\r\n  if (currentStep.value === 0) {\r\n    try {\r\n      await formRef.value?.validate();\r\n      currentStep.value++;\r\n    } catch (errors) {\r\n      console.error(errors);\r\n    }\r\n  } else if (currentStep.value === 1) {\r\n    if (!canProceed.value) {\r\n      message.warning('请上传身份证正反面照片');\r\n      return;\r\n    }\r\n    currentStep.value++;\r\n  } else if (currentStep.value === 2) {\r\n    if (!faceVerified.value) {\r\n      message.warning('请完成人脸验证');\r\n      return;\r\n    }\r\n    currentStep.value++;\r\n    stepStatus.value = 'finish';\r\n  }\r\n};\r\n\r\n// 上一步\r\nconst prevStep = () => {\r\n  if (currentStep.value > 0) {\r\n    currentStep.value--;\r\n  }\r\n};\r\n\r\n// 返回仪表盘\r\nconst goToDashboard = () => {\r\n  router.push('/dashboard');\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.verification-container {\r\n  padding: 16px;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 24px;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-card {\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.step-content {\r\n  margin-top: 32px;\r\n}\r\n\r\n.upload-area {\r\n  display: flex;\r\n  gap: 24px;\r\n  margin-top: 24px;\r\n}\r\n\r\n.id-upload {\r\n  flex: 1;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: var(--n-text-color-3);\r\n  margin-top: 8px;\r\n}\r\n\r\n.step-actions {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 24px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .upload-area {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAI5BA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAe;;EAcrBA,KAAK,EAAC;AAAc;;EAWpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAe;;EAiBvBA,KAAK,EAAC;AAAgB;;EAKpBA,KAAK,EAAC;AAAiB;;EAMnBA,KAAK,EAAC;AAAc;;EAapBA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAc;;EAexBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EASxBA,KAAK,EAAC;AAAc;;EAgBtBA,KAAK,EAAC;AAAgB;;;EAKpBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;;EActBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAkB;;EACpBC,GAAG,EAAC,UAAU;EAACC,QAAQ,EAAR,EAAQ;EAACC,KAAK,EAAL,EAAK;EAACC,WAAW,EAAX;;;EAC7BH,GAAG,EAAC,WAAW;EAACI,KAAsB,EAAtB;IAAA;EAAA;;;EAKrBL,KAAK,EAAC;AAAqB;;;EAIUA,KAAK,EAAC;;;EAY7CA,KAAK,EAAC;AAAc;;;;;;;;;;;;;;;uBAzN/BM,mBAAA,CAoPM,OApPNC,UAoPM,G,4BAnPJC,mBAAA,CAAgC;IAA5BR,KAAK,EAAC;EAAY,GAAC,MAAI,qBAE3BS,mBAAA,YAAe,EACfD,mBAAA,CA0EM,OA1ENE,UA0EM,GAzEJC,YAAA,CAgCSC,MAAA;IAhCDZ,KAAK,EAAAa,eAAA,EAAC,YAAY;MAAA,aAAwBC,IAAA,CAAAC;IAAe;;sBAC/D,MAQM,CARNP,mBAAA,CAQM,OARNQ,UAQM,GAPJL,YAAA,CAGSC,MAAA;MAHDK,IAAI,EAAC,IAAI;MAAEC,KAAK,EAAEJ,IAAA,CAAAC,eAAe;;wBACvC,MAAmD,CAAnBD,IAAA,CAAAC,eAAe,I,cAA/CI,YAAA,CAAmDC,mCAAA;QAAAC,GAAA;MAAA,O,cACnDF,YAAA,CAAyBG,yBAAA;QAAAD,GAAA;MAAA,I;;4DAE3Bb,mBAAA,CAAa,YAAT,MAAI,qBACKM,IAAA,CAAAC,eAAe,I,cAA5BI,YAAA,CAAqEI,gBAAA;;MAAvCC,IAAI,EAAC,SAAS;MAACP,IAAI,EAAC;;wBAAQ,MAAGQ,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;yBAC7DN,YAAA,CAAkDI,gBAAA;;MAApCC,IAAI,EAAC,MAAM;MAACP,IAAI,EAAC;;wBAAQ,MAAGQ,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;uCAE5CjB,mBAAA,CAAsD;MAAnDR,KAAK,EAAC;IAAmB,GAAC,uBAAqB,qBAClDQ,mBAAA,CASM,OATNkB,UASM,GARJlB,mBAAA,CAGM,OAHNmB,UAGM,GAFJhB,YAAA,CAAwDC,MAAA;MAAhDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbP,YAAA,CAAaiB,oBAAA,E;;kCAC/CpB,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAGM,OAHNqB,UAGM,GAFJlB,YAAA,CAAwDC,MAAA;MAAhDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbP,YAAA,CAAaiB,oBAAA,E;;oCAC/CpB,mBAAA,CAAmB,cAAb,QAAM,oB,KAGhBA,mBAAA,CAUM,OAVNsB,UAUM,G,CARKhB,IAAA,CAAAC,eAAe,I,cADxBI,YAAA,CAOWP,MAAA;;MALTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEjB,IAAA,CAAAkB,uBAAuB;MAC9BC,OAAO,EAAEnB,IAAA,CAAAoB;;wBACX,MAEDT,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;gEACAN,YAAA,CAAwCP,MAAA;;MAAvBuB,QAAQ,EAAR;;wBAAS,MAAGV,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;gCAIjCd,YAAA,CAsCSC,MAAA;IAtCDZ,KAAK,EAAAa,eAAA,EAAC,YAAY;MAAA,aAAwBC,IAAA,CAAAsB,eAAe;MAAA,aAAetB,IAAA,CAAAC;IAAe;;sBAC7F,MASM,CATNP,mBAAA,CASM,OATN6B,UASM,GARJ1B,YAAA,CAGSC,MAAA;MAHDK,IAAI,EAAC,IAAI;MAAEC,KAAK,EAAEJ,IAAA,CAAAsB,eAAe,eAAgBtB,IAAA,CAAAC,eAAe;;wBACtE,MAAmD,CAAnBD,IAAA,CAAAsB,eAAe,I,cAA/CjB,YAAA,CAAmDC,mCAAA;QAAAC,GAAA;MAAA,O,cACnDF,YAAA,CAAyBG,yBAAA;QAAAD,GAAA;MAAA,I;;8DAE3Bb,mBAAA,CAAa,YAAT,MAAI,qBACKM,IAAA,CAAAsB,eAAe,I,cAA5BjB,YAAA,CAAqEI,gBAAA;;MAAvCC,IAAI,EAAC,SAAS;MAACP,IAAI,EAAC;;wBAAQ,MAAGQ,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;UAC3CX,IAAA,CAAAC,eAAe,I,cAAjCI,YAAA,CAA0EI,gBAAA;;MAAvCC,IAAI,EAAC,SAAS;MAACP,IAAI,EAAC;;wBAAQ,MAAGQ,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;yBAClEN,YAAA,CAAyDI,gBAAA;;MAA3CC,IAAI,EAAC,SAAS;MAACP,IAAI,EAAC;;wBAAQ,MAAOQ,MAAA,SAAAA,MAAA,Q,iBAAP,SAAO,E;;;uCAEnDjB,mBAAA,CAAoD;MAAjDR,KAAK,EAAC;IAAmB,GAAC,qBAAmB,qBAChDQ,mBAAA,CAaM,OAbN8B,UAaM,GAZJ9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ5B,YAAA,CAAwDC,MAAA;MAAhDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbP,YAAA,CAAaiB,oBAAA,E;;oCAC/CpB,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAGM,OAHNgC,WAGM,GAFJ7B,YAAA,CAAwDC,MAAA;MAAhDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbP,YAAA,CAAaiB,oBAAA,E;;oCAC/CpB,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAGM,OAHNiC,WAGM,GAFJ9B,YAAA,CAAwDC,MAAA;MAAhDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbP,YAAA,CAAaiB,oBAAA,E;;oCAC/CpB,mBAAA,CAAiB,cAAX,MAAI,oB,KAGdA,mBAAA,CAWM,OAXNkC,WAWM,G,CATK5B,IAAA,CAAAsB,eAAe,IAAItB,IAAA,CAAAC,eAAe,I,cAD3CI,YAAA,CAOWP,MAAA;;MALTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEjB,IAAA,CAAA6B,uBAAuB;MAC9BV,OAAO,EAAEnB,IAAA,CAAA8B;;wBACX,MAEDnB,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;iDACqBX,IAAA,CAAAsB,eAAe,I,cAApCjB,YAAA,CAA6DP,MAAA;;MAAvBuB,QAAQ,EAAR;;wBAAS,MAAGV,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;yBAClDN,YAAA,CAA4CP,MAAA;;MAA3BuB,QAAQ,EAAR;;wBAAS,MAAOV,MAAA,SAAAA,MAAA,Q,iBAAP,SAAO,E;;;;;;kCAKvChB,mBAAA,aAAgB,EAChBE,YAAA,CAwFUkC,kBAAA;IAxFOC,IAAI,EAAEhC,IAAA,CAAAiC,eAAe;yDAAfjC,IAAA,CAAAiC,eAAe,GAAAC,MAAA;IAAEC,MAAM,EAAC,MAAM;IAACC,KAAK,EAAC,MAAM;IAAC7C,KAAsC,EAAtC;MAAA;MAAA;IAAA;;IA2EtD8C,MAAM,EAAAC,QAAA,CACf,MAUM,CAVN5C,mBAAA,CAUM,OAVN6C,WAUM,GATJ1C,YAAA,CAAwDC,MAAA;MAA7CmB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAElC,IAAA,CAAAiC,eAAe;;wBAAU,MAAEtB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC7Cd,YAAA,CAOWC,MAAA;MANTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEjB,IAAA,CAAAwC,oBAAoB;MAC3BnB,QAAQ,GAAGrB,IAAA,CAAAyC,eAAe;MAC1BtB,OAAO,EAAEnB,IAAA,CAAAoB;;wBACX,MACM,C,iBADN,QACM,GAAAsB,gBAAA,CAAG1C,IAAA,CAAAyC,eAAe,gD;;;sBAnF7B,MAwEM,CAxEN/C,mBAAA,CAwEM,OAxENiD,WAwEM,GAvEJ9C,YAAA,CAEUC,MAAA;MAFDsC,KAAK,EAAC,MAAM;MAAC1B,IAAI,EAAC,MAAM;MAACnB,KAA4B,EAA5B;QAAA;MAAA;;wBAA6B,MAE/DoB,MAAA,SAAAA,MAAA,Q,iBAF+D,uCAE/D,E;;;QAEAjB,mBAAA,CA0CM,OA1CNkD,WA0CM,GAzCJlD,mBAAA,CAqBM;MApBJR,KAAK,EAAAa,eAAA,EAAC,gBAAgB;QAAA,YACAC,IAAA,CAAAyC,eAAe;MAAA;MACpCxB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAElC,IAAA,CAAAyC,eAAe;QAEvB/C,mBAAA,CAIM,OAJNmD,WAIM,GAHJhD,YAAA,CAESC,MAAA;MAFDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAAe,CAAfP,YAAA,CAAeiD,sBAAA,E;;sCAGnBpD,mBAAA,CAOM;MAPDR,KAAK,EAAC;IAAc,IACvBQ,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAoB,WAAjB,eAAa,GAChBA,mBAAA,CAGM;MAHDR,KAAK,EAAC;IAAe,IACxBQ,mBAAA,CAA+B;MAAzBR,KAAK,EAAC;IAAO,GAAC,MAAI,GACxBQ,mBAAA,CAAwC;MAAlCR,KAAK,EAAC;IAAgB,GAAC,MAAI,E,uBAGrCQ,mBAAA,CAEM,OAFNqD,WAEM,GADJlD,YAAA,CAA6CY,gBAAA;MAAtCC,IAAI,EAAC,SAAS;MAACP,IAAI,EAAC;;wBAAQ,MAAEQ,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;2BAIzCjB,mBAAA,CAiBM;MAhBJR,KAAK,EAAAa,eAAA,EAAC,gBAAgB;QAAA,YACAC,IAAA,CAAAyC,eAAe;MAAA;MACpCxB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAElC,IAAA,CAAAyC,eAAe;QAEvB/C,mBAAA,CAIM,OAJNsD,WAIM,GAHJnD,YAAA,CAESC,MAAA;MAFDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAAe,CAAfP,YAAA,CAAeoD,sBAAA,E;;sCAGnBvD,mBAAA,CAMM;MANDR,KAAK,EAAC;IAAc,IACvBQ,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAmB,WAAhB,cAAY,GACfA,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAe,IACxBQ,mBAAA,CAA+B;MAAzBR,KAAK,EAAC;IAAO,GAAC,MAAI,E,0CAMhCQ,mBAAA,CAsBM,OAtBNwD,WAsBM,G,4BArBJxD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAmBM,OAnBNyD,WAmBM,GAlBJzD,mBAAA,CAGM,OAHN0D,WAGM,GAFJvD,YAAA,CAA2DC,MAAA;MAAnDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAgB,CAAhBP,YAAA,CAAgBwD,uBAAA,E;;oCAClD3D,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAEM,OAFN4D,WAEM,GADJzD,YAAA,CAA4DC,MAAA;MAApDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAiB,CAAjBP,YAAA,CAAiB0D,wBAAA,E;;UAErD7D,mBAAA,CAGM,OAHN8D,WAGM,GAFJ3D,YAAA,CAA6DC,MAAA;MAArDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAkB,CAAlBP,YAAA,CAAkB4D,yBAAA,E;;oCACpD/D,mBAAA,CAAiB,cAAX,MAAI,oB,GAEZA,mBAAA,CAEM,OAFNgE,WAEM,GADJ7D,YAAA,CAA4DC,MAAA;MAApDK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAiB,CAAjBP,YAAA,CAAiB0D,wBAAA,E;;UAErD7D,mBAAA,CAGM,OAHNiE,WAGM,GAFJ9D,YAAA,CAAuEC,MAAA;MAA/DK,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAA4B,CAA5BP,YAAA,CAA4B+D,mCAAA,E;;oCAC9DlE,mBAAA,CAAiB,cAAX,MAAI,oB;;+BAqBpBC,mBAAA,aAAgB,EAChBE,YAAA,CAuEUkC,kBAAA;IAvEOC,IAAI,EAAEhC,IAAA,CAAA6D,eAAe;yDAAf7D,IAAA,CAAA6D,eAAe,GAAA3B,MAAA;IAAEC,MAAM,EAAC,MAAM;IAACC,KAAK,EAAC,MAAM;IAAC7C,KAAsC,EAAtC;MAAA;MAAA;IAAA;;IA4CtD8C,MAAM,EAAAC,QAAA,CACf,MAwBM,CAxBN5C,mBAAA,CAwBM,OAxBNoE,WAwBM,GAvBJjE,YAAA,CAAkFC,MAAA;MAAvEmB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAuB,MAAA,IAAElC,IAAA,CAAA6D,eAAe;MAAWxC,QAAQ,EAAErB,IAAA,CAAA+D;;wBAAe,MAAEpD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;sCAE9DX,IAAA,CAAAgE,uBAAuB,I,cADhC3D,YAAA,CAOWP,MAAA;;MALTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEnB,MAAA,CAAAmE,qBAAqB;MAC5B9C,OAAO,EAAEnB,IAAA,CAAA8B;;wBACX,MAEDnB,MAAA,SAAAA,MAAA,Q,iBAFC,UAED,E;;;sCAEaX,IAAA,CAAAkE,sBAAsB,EAAEC,OAAO,I,cAD5C9D,YAAA,CAMWP,MAAA;;MAJTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEjB,IAAA,CAAAoE;;wBACT,MAEDzD,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;sCAEaX,IAAA,CAAAkE,sBAAsB,KAAKlE,IAAA,CAAAkE,sBAAsB,CAACC,OAAO,I,cADtE9D,YAAA,CAMWP,MAAA;;MAJTY,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEjB,IAAA,CAAAqE;;wBACT,MAED1D,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;sBAnEJ,MAyCM,CAzCNjB,mBAAA,CAyCM,OAzCN4E,WAyCM,GAxCJzE,YAAA,CAEUC,MAAA;MAFDsC,KAAK,EAAC,QAAQ;MAAC1B,IAAI,EAAC,MAAM;MAACnB,KAA4B,EAA5B;QAAA;MAAA;;wBAA6B,MAEjEoB,MAAA,SAAAA,MAAA,Q,iBAFiE,8BAEjE,E;;;SAE2CX,IAAA,CAAAgE,uBAAuB,I,cAAlExE,mBAAA,CAaM,OAbN+E,WAaM,GAZJ7E,mBAAA,CAEM,OAFN8E,WAEM,GADJ3E,YAAA,CAA8EC,MAAA;MAApEK,IAAI,EAAC,KAAK;MAAEsE,GAAG,EAAE3E,MAAA,CAAA4E,SAAS,CAACC,IAAI,EAAEC,MAAM;oEAEnDlF,mBAAA,CAQM;MARDR,KAAK,EAAC;IAAmB,IAC5BQ,mBAAA,CAAgB,YAAZ,SAAO,GACXA,mBAAA,CAKK,aAJHA,mBAAA,CAAuB,YAAnB,gBAAc,GAClBA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAAqB,YAAjB,cAAY,E,0CAKtBF,mBAAA,CAoBM,OApBNqF,WAoBM,GAnBJnF,mBAAA,CAMM,OANNoF,WAMM,GALJpF,mBAAA,CAAyD,SAAzDqF,WAAyD,+BACzDrF,mBAAA,CAAwD,UAAxDsF,WAAwD,+B,4BACxDtF,mBAAA,CAEM;MAFDR,KAAK,EAAC;IAAgB,IACzBQ,mBAAA,CAA8B;MAAzBR,KAAK,EAAC;IAAY,G,uBAG3BQ,mBAAA,CAWM,OAXNuF,WAWM,GAVUjF,IAAA,CAAA+D,aAAa,I,cAA3B1D,YAAA,CAES6E,iBAAA;;MAFoB/E,IAAI,EAAC;;MACrBgF,WAAW,EAAA7C,QAAA,CAAC,MAAW3B,MAAA,SAAAA,MAAA,Q,iBAAX,aAAW,E;;UAEpBX,IAAA,CAAAkE,sBAAsB,I,cAAtC1E,mBAAA,CAMM,OANN4F,WAMM,GALJvF,YAAA,CAGSC,MAAA;MAHDK,IAAI,EAAC,IAAI;MAAEC,KAAK,EAAEJ,IAAA,CAAAkE,sBAAsB,CAACC,OAAO;;wBACtD,MAAkE,CAAlCnE,IAAA,CAAAkE,sBAAsB,CAACC,OAAO,I,cAA9D9D,YAAA,CAAkEuD,mCAAA;QAAArD,GAAA;MAAA,O,cAClEF,YAAA,CAA+BgF,+BAAA;QAAA9E,GAAA;MAAA,I;;kCAEjCb,mBAAA,CAAiD,cAAAgD,gBAAA,CAAxC1C,IAAA,CAAAkE,sBAAsB,CAACoB,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}