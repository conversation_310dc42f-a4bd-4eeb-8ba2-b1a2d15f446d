{"ast": null, "code": "import { ref, computed, watch, onMounted } from 'vue';\nimport { NModal, NIcon, NSpin, NButton, NAlert } from 'naive-ui';\nimport { CheckmarkCircleOutline, CloseCircleOutline, ScanOutline, WalletOutline, PersonOutline } from '@vicons/ionicons5';\nexport default {\n  __name: 'FacePaymentResult',\n  props: {\n    show: {\n      type: Boolean,\n      default: false\n    },\n    status: {\n      type: String,\n      default: 'processing',\n      // processing, success, failed\n      validator: value => ['processing', 'success', 'failed'].includes(value)\n    },\n    paymentMethod: {\n      type: String,\n      default: 'alipay' // alipay, wechat\n    },\n    amount: {\n      type: [String, Number],\n      default: '1.2'\n    },\n    realName: {\n      type: String,\n      default: ''\n    },\n    idNumber: {\n      type: String,\n      default: ''\n    },\n    verifiedAt: {\n      type: [String, Date],\n      default: null\n    },\n    errorMessage: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['update:show', 'confirm', 'retry'],\n  setup(__props, {\n    expose: __expose,\n    emit: __emit\n  }) {\n    __expose();\n    const props = __props;\n    const emit = __emit;\n    const visible = computed({\n      get: () => props.show,\n      set: value => emit('update:show', value)\n    });\n    const currentStep = ref(1);\n    const resultTitle = computed(() => {\n      switch (props.status) {\n        case 'success':\n          return '一级认证成功';\n        case 'failed':\n          return '认证失败';\n        case 'processing':\n        default:\n          return '正在认证';\n      }\n    });\n    const maskedName = computed(() => {\n      if (!props.realName) return '';\n      if (props.realName.length <= 2) return props.realName;\n      return props.realName.charAt(0) + '***' + props.realName.charAt(props.realName.length - 1);\n    });\n    const maskedIdNumber = computed(() => {\n      if (!props.idNumber) return '';\n      if (props.idNumber.length < 10) return props.idNumber;\n      return props.idNumber.substring(0, 6) + '****' + props.idNumber.substring(props.idNumber.length - 4);\n    });\n    const formatTime = time => {\n      if (!time) return '';\n      const date = new Date(time);\n      return date.toLocaleString('zh-CN');\n    };\n    const handleConfirm = () => {\n      emit('confirm');\n      visible.value = false;\n    };\n    const handleRetry = () => {\n      emit('retry');\n      visible.value = false;\n    };\n\n    // 模拟处理步骤\n    watch(() => props.status, newStatus => {\n      if (newStatus === 'processing') {\n        currentStep.value = 1;\n        const stepInterval = setInterval(() => {\n          if (currentStep.value < 4 && props.status === 'processing') {\n            currentStep.value++;\n          } else {\n            clearInterval(stepInterval);\n          }\n        }, 1000);\n      }\n    });\n    onMounted(() => {\n      if (props.status === 'processing') {\n        currentStep.value = 1;\n      }\n    });\n    const __returned__ = {\n      props,\n      emit,\n      visible,\n      currentStep,\n      resultTitle,\n      maskedName,\n      maskedIdNumber,\n      formatTime,\n      handleConfirm,\n      handleRetry,\n      ref,\n      computed,\n      watch,\n      onMounted,\n      get NModal() {\n        return NModal;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NSpin() {\n        return NSpin;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get ScanOutline() {\n        return ScanOutline;\n      },\n      get WalletOutline() {\n        return WalletOutline;\n      },\n      get PersonOutline() {\n        return PersonOutline;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "computed", "watch", "onMounted", "NModal", "NIcon", "NSpin", "NButton", "N<PERSON><PERSON><PERSON>", "CheckmarkCircleOutline", "CloseCircleOutline", "ScanOutline", "WalletOutline", "PersonOutline", "props", "__props", "emit", "__emit", "visible", "get", "show", "set", "value", "currentStep", "resultTitle", "status", "<PERSON><PERSON><PERSON>", "realName", "length", "char<PERSON>t", "maskedIdNumber", "idNumber", "substring", "formatTime", "time", "date", "Date", "toLocaleString", "handleConfirm", "handleRetry", "newStatus", "stepInterval", "setInterval", "clearInterval"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/components/FacePaymentResult.vue"], "sourcesContent": ["<template>\n  <div class=\"face-payment-result\">\n    <n-modal \n      v-model:show=\"visible\" \n      preset=\"card\" \n      :title=\"resultTitle\"\n      style=\"width: 500px; max-width: 90vw;\"\n      :closable=\"false\"\n      :mask-closable=\"false\"\n    >\n      <div class=\"result-content\">\n        <!-- 成功状态 -->\n        <div v-if=\"status === 'success'\" class=\"result-success\">\n          <div class=\"result-icon\">\n            <n-icon size=\"64\" color=\"#18a058\">\n              <checkmark-circle-outline />\n            </n-icon>\n          </div>\n          <h3>识脸支付成功</h3>\n          <p class=\"result-message\">恭喜！您已成功完成一级认证</p>\n          \n          <div class=\"payment-info\">\n            <div class=\"info-item\">\n              <span class=\"label\">支付方式：</span>\n              <span class=\"value\">{{ paymentMethod === 'alipay' ? '支付宝识脸支付' : '微信识脸支付' }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">支付金额：</span>\n              <span class=\"value\">¥{{ amount }}</span>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">认证时间：</span>\n              <span class=\"value\">{{ formatTime(verifiedAt) }}</span>\n            </div>\n          </div>\n          \n          <div class=\"identity-info\">\n            <n-alert title=\"获取的实名信息\" type=\"success\" style=\"margin: 16px 0;\">\n              <div class=\"identity-details\">\n                <div class=\"info-item\">\n                  <span class=\"label\">真实姓名：</span>\n                  <span class=\"value\">{{ maskedName }}</span>\n                </div>\n                <div class=\"info-item\">\n                  <span class=\"label\">身份证号：</span>\n                  <span class=\"value\">{{ maskedIdNumber }}</span>\n                </div>\n              </div>\n            </n-alert>\n          </div>\n          \n          <div class=\"notice\">\n            <n-alert title=\"重要提示\" type=\"warning\" style=\"margin-top: 16px;\">\n              <p>• 一级认证信息将在30分钟后自动清除</p>\n              <p>• 如需永久保存，请完成二级认证</p>\n              <p>• 认证信息仅用于身份验证，严格保护隐私</p>\n            </n-alert>\n          </div>\n        </div>\n        \n        <!-- 失败状态 -->\n        <div v-else-if=\"status === 'failed'\" class=\"result-failed\">\n          <div class=\"result-icon\">\n            <n-icon size=\"64\" color=\"#d03050\">\n              <close-circle-outline />\n            </n-icon>\n          </div>\n          <h3>识脸支付失败</h3>\n          <p class=\"result-message\">{{ errorMessage || '支付过程中出现问题，请重试' }}</p>\n          \n          <div class=\"failure-reasons\">\n            <n-alert title=\"可能的原因\" type=\"error\" style=\"margin: 16px 0;\">\n              <ul>\n                <li>人脸识别验证失败</li>\n                <li>支付账户余额不足</li>\n                <li>网络连接异常</li>\n                <li>支付平台服务异常</li>\n              </ul>\n            </n-alert>\n          </div>\n        </div>\n        \n        <!-- 处理中状态 -->\n        <div v-else-if=\"status === 'processing'\" class=\"result-processing\">\n          <div class=\"result-icon\">\n            <n-spin size=\"large\" />\n          </div>\n          <h3>正在处理</h3>\n          <p class=\"result-message\">正在验证支付结果，请稍候...</p>\n          \n          <div class=\"processing-steps\">\n            <div class=\"step\" :class=\"{ active: currentStep >= 1 }\">\n              <n-icon size=\"16\"><scan-outline /></n-icon>\n              <span>人脸识别验证</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 2 }\">\n              <n-icon size=\"16\"><wallet-outline /></n-icon>\n              <span>处理支付</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 3 }\">\n              <n-icon size=\"16\"><person-outline /></n-icon>\n              <span>获取实名信息</span>\n            </div>\n            <div class=\"step\" :class=\"{ active: currentStep >= 4 }\">\n              <n-icon size=\"16\"><checkmark-circle-outline /></n-icon>\n              <span>完成认证</span>\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <template #footer>\n        <div class=\"modal-footer\">\n          <n-button \n            v-if=\"status === 'success'\" \n            type=\"primary\" \n            @click=\"handleConfirm\"\n          >\n            确定\n          </n-button>\n          <n-button \n            v-else-if=\"status === 'failed'\" \n            @click=\"handleRetry\"\n          >\n            重试\n          </n-button>\n          <n-button \n            v-if=\"status === 'failed'\" \n            type=\"primary\" \n            @click=\"handleConfirm\"\n          >\n            关闭\n          </n-button>\n        </div>\n      </template>\n    </n-modal>\n  </div>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted } from 'vue';\nimport {\n  NModal,\n  NIcon,\n  NSpin,\n  NButton,\n  NAlert\n} from 'naive-ui';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  ScanOutline,\n  WalletOutline,\n  PersonOutline\n} from '@vicons/ionicons5';\n\nconst props = defineProps({\n  show: {\n    type: Boolean,\n    default: false\n  },\n  status: {\n    type: String,\n    default: 'processing', // processing, success, failed\n    validator: (value) => ['processing', 'success', 'failed'].includes(value)\n  },\n  paymentMethod: {\n    type: String,\n    default: 'alipay' // alipay, wechat\n  },\n  amount: {\n    type: [String, Number],\n    default: '1.2'\n  },\n  realName: {\n    type: String,\n    default: ''\n  },\n  idNumber: {\n    type: String,\n    default: ''\n  },\n  verifiedAt: {\n    type: [String, Date],\n    default: null\n  },\n  errorMessage: {\n    type: String,\n    default: ''\n  }\n});\n\nconst emit = defineEmits(['update:show', 'confirm', 'retry']);\n\nconst visible = computed({\n  get: () => props.show,\n  set: (value) => emit('update:show', value)\n});\n\nconst currentStep = ref(1);\n\nconst resultTitle = computed(() => {\n  switch (props.status) {\n    case 'success':\n      return '一级认证成功';\n    case 'failed':\n      return '认证失败';\n    case 'processing':\n    default:\n      return '正在认证';\n  }\n});\n\nconst maskedName = computed(() => {\n  if (!props.realName) return '';\n  if (props.realName.length <= 2) return props.realName;\n  return props.realName.charAt(0) + '***' + props.realName.charAt(props.realName.length - 1);\n});\n\nconst maskedIdNumber = computed(() => {\n  if (!props.idNumber) return '';\n  if (props.idNumber.length < 10) return props.idNumber;\n  return props.idNumber.substring(0, 6) + '****' + props.idNumber.substring(props.idNumber.length - 4);\n});\n\nconst formatTime = (time) => {\n  if (!time) return '';\n  const date = new Date(time);\n  return date.toLocaleString('zh-CN');\n};\n\nconst handleConfirm = () => {\n  emit('confirm');\n  visible.value = false;\n};\n\nconst handleRetry = () => {\n  emit('retry');\n  visible.value = false;\n};\n\n// 模拟处理步骤\nwatch(() => props.status, (newStatus) => {\n  if (newStatus === 'processing') {\n    currentStep.value = 1;\n    const stepInterval = setInterval(() => {\n      if (currentStep.value < 4 && props.status === 'processing') {\n        currentStep.value++;\n      } else {\n        clearInterval(stepInterval);\n      }\n    }, 1000);\n  }\n});\n\nonMounted(() => {\n  if (props.status === 'processing') {\n    currentStep.value = 1;\n  }\n});\n</script>\n\n<style scoped>\n.face-payment-result {\n  /* 组件容器样式 */\n}\n\n.result-content {\n  text-align: center;\n  padding: 16px 0;\n}\n\n.result-icon {\n  margin-bottom: 16px;\n}\n\n.result-content h3 {\n  margin: 0 0 8px 0;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.result-message {\n  margin: 0 0 24px 0;\n  color: var(--n-text-color-2);\n  font-size: 14px;\n}\n\n.payment-info,\n.identity-details {\n  text-align: left;\n  margin: 16px 0;\n}\n\n.info-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 8px 0;\n  border-bottom: 1px solid var(--n-border-color);\n}\n\n.info-item:last-child {\n  border-bottom: none;\n}\n\n.info-item .label {\n  font-weight: 500;\n  color: var(--n-text-color-2);\n}\n\n.info-item .value {\n  font-weight: 600;\n  color: var(--n-text-color-1);\n}\n\n.processing-steps {\n  display: flex;\n  justify-content: space-between;\n  margin: 24px 0;\n  padding: 16px;\n  background-color: var(--n-color-target);\n  border-radius: 8px;\n}\n\n.step {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  font-size: 12px;\n  color: var(--n-text-color-3);\n  transition: all 0.3s ease;\n}\n\n.step.active {\n  color: var(--n-primary-color);\n}\n\n.step span {\n  text-align: center;\n  max-width: 60px;\n  line-height: 1.2;\n}\n\n.failure-reasons ul {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n\n.failure-reasons li {\n  margin: 4px 0;\n  color: var(--n-text-color-2);\n}\n\n.notice p {\n  margin: 4px 0;\n  font-size: 13px;\n}\n\n.modal-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n@media (max-width: 768px) {\n  .processing-steps {\n    flex-direction: column;\n    gap: 16px;\n  }\n  \n  .step {\n    flex-direction: row;\n    justify-content: flex-start;\n    text-align: left;\n  }\n  \n  .step span {\n    max-width: none;\n  }\n}\n</style>\n"], "mappings": "AA4IA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,KAAK;AACrD,SACEC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,OAAO,EACPC,MAAK,QACA,UAAU;AACjB,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,WAAW,EACXC,aAAa,EACbC,aAAY,QACP,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAE1B,MAAMC,KAAK,GAAGC,OAkCZ;IAEF,MAAMC,IAAI,GAAGC,MAAgD;IAE7D,MAAMC,OAAO,GAAGjB,QAAQ,CAAC;MACvBkB,GAAG,EAAEA,CAAA,KAAML,KAAK,CAACM,IAAI;MACrBC,GAAG,EAAGC,KAAK,IAAKN,IAAI,CAAC,aAAa,EAAEM,KAAK;IAC3C,CAAC,CAAC;IAEF,MAAMC,WAAW,GAAGvB,GAAG,CAAC,CAAC,CAAC;IAE1B,MAAMwB,WAAW,GAAGvB,QAAQ,CAAC,MAAM;MACjC,QAAQa,KAAK,CAACW,MAAM;QAClB,KAAK,SAAS;UACZ,OAAO,QAAQ;QACjB,KAAK,QAAQ;UACX,OAAO,MAAM;QACf,KAAK,YAAY;QACjB;UACE,OAAO,MAAM;MACjB;IACF,CAAC,CAAC;IAEF,MAAMC,UAAU,GAAGzB,QAAQ,CAAC,MAAM;MAChC,IAAI,CAACa,KAAK,CAACa,QAAQ,EAAE,OAAO,EAAE;MAC9B,IAAIb,KAAK,CAACa,QAAQ,CAACC,MAAM,IAAI,CAAC,EAAE,OAAOd,KAAK,CAACa,QAAQ;MACrD,OAAOb,KAAK,CAACa,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAGf,KAAK,CAACa,QAAQ,CAACE,MAAM,CAACf,KAAK,CAACa,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IAC5F,CAAC,CAAC;IAEF,MAAME,cAAc,GAAG7B,QAAQ,CAAC,MAAM;MACpC,IAAI,CAACa,KAAK,CAACiB,QAAQ,EAAE,OAAO,EAAE;MAC9B,IAAIjB,KAAK,CAACiB,QAAQ,CAACH,MAAM,GAAG,EAAE,EAAE,OAAOd,KAAK,CAACiB,QAAQ;MACrD,OAAOjB,KAAK,CAACiB,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,MAAM,GAAGlB,KAAK,CAACiB,QAAQ,CAACC,SAAS,CAAClB,KAAK,CAACiB,QAAQ,CAACH,MAAM,GAAG,CAAC,CAAC;IACtG,CAAC,CAAC;IAEF,MAAMK,UAAU,GAAIC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;MACpB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,IAAI,CAAC;MAC3B,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,CAAC;IACrC,CAAC;IAED,MAAMC,aAAa,GAAGA,CAAA,KAAM;MAC1BtB,IAAI,CAAC,SAAS,CAAC;MACfE,OAAO,CAACI,KAAK,GAAG,KAAK;IACvB,CAAC;IAED,MAAMiB,WAAW,GAAGA,CAAA,KAAM;MACxBvB,IAAI,CAAC,OAAO,CAAC;MACbE,OAAO,CAACI,KAAK,GAAG,KAAK;IACvB,CAAC;;IAED;IACApB,KAAK,CAAC,MAAMY,KAAK,CAACW,MAAM,EAAGe,SAAS,IAAK;MACvC,IAAIA,SAAS,KAAK,YAAY,EAAE;QAC9BjB,WAAW,CAACD,KAAK,GAAG,CAAC;QACrB,MAAMmB,YAAY,GAAGC,WAAW,CAAC,MAAM;UACrC,IAAInB,WAAW,CAACD,KAAK,GAAG,CAAC,IAAIR,KAAK,CAACW,MAAM,KAAK,YAAY,EAAE;YAC1DF,WAAW,CAACD,KAAK,EAAE;UACrB,CAAC,MAAM;YACLqB,aAAa,CAACF,YAAY,CAAC;UAC7B;QACF,CAAC,EAAE,IAAI,CAAC;MACV;IACF,CAAC,CAAC;IAEFtC,SAAS,CAAC,MAAM;MACd,IAAIW,KAAK,CAACW,MAAM,KAAK,YAAY,EAAE;QACjCF,WAAW,CAACD,KAAK,GAAG,CAAC;MACvB;IACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}