{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, normalizeClass as _normalizeClass, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"verification-container\"\n};\nconst _hoisted_2 = {\n  class: \"verification-overview\"\n};\nconst _hoisted_3 = {\n  class: \"level-header\"\n};\nconst _hoisted_4 = {\n  class: \"level-features\"\n};\nconst _hoisted_5 = {\n  class: \"feature-item\"\n};\nconst _hoisted_6 = {\n  class: \"feature-item\"\n};\nconst _hoisted_7 = {\n  class: \"feature-item\"\n};\nconst _hoisted_8 = {\n  class: \"level-actions\"\n};\nconst _hoisted_9 = {\n  class: \"level-header\"\n};\nconst _hoisted_10 = {\n  class: \"level-features\"\n};\nconst _hoisted_11 = {\n  class: \"feature-item\"\n};\nconst _hoisted_12 = {\n  class: \"feature-item\"\n};\nconst _hoisted_13 = {\n  class: \"level-actions\"\n};\nconst _hoisted_14 = {\n  class: \"level1-content\"\n};\nconst _hoisted_15 = {\n  class: \"payment-options\"\n};\nconst _hoisted_16 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_17 = {\n  class: \"payment-badge\"\n};\nconst _hoisted_18 = {\n  class: \"payment-icon\"\n};\nconst _hoisted_19 = {\n  class: \"verification-process\"\n};\nconst _hoisted_20 = {\n  class: \"process-steps\"\n};\nconst _hoisted_21 = {\n  class: \"process-step\"\n};\nconst _hoisted_22 = {\n  class: \"process-step\"\n};\nconst _hoisted_23 = {\n  class: \"process-step\"\n};\nconst _hoisted_24 = {\n  class: \"process-step\"\n};\nconst _hoisted_25 = {\n  class: \"process-step\"\n};\nconst _hoisted_26 = {\n  class: \"modal-footer\"\n};\nconst _hoisted_27 = {\n  class: \"level2-content\"\n};\nconst _hoisted_28 = {\n  key: 0,\n  class: \"verification-result-display\"\n};\nconst _hoisted_29 = {\n  class: \"modal-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_cache[41] || (_cache[41] = _createElementVNode(\"h1\", {\n    class: \"page-title\"\n  }, \"实名认证\", -1 /* CACHED */)), _createCommentVNode(\" 认证状态概览 \"), _createElementVNode(\"div\", _hoisted_2, [_createVNode($setup[\"NCard\"], {\n    class: _normalizeClass([\"level-card primary-card\", {\n      'completed': $setup.level2Completed\n    }])\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createVNode($setup[\"NIcon\"], {\n      size: \"24\",\n      color: $setup.level2Completed ? '#18a058' : '#2080f0'\n    }, {\n      default: _withCtx(() => [$setup.level2Completed ? (_openBlock(), _createBlock($setup[\"ShieldCheckmarkOutline\"], {\n        key: 0\n      })) : (_openBlock(), _createBlock($setup[\"ShieldOutline\"], {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"color\"]), _cache[10] || (_cache[10] = _createElementVNode(\"h3\", null, \"二级认证\", -1 /* CACHED */)), $setup.level2Completed ? (_openBlock(), _createBlock($setup[\"NTag\"], {\n      key: 0,\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[8] || (_cache[8] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [8]\n    })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n      key: 1,\n      type: \"info\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"免费认证\")])),\n      _: 1 /* STABLE */,\n      __: [9]\n    }))]), _cache[16] || (_cache[16] = _createElementVNode(\"p\", {\n      class: \"level-description\"\n    }, \"通过姓名和身份证号进行二要素验证，完全免费\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Checkmark\"])]),\n      _: 1 /* STABLE */\n    }), _cache[11] || (_cache[11] = _createElementVNode(\"span\", null, \"二要素身份验证\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_6, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Checkmark\"])]),\n      _: 1 /* STABLE */\n    }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", null, \"访问所有功能\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_7, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Checkmark\"])]),\n      _: 1 /* STABLE */\n    }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"完全免费\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_8, [!$setup.level2Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.startLevel2Verification,\n      loading: $setup.level2Loading\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\" 开始认证 \")])),\n      _: 1 /* STABLE */,\n      __: [14]\n    }, 8 /* PROPS */, [\"loading\"])) : (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [15]\n    }))])]),\n    _: 1 /* STABLE */,\n    __: [16]\n  }, 8 /* PROPS */, [\"class\"]), _createVNode($setup[\"NCard\"], {\n    class: _normalizeClass([\"level-card\", {\n      'completed': $setup.level1Completed,\n      'disabled': !$setup.level2Completed\n    }])\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_9, [_createVNode($setup[\"NIcon\"], {\n      size: \"24\",\n      color: $setup.level1Completed ? '#18a058' : $setup.level2Completed ? '#2080f0' : '#d0d0d0'\n    }, {\n      default: _withCtx(() => [$setup.level1Completed ? (_openBlock(), _createBlock($setup[\"ShieldCheckmarkOutline\"], {\n        key: 0\n      })) : (_openBlock(), _createBlock($setup[\"ShieldOutline\"], {\n        key: 1\n      }))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"color\"]), _cache[20] || (_cache[20] = _createElementVNode(\"h3\", null, \"一级认证\", -1 /* CACHED */)), $setup.level1Completed ? (_openBlock(), _createBlock($setup[\"NTag\"], {\n      key: 0,\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [17]\n    })) : $setup.level2Completed ? (_openBlock(), _createBlock($setup[\"NTag\"], {\n      key: 1,\n      type: \"warning\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"可进行\")])),\n      _: 1 /* STABLE */,\n      __: [18]\n    })) : (_openBlock(), _createBlock($setup[\"NTag\"], {\n      key: 2,\n      type: \"default\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"需完成二级认证\")])),\n      _: 1 /* STABLE */,\n      __: [19]\n    }))]), _cache[26] || (_cache[26] = _createElementVNode(\"p\", {\n      class: \"level-description\"\n    }, \"通过支付宝或微信实名验证，获得更高信任度\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Checkmark\"])]),\n      _: 1 /* STABLE */\n    }), _cache[21] || (_cache[21] = _createElementVNode(\"span\", null, \"支付平台验证\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_12, [_createVNode($setup[\"NIcon\"], {\n      size: \"16\",\n      color: \"#18a058\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Checkmark\"])]),\n      _: 1 /* STABLE */\n    }), _cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"更高信任等级\", -1 /* CACHED */))])]), _createElementVNode(\"div\", _hoisted_13, [!$setup.level1Completed && $setup.level2Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.startLevel1Verification,\n      loading: $setup.level1Loading\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 开始认证 \")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }, 8 /* PROPS */, [\"loading\"])) : $setup.level1Completed ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"已完成\")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    })) : (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 2,\n      disabled: \"\"\n    }, {\n      default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"需完成二级认证\")])),\n      _: 1 /* STABLE */,\n      __: [25]\n    }))])]),\n    _: 1 /* STABLE */,\n    __: [26]\n  }, 8 /* PROPS */, [\"class\"])]), _createCommentVNode(\" 一级认证模态框 \"), _createVNode($setup[\"NModal\"], {\n    show: $setup.showLevel1Modal,\n    \"onUpdate:show\": _cache[3] || (_cache[3] = $event => $setup.showLevel1Modal = $event),\n    preset: \"card\",\n    title: \"一级认证\",\n    style: {\n      \"width\": \"600px\",\n      \"max-width\": \"90vw\"\n    }\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createVNode($setup[\"NButton\"], {\n      onClick: _cache[2] || (_cache[2] = $event => $setup.showLevel1Modal = false)\n    }, {\n      default: _withCtx(() => _cache[35] || (_cache[35] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [35]\n    }), _createVNode($setup[\"NButton\"], {\n      type: \"primary\",\n      onClick: $setup.proceedLevel1Payment,\n      disabled: !$setup.selectedPayment,\n      loading: $setup.level1Loading\n    }, {\n      default: _withCtx(() => [_createTextVNode(\" 确认支付 \" + _toDisplayString($setup.selectedPayment === 'alipay' ? '¥1.2' : '¥1.5'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"disabled\", \"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_14, [_createVNode($setup[\"NAlert\"], {\n      title: \"认证说明\",\n      type: \"info\",\n      style: {\n        \"margin-bottom\": \"24px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\" 选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。 \")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"payment-option\", {\n        'selected': $setup.selectedPayment === 'alipay'\n      }]),\n      onClick: _cache[0] || (_cache[0] = $event => $setup.selectedPayment = 'alipay')\n    }, [_createElementVNode(\"div\", _hoisted_16, [_createVNode($setup[\"NIcon\"], {\n      size: \"32\",\n      color: \"#1677ff\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"LogoAlipay\"])]),\n      _: 1 /* STABLE */\n    })]), _cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n      class: \"payment-info\"\n    }, [_createElementVNode(\"h4\", null, \"支付宝认证\"), _createElementVNode(\"p\", null, \"通过支付宝实名信息进行验证\"), _createElementVNode(\"div\", {\n      class: \"payment-price\"\n    }, [_createElementVNode(\"span\", {\n      class: \"price\"\n    }, \"¥1.2\"), _createElementVNode(\"span\", {\n      class: \"original-price\"\n    }, \"¥2.0\")])], -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_17, [_createVNode($setup[\"NTag\"], {\n      type: \"success\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"推荐\")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    })])], 2 /* CLASS */), _createElementVNode(\"div\", {\n      class: _normalizeClass([\"payment-option\", {\n        'selected': $setup.selectedPayment === 'wechat'\n      }]),\n      onClick: _cache[1] || (_cache[1] = $event => $setup.selectedPayment = 'wechat')\n    }, [_createElementVNode(\"div\", _hoisted_18, [_createVNode($setup[\"NIcon\"], {\n      size: \"32\",\n      color: \"#07c160\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"LogoWechat\"])]),\n      _: 1 /* STABLE */\n    })]), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n      class: \"payment-info\"\n    }, [_createElementVNode(\"h4\", null, \"微信认证\"), _createElementVNode(\"p\", null, \"通过微信实名信息进行验证\"), _createElementVNode(\"div\", {\n      class: \"payment-price\"\n    }, [_createElementVNode(\"span\", {\n      class: \"price\"\n    }, \"¥1.5\")])], -1 /* CACHED */))], 2 /* CLASS */)]), _createElementVNode(\"div\", _hoisted_19, [_cache[34] || (_cache[34] = _createElementVNode(\"h4\", null, \"认证流程：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"CardOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[31] || (_cache[31] = _createElementVNode(\"span\", null, \"选择支付方式\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_22, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowForward\"])]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_23, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"WalletOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[32] || (_cache[32] = _createElementVNode(\"span\", null, \"完成支付\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_24, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"ArrowForward\"])]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_25, [_createVNode($setup[\"NIcon\"], {\n      size: \"20\",\n      color: \"#2080f0\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"CheckmarkCircleOutline\"])]),\n      _: 1 /* STABLE */\n    }), _cache[33] || (_cache[33] = _createElementVNode(\"span\", null, \"认证完成\", -1 /* CACHED */))])])])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"]), _createCommentVNode(\" 二级认证模态框 \"), _createVNode($setup[\"NModal\"], {\n    show: $setup.showLevel2Modal,\n    \"onUpdate:show\": _cache[7] || (_cache[7] = $event => $setup.showLevel2Modal = $event),\n    preset: \"card\",\n    title: \"二级认证\",\n    style: {\n      \"width\": \"600px\",\n      \"max-width\": \"90vw\"\n    }\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_29, [_createVNode($setup[\"NButton\"], {\n      onClick: _cache[6] || (_cache[6] = $event => $setup.showLevel2Modal = false),\n      disabled: $setup.level2Loading\n    }, {\n      default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [38]\n    }, 8 /* PROPS */, [\"disabled\"]), !_ctx.level2VerificationResult?.success ? (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 0,\n      type: \"primary\",\n      onClick: _ctx.submitLevel2Verification,\n      loading: $setup.level2Loading,\n      disabled: !_ctx.isLevel2FormValid\n    }, {\n      default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\" 开始验证 \")])),\n      _: 1 /* STABLE */,\n      __: [39]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\", \"disabled\"])) : (_openBlock(), _createBlock($setup[\"NButton\"], {\n      key: 1,\n      type: \"primary\",\n      onClick: _ctx.completeLevel2Verification\n    }, {\n      default: _withCtx(() => _cache[40] || (_cache[40] = [_createTextVNode(\" 完成认证 \")])),\n      _: 1 /* STABLE */,\n      __: [40]\n    }, 8 /* PROPS */, [\"onClick\"]))])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_27, [_createVNode($setup[\"NAlert\"], {\n      title: \"二要素身份验证\",\n      type: \"info\",\n      style: {\n        \"margin-bottom\": \"24px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\" 二级认证完全免费，通过姓名和身份证号进行二要素验证。 \")])),\n      _: 1 /* STABLE */,\n      __: [36]\n    }), _createVNode($setup[\"NForm\"], {\n      ref: \"level2FormRef\",\n      model: _ctx.level2Form,\n      rules: _ctx.level2Rules,\n      \"label-placement\": \"top\",\n      size: \"medium\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"NFormItem\"], {\n        label: \"真实姓名\",\n        path: \"realName\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NInput\"], {\n          value: _ctx.level2Form.realName,\n          \"onUpdate:value\": _cache[4] || (_cache[4] = $event => _ctx.level2Form.realName = $event),\n          placeholder: \"请输入您的真实姓名\",\n          disabled: $setup.level2Loading\n        }, {\n          prefix: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n            default: _withCtx(() => [_createVNode($setup[\"Person\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode($setup[\"NFormItem\"], {\n        label: \"身份证号码\",\n        path: \"idNumber\"\n      }, {\n        default: _withCtx(() => [_createVNode($setup[\"NInput\"], {\n          value: _ctx.level2Form.idNumber,\n          \"onUpdate:value\": _cache[5] || (_cache[5] = $event => _ctx.level2Form.idNumber = $event),\n          placeholder: \"请输入您的身份证号码\",\n          disabled: $setup.level2Loading,\n          maxlength: \"18\"\n        }, {\n          prefix: _withCtx(() => [_createVNode($setup[\"NIcon\"], null, {\n            default: _withCtx(() => [_createVNode($setup[\"CardOutline\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"value\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"]), _cache[37] || (_cache[37] = _createElementVNode(\"div\", {\n      class: \"verification-instructions\"\n    }, [_createElementVNode(\"h4\", null, \"验证说明：\"), _createElementVNode(\"ul\", null, [_createElementVNode(\"li\", null, \"请确保输入的姓名与身份证上的姓名完全一致\"), _createElementVNode(\"li\", null, \"身份证号码必须是18位有效号码\"), _createElementVNode(\"li\", null, \"验证过程通过权威数据源进行核实\"), _createElementVNode(\"li\", null, \"您的个人信息将被严格保密\")])], -1 /* CACHED */)), _ctx.level2VerificationResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_28, [_createVNode($setup[\"NAlert\"], {\n      title: _ctx.level2VerificationResult.success ? '验证成功' : '验证失败',\n      type: _ctx.level2VerificationResult.success ? 'success' : 'error'\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString(_ctx.level2VerificationResult.message), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"title\", \"type\"])])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"show\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_createCommentVNode", "_hoisted_2", "_createVNode", "$setup", "_normalizeClass", "level2Completed", "_hoisted_3", "size", "color", "_createBlock", "key", "type", "_cache", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "onClick", "startLevel2Verification", "loading", "level2Loading", "disabled", "level1Completed", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "startLevel1Verification", "level1Loading", "show", "showLevel1Modal", "$event", "preset", "title", "style", "footer", "_withCtx", "_hoisted_26", "proceedLevel1Payment", "selectedPayment", "_toDisplayString", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "showLevel2Modal", "_hoisted_29", "_ctx", "level2VerificationResult", "success", "submitLevel2Verification", "isLevel2FormValid", "completeLevel2Verification", "_hoisted_27", "ref", "model", "level2Form", "rules", "level2Rules", "label", "path", "value", "realName", "placeholder", "prefix", "idNumber", "maxlength", "_hoisted_28", "message"], "sources": ["G:\\Project\\KongKuang-Network\\kongkuang-auth\\src\\views\\Verification.vue"], "sourcesContent": ["<template>\r\n  <div class=\"verification-container\">\r\n    <h1 class=\"page-title\">实名认证</h1>\r\n\r\n    <!-- 认证状态概览 -->\r\n    <div class=\"verification-overview\">\r\n      <n-card class=\"level-card primary-card\" :class=\"{ 'completed': level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level2Completed ? '#18a058' : '#2080f0'\">\r\n            <shield-checkmark-outline v-if=\"level2Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>二级认证</h3>\r\n          <n-tag v-if=\"level2Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else type=\"info\" size=\"small\">免费认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过姓名和身份证号进行二要素验证，完全免费</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>二要素身份验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>访问所有功能</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>完全免费</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else disabled>已完成</n-button>\r\n        </div>\r\n      </n-card>\r\n\r\n      <n-card class=\"level-card\" :class=\"{ 'completed': level1Completed, 'disabled': !level2Completed }\">\r\n        <div class=\"level-header\">\r\n          <n-icon size=\"24\" :color=\"level1Completed ? '#18a058' : (level2Completed ? '#2080f0' : '#d0d0d0')\">\r\n            <shield-checkmark-outline v-if=\"level1Completed\" />\r\n            <shield-outline v-else />\r\n          </n-icon>\r\n          <h3>一级认证</h3>\r\n          <n-tag v-if=\"level1Completed\" type=\"success\" size=\"small\">已完成</n-tag>\r\n          <n-tag v-else-if=\"level2Completed\" type=\"warning\" size=\"small\">可进行</n-tag>\r\n          <n-tag v-else type=\"default\" size=\"small\">需完成二级认证</n-tag>\r\n        </div>\r\n        <p class=\"level-description\">通过支付宝或微信实名验证，获得更高信任度</p>\r\n        <div class=\"level-features\">\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>支付平台验证</span>\r\n          </div>\r\n          <div class=\"feature-item\">\r\n            <n-icon size=\"16\" color=\"#18a058\"><checkmark /></n-icon>\r\n            <span>更高信任等级</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"level-actions\">\r\n          <n-button\r\n            v-if=\"!level1Completed && level2Completed\"\r\n            type=\"primary\"\r\n            @click=\"startLevel1Verification\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            开始认证\r\n          </n-button>\r\n          <n-button v-else-if=\"level1Completed\" disabled>已完成</n-button>\r\n          <n-button v-else disabled>需完成二级认证</n-button>\r\n        </div>\r\n      </n-card>\r\n    </div>\r\n\r\n    <!-- 一级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel1Modal\" preset=\"card\" title=\"一级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level1-content\">\r\n        <n-alert title=\"认证说明\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          选择支付宝或微信进行实名认证，认证费用将在认证成功后从您的账户中扣除。\r\n        </n-alert>\r\n\r\n        <div class=\"payment-options\">\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'alipay' }\"\r\n            @click=\"selectedPayment = 'alipay'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#1677ff\">\r\n                <logo-alipay />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>支付宝认证</h4>\r\n              <p>通过支付宝实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.2</span>\r\n                <span class=\"original-price\">¥2.0</span>\r\n              </div>\r\n            </div>\r\n            <div class=\"payment-badge\">\r\n              <n-tag type=\"success\" size=\"small\">推荐</n-tag>\r\n            </div>\r\n          </div>\r\n\r\n          <div\r\n            class=\"payment-option\"\r\n            :class=\"{ 'selected': selectedPayment === 'wechat' }\"\r\n            @click=\"selectedPayment = 'wechat'\"\r\n          >\r\n            <div class=\"payment-icon\">\r\n              <n-icon size=\"32\" color=\"#07c160\">\r\n                <logo-wechat />\r\n              </n-icon>\r\n            </div>\r\n            <div class=\"payment-info\">\r\n              <h4>微信认证</h4>\r\n              <p>通过微信实名信息进行验证</p>\r\n              <div class=\"payment-price\">\r\n                <span class=\"price\">¥1.5</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"verification-process\">\r\n          <h4>认证流程：</h4>\r\n          <div class=\"process-steps\">\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><card-outline /></n-icon>\r\n              <span>选择支付方式</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><wallet-outline /></n-icon>\r\n              <span>完成支付</span>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><arrow-forward /></n-icon>\r\n            </div>\r\n            <div class=\"process-step\">\r\n              <n-icon size=\"20\" color=\"#2080f0\"><checkmark-circle-outline /></n-icon>\r\n              <span>认证完成</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel1Modal = false\">取消</n-button>\r\n          <n-button\r\n            type=\"primary\"\r\n            @click=\"proceedLevel1Payment\"\r\n            :disabled=\"!selectedPayment\"\r\n            :loading=\"level1Loading\"\r\n          >\r\n            确认支付 {{ selectedPayment === 'alipay' ? '¥1.2' : '¥1.5' }}\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n\r\n    <!-- 二级认证模态框 -->\r\n    <n-modal v-model:show=\"showLevel2Modal\" preset=\"card\" title=\"二级认证\" style=\"width: 600px; max-width: 90vw;\">\r\n      <div class=\"level2-content\">\r\n        <n-alert title=\"二要素身份验证\" type=\"info\" style=\"margin-bottom: 24px;\">\r\n          二级认证完全免费，通过姓名和身份证号进行二要素验证。\r\n        </n-alert>\r\n\r\n        <n-form\r\n          ref=\"level2FormRef\"\r\n          :model=\"level2Form\"\r\n          :rules=\"level2Rules\"\r\n          label-placement=\"top\"\r\n          size=\"medium\"\r\n        >\r\n          <n-form-item label=\"真实姓名\" path=\"realName\">\r\n            <n-input\r\n              v-model:value=\"level2Form.realName\"\r\n              placeholder=\"请输入您的真实姓名\"\r\n              :disabled=\"level2Loading\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><person /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n\r\n          <n-form-item label=\"身份证号码\" path=\"idNumber\">\r\n            <n-input\r\n              v-model:value=\"level2Form.idNumber\"\r\n              placeholder=\"请输入您的身份证号码\"\r\n              :disabled=\"level2Loading\"\r\n              maxlength=\"18\"\r\n            >\r\n              <template #prefix>\r\n                <n-icon><card-outline /></n-icon>\r\n              </template>\r\n            </n-input>\r\n          </n-form-item>\r\n        </n-form>\r\n\r\n        <div class=\"verification-instructions\">\r\n          <h4>验证说明：</h4>\r\n          <ul>\r\n            <li>请确保输入的姓名与身份证上的姓名完全一致</li>\r\n            <li>身份证号码必须是18位有效号码</li>\r\n            <li>验证过程通过权威数据源进行核实</li>\r\n            <li>您的个人信息将被严格保密</li>\r\n          </ul>\r\n        </div>\r\n\r\n        <div v-if=\"level2VerificationResult\" class=\"verification-result-display\">\r\n          <n-alert\r\n            :title=\"level2VerificationResult.success ? '验证成功' : '验证失败'\"\r\n            :type=\"level2VerificationResult.success ? 'success' : 'error'\"\r\n          >\r\n            {{ level2VerificationResult.message }}\r\n          </n-alert>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <div class=\"modal-footer\">\r\n          <n-button @click=\"showLevel2Modal = false\" :disabled=\"level2Loading\">取消</n-button>\r\n          <n-button\r\n            v-if=\"!level2VerificationResult?.success\"\r\n            type=\"primary\"\r\n            @click=\"submitLevel2Verification\"\r\n            :loading=\"level2Loading\"\r\n            :disabled=\"!isLevel2FormValid\"\r\n          >\r\n            开始验证\r\n          </n-button>\r\n          <n-button\r\n            v-else\r\n            type=\"primary\"\r\n            @click=\"completeLevel2Verification\"\r\n          >\r\n            完成认证\r\n          </n-button>\r\n        </div>\r\n      </template>\r\n    </n-modal>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport {\r\n  NCard,\r\n  NButton,\r\n  NAlert,\r\n  NIcon,\r\n  NAvatar,\r\n  NModal,\r\n  NTag,\r\n  NSpin,\r\n  NForm,\r\n  NFormItem,\r\n  NInput,\r\n  useMessage\r\n} from 'naive-ui';\r\nimport {\r\n  ShieldOutline,\r\n  ShieldCheckmarkOutline,\r\n  Checkmark,\r\n  LogoAlipay,\r\n  LogoWechat,\r\n  CardOutline,\r\n  WalletOutline,\r\n  CheckmarkCircleOutline,\r\n  CloseCircleOutline,\r\n  ArrowForward,\r\n  Person\r\n} from '@vicons/ionicons5';\r\nimport { useUserStore } from '../stores/user';\r\nimport { getApiClient } from '../utils/api';\r\n\r\nconst router = useRouter();\r\nconst message = useMessage();\r\nconst userStore = useUserStore();\r\nconst apiClient = getApiClient();\r\n\r\n// 认证状态\r\nconst level1Completed = ref(false);\r\nconst level2Completed = ref(false);\r\nconst level1Loading = ref(false);\r\nconst level2Loading = ref(false);\r\n\r\n// 模态框状态\r\nconst showLevel1Modal = ref(false);\r\nconst showLevel2Modal = ref(false);\r\n\r\n// 一级认证相关\r\nconst selectedPayment = ref('');\r\n\r\n// 二级认证相关\r\nconst faceVerificationStarted = ref(false);\r\nconst faceVerifying = ref(false);\r\nconst faceVerificationResult = ref(null);\r\nconst videoRef = ref(null);\r\nconst canvasRef = ref(null);\r\nconst mediaStream = ref(null);\r\n\r\n// 阿里云人脸识别配置\r\nconst FACE_API_CONFIG = {\r\n  appKey: '*********',\r\n  appSecret: 'l77vwNhbVtax65GRQz9NnnOxxREz5AZS',\r\n  appCode: 'ac2c8231f12445928b757dd27e67dbce',\r\n  apiUrl: 'https://face.market.alicloudapi.com/face/human_face_compare'\r\n};\r\n\r\n// 检查认证状态\r\nconst checkVerificationStatus = async () => {\r\n  try {\r\n    const response = await apiClient.get('/users/verification-status');\r\n    level1Completed.value = response.data.level1Completed || false;\r\n    level2Completed.value = response.data.level2Completed || false;\r\n  } catch (error) {\r\n    console.error('获取认证状态失败:', error);\r\n  }\r\n};\r\n\r\n// 开始一级认证\r\nconst startLevel1Verification = () => {\r\n  showLevel1Modal.value = true;\r\n  selectedPayment.value = 'alipay'; // 默认选择支付宝\r\n};\r\n\r\n// 处理一级认证支付\r\nconst proceedLevel1Payment = async () => {\r\n  if (!selectedPayment.value) {\r\n    message.warning('请选择支付方式');\r\n    return;\r\n  }\r\n\r\n  level1Loading.value = true;\r\n\r\n  try {\r\n    // 调用支付API\r\n    const response = await apiClient.post('/verification/level1/payment', {\r\n      paymentMethod: selectedPayment.value,\r\n      amount: selectedPayment.value === 'alipay' ? 1.2 : 1.5\r\n    });\r\n\r\n    if (response.data.success) {\r\n      // 跳转到支付页面或处理支付\r\n      window.open(response.data.paymentUrl, '_blank');\r\n      message.success('支付链接已打开，请完成支付');\r\n      showLevel1Modal.value = false;\r\n\r\n      // 轮询检查支付状态\r\n      checkPaymentStatus(response.data.orderId);\r\n    }\r\n  } catch (error) {\r\n    message.error(error.response?.data?.message || '发起支付失败');\r\n  } finally {\r\n    level1Loading.value = false;\r\n  }\r\n};\r\n\r\n// 检查支付状态\r\nconst checkPaymentStatus = async (orderId) => {\r\n  const maxAttempts = 30; // 最多检查30次，每次间隔2秒\r\n  let attempts = 0;\r\n\r\n  const checkStatus = async () => {\r\n    try {\r\n      const response = await apiClient.get(`/verification/level1/payment-status/${orderId}`);\r\n\r\n      if (response.data.status === 'completed') {\r\n        level1Completed.value = true;\r\n        message.success('一级认证完成！');\r\n        return;\r\n      } else if (response.data.status === 'failed') {\r\n        message.error('支付失败，请重试');\r\n        return;\r\n      }\r\n\r\n      attempts++;\r\n      if (attempts < maxAttempts) {\r\n        setTimeout(checkStatus, 2000);\r\n      }\r\n    } catch (error) {\r\n      console.error('检查支付状态失败:', error);\r\n    }\r\n  };\r\n\r\n  checkStatus();\r\n};\r\n\r\n// 开始二级认证\r\nconst startLevel2Verification = () => {\r\n  if (!level1Completed.value) {\r\n    message.warning('请先完成一级认证');\r\n    return;\r\n  }\r\n  showLevel2Modal.value = true;\r\n};\r\n\r\n// 开始人脸识别\r\nconst startFaceVerification = async () => {\r\n  faceVerificationStarted.value = true;\r\n  faceVerifying.value = true;\r\n  faceVerificationResult.value = null;\r\n\r\n  try {\r\n    // 获取摄像头权限\r\n    mediaStream.value = await navigator.mediaDevices.getUserMedia({\r\n      video: {\r\n        width: 640,\r\n        height: 480,\r\n        facingMode: 'user'\r\n      }\r\n    });\r\n\r\n    if (videoRef.value) {\r\n      videoRef.value.srcObject = mediaStream.value;\r\n\r\n      // 等待视频加载\r\n      await new Promise((resolve) => {\r\n        videoRef.value.onloadedmetadata = resolve;\r\n      });\r\n\r\n      // 延迟3秒后自动拍照进行识别\r\n      setTimeout(() => {\r\n        captureAndVerifyFace();\r\n      }, 3000);\r\n    }\r\n  } catch (error) {\r\n    console.error('获取摄像头失败:', error);\r\n    message.error('无法访问摄像头，请检查权限设置');\r\n    faceVerifying.value = false;\r\n  }\r\n};\r\n\r\n// 拍照并进行人脸识别\r\nconst captureAndVerifyFace = async () => {\r\n  if (!videoRef.value || !canvasRef.value) return;\r\n\r\n  const canvas = canvasRef.value;\r\n  const video = videoRef.value;\r\n  const context = canvas.getContext('2d');\r\n\r\n  // 设置canvas尺寸\r\n  canvas.width = video.videoWidth;\r\n  canvas.height = video.videoHeight;\r\n\r\n  // 绘制当前帧到canvas\r\n  context.drawImage(video, 0, 0);\r\n\r\n  // 获取base64图片数据\r\n  const imageData = canvas.toDataURL('image/jpeg', 0.8);\r\n\r\n  try {\r\n    // 调用阿里云人脸识别API\r\n    const result = await callFaceRecognitionAPI(imageData);\r\n\r\n    faceVerifying.value = false;\r\n    faceVerificationResult.value = result;\r\n\r\n    if (result.success) {\r\n      message.success('人脸识别成功！');\r\n    } else {\r\n      message.error(result.message || '人脸识别失败，请重试');\r\n    }\r\n  } catch (error) {\r\n    faceVerifying.value = false;\r\n    faceVerificationResult.value = {\r\n      success: false,\r\n      message: '人脸识别服务异常，请稍后重试'\r\n    };\r\n    message.error('人脸识别失败');\r\n  }\r\n};\r\n\r\n// 调用阿里云人脸识别API\r\nconst callFaceRecognitionAPI = async (imageData) => {\r\n  try {\r\n    // 这里应该调用后端API，由后端调用阿里云服务\r\n    const response = await apiClient.post('/verification/level2/face-recognition', {\r\n      image: imageData,\r\n      userId: userStore.user?.id\r\n    });\r\n\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('人脸识别API调用失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// 重新进行人脸识别\r\nconst retryFaceVerification = () => {\r\n  faceVerificationResult.value = null;\r\n  faceVerifying.value = true;\r\n\r\n  setTimeout(() => {\r\n    captureAndVerifyFace();\r\n  }, 1000);\r\n};\r\n\r\n// 完成人脸识别认证\r\nconst completeFaceVerification = async () => {\r\n  try {\r\n    level2Loading.value = true;\r\n\r\n    const response = await apiClient.post('/verification/level2/complete', {\r\n      verificationId: faceVerificationResult.value.verificationId\r\n    });\r\n\r\n    if (response.data.success) {\r\n      level2Completed.value = true;\r\n      showLevel2Modal.value = false;\r\n      message.success('二级认证完成！');\r\n    }\r\n  } catch (error) {\r\n    message.error(error.response?.data?.message || '完成认证失败');\r\n  } finally {\r\n    level2Loading.value = false;\r\n  }\r\n};\r\n\r\n// 停止摄像头\r\nconst stopCamera = () => {\r\n  if (mediaStream.value) {\r\n    mediaStream.value.getTracks().forEach(track => track.stop());\r\n    mediaStream.value = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时检查认证状态\r\nonMounted(() => {\r\n  checkVerificationStatus();\r\n});\r\n\r\n// 组件卸载时停止摄像头\r\nonUnmounted(() => {\r\n  stopCamera();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.verification-container {\r\n  padding: 16px;\r\n  min-height: 100%;\r\n}\r\n\r\n.page-title {\r\n  font-size: 24px;\r\n  font-weight: 600;\r\n  margin-bottom: 24px;\r\n  color: var(--n-text-color-1);\r\n}\r\n\r\n.verification-overview {\r\n  display: grid;\r\n  grid-template-columns: 1fr 1fr;\r\n  gap: 24px;\r\n  margin-bottom: 32px;\r\n}\r\n\r\n.level-card {\r\n  border-radius: 16px;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.level-card:hover {\r\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);\r\n  transform: translateY(-2px);\r\n}\r\n\r\n.level-card.completed {\r\n  border: 2px solid #18a058;\r\n  background: linear-gradient(135deg, rgba(24, 160, 88, 0.05) 0%, rgba(24, 160, 88, 0.02) 100%);\r\n}\r\n\r\n.level-card.disabled {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.level-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.level-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  flex: 1;\r\n}\r\n\r\n.level-description {\r\n  color: var(--n-text-color-2);\r\n  margin-bottom: 16px;\r\n  line-height: 1.5;\r\n}\r\n\r\n.level-features {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.feature-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n.level-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 一级认证模态框样式 */\r\n.level1-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.payment-options {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 16px;\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.payment-option {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 16px;\r\n  border: 2px solid var(--n-border-color);\r\n  border-radius: 12px;\r\n  cursor: pointer;\r\n  transition: all 0.3s ease;\r\n  position: relative;\r\n}\r\n\r\n.payment-option:hover {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-hover);\r\n}\r\n\r\n.payment-option.selected {\r\n  border-color: var(--n-primary-color);\r\n  background-color: var(--n-primary-color-suppl);\r\n}\r\n\r\n.payment-icon {\r\n  margin-right: 16px;\r\n}\r\n\r\n.payment-info {\r\n  flex: 1;\r\n}\r\n\r\n.payment-info h4 {\r\n  margin: 0 0 4px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.payment-info p {\r\n  margin: 0 0 8px 0;\r\n  color: var(--n-text-color-2);\r\n  font-size: 14px;\r\n}\r\n\r\n.payment-price {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.price {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n  color: var(--n-error-color);\r\n}\r\n\r\n.original-price {\r\n  font-size: 14px;\r\n  color: var(--n-text-color-3);\r\n  text-decoration: line-through;\r\n}\r\n\r\n.payment-badge {\r\n  position: absolute;\r\n  top: 8px;\r\n  right: 8px;\r\n}\r\n\r\n.verification-process {\r\n  margin-top: 24px;\r\n}\r\n\r\n.verification-process h4 {\r\n  margin: 0 0 16px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.process-steps {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  padding: 16px;\r\n  background-color: var(--n-color-target);\r\n  border-radius: 8px;\r\n}\r\n\r\n.process-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 12px;\r\n  color: var(--n-text-color-2);\r\n}\r\n\r\n/* 二级认证模态框样式 */\r\n.level2-content {\r\n  padding: 8px 0;\r\n}\r\n\r\n.face-verification-area {\r\n  text-align: center;\r\n  padding: 24px;\r\n}\r\n\r\n.face-preview {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n.face-instructions {\r\n  text-align: left;\r\n  max-width: 400px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.face-instructions h4 {\r\n  margin: 0 0 12px 0;\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.face-instructions ul {\r\n  margin: 0;\r\n  padding-left: 20px;\r\n}\r\n\r\n.face-instructions li {\r\n  margin-bottom: 8px;\r\n  color: var(--n-text-color-2);\r\n  line-height: 1.5;\r\n}\r\n\r\n.face-verification-camera {\r\n  text-align: center;\r\n}\r\n\r\n.camera-container {\r\n  position: relative;\r\n  display: inline-block;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.camera-container video {\r\n  width: 480px;\r\n  height: 360px;\r\n  object-fit: cover;\r\n}\r\n\r\n.camera-overlay {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.face-frame {\r\n  width: 200px;\r\n  height: 240px;\r\n  border: 3px solid #2080f0;\r\n  border-radius: 50%;\r\n  box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.verification-status {\r\n  margin-top: 16px;\r\n  min-height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.verification-result {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  font-size: 16px;\r\n  font-weight: 500;\r\n}\r\n\r\n.modal-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 12px;\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .verification-overview {\r\n    grid-template-columns: 1fr;\r\n    gap: 16px;\r\n  }\r\n\r\n  .payment-option {\r\n    flex-direction: column;\r\n    text-align: center;\r\n    gap: 12px;\r\n  }\r\n\r\n  .payment-icon {\r\n    margin-right: 0;\r\n  }\r\n\r\n  .process-steps {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n\r\n  .camera-container video {\r\n    width: 320px;\r\n    height: 240px;\r\n  }\r\n\r\n  .face-frame {\r\n    width: 150px;\r\n    height: 180px;\r\n  }\r\n}\r\n\r\n@media (max-width: 480px) {\r\n  .verification-container {\r\n    padding: 12px;\r\n  }\r\n\r\n  .camera-container video {\r\n    width: 280px;\r\n    height: 210px;\r\n  }\r\n}\r\n</style>"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAI5BA,KAAK,EAAC;AAAuB;;EAEzBA,KAAK,EAAC;AAAc;;EAUpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAe;;EAcrBA,KAAK,EAAC;AAAc;;EAWpBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAe;;EAiBvBA,KAAK,EAAC;AAAgB;;EAKpBA,KAAK,EAAC;AAAiB;;EAMnBA,KAAK,EAAC;AAAc;;EAapBA,KAAK,EAAC;AAAe;;EAUrBA,KAAK,EAAC;AAAc;;EAexBA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EAIpBA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAc;;EASxBA,KAAK,EAAC;AAAc;;EAgBtBA,KAAK,EAAC;AAAgB;;;EAgDYA,KAAK,EAAC;;;EAWtCA,KAAK,EAAC;AAAc;;uBAxO/BC,mBAAA,CA6PM,OA7PNC,UA6PM,G,4BA5PJC,mBAAA,CAAgC;IAA5BH,KAAK,EAAC;EAAY,GAAC,MAAI,qBAE3BI,mBAAA,YAAe,EACfD,mBAAA,CA0EM,OA1ENE,UA0EM,GAzEJC,YAAA,CAoCSC,MAAA;IApCDP,KAAK,EAAAQ,eAAA,EAAC,yBAAyB;MAAA,aAAwBD,MAAA,CAAAE;IAAe;;sBAC5E,MAQM,CARNN,mBAAA,CAQM,OARNO,UAQM,GAPJJ,YAAA,CAGSC,MAAA;MAHDI,IAAI,EAAC,IAAI;MAAEC,KAAK,EAAEL,MAAA,CAAAE,eAAe;;wBACvC,MAAmD,CAAnBF,MAAA,CAAAE,eAAe,I,cAA/CI,YAAA,CAAmDN,MAAA;QAAAO,GAAA;MAAA,O,cACnDD,YAAA,CAAyBN,MAAA;QAAAO,GAAA;MAAA,I;;8DAE3BX,mBAAA,CAAa,YAAT,MAAI,qBACKI,MAAA,CAAAE,eAAe,I,cAA5BI,YAAA,CAAqEN,MAAA;;MAAvCQ,IAAI,EAAC,SAAS;MAACJ,IAAI,EAAC;;wBAAQ,MAAGK,MAAA,QAAAA,MAAA,O,iBAAH,KAAG,E;;;yBAC7DH,YAAA,CAAmDN,MAAA;;MAArCQ,IAAI,EAAC,MAAM;MAACJ,IAAI,EAAC;;wBAAQ,MAAIK,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;uCAE7Cb,mBAAA,CAAsD;MAAnDH,KAAK,EAAC;IAAmB,GAAC,uBAAqB,qBAClDG,mBAAA,CAaM,OAbNc,UAaM,GAZJd,mBAAA,CAGM,OAHNe,UAGM,GAFJZ,YAAA,CAAwDC,MAAA;MAAhDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbN,YAAA,CAAaC,MAAA,e;;oCAC/CJ,mBAAA,CAAoB,cAAd,SAAO,oB,GAEfA,mBAAA,CAGM,OAHNgB,UAGM,GAFJb,YAAA,CAAwDC,MAAA;MAAhDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbN,YAAA,CAAaC,MAAA,e;;oCAC/CJ,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAGM,OAHNiB,UAGM,GAFJd,YAAA,CAAwDC,MAAA;MAAhDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbN,YAAA,CAAaC,MAAA,e;;oCAC/CJ,mBAAA,CAAiB,cAAX,MAAI,oB,KAGdA,mBAAA,CAUM,OAVNkB,UAUM,G,CARKd,MAAA,CAAAE,eAAe,I,cADxBI,YAAA,CAOWN,MAAA;;MALTQ,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEf,MAAA,CAAAgB,uBAAuB;MAC9BC,OAAO,EAAEjB,MAAA,CAAAkB;;wBACX,MAEDT,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;qDACAH,YAAA,CAAwCN,MAAA;;MAAvBmB,QAAQ,EAAR;;wBAAS,MAAGV,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;gCAIjCV,YAAA,CAkCSC,MAAA;IAlCDP,KAAK,EAAAQ,eAAA,EAAC,YAAY;MAAA,aAAwBD,MAAA,CAAAoB,eAAe;MAAA,aAAepB,MAAA,CAAAE;IAAe;;sBAC7F,MASM,CATNN,mBAAA,CASM,OATNyB,UASM,GARJtB,YAAA,CAGSC,MAAA;MAHDI,IAAI,EAAC,IAAI;MAAEC,KAAK,EAAEL,MAAA,CAAAoB,eAAe,eAAgBpB,MAAA,CAAAE,eAAe;;wBACtE,MAAmD,CAAnBF,MAAA,CAAAoB,eAAe,I,cAA/Cd,YAAA,CAAmDN,MAAA;QAAAO,GAAA;MAAA,O,cACnDD,YAAA,CAAyBN,MAAA;QAAAO,GAAA;MAAA,I;;8DAE3BX,mBAAA,CAAa,YAAT,MAAI,qBACKI,MAAA,CAAAoB,eAAe,I,cAA5Bd,YAAA,CAAqEN,MAAA;;MAAvCQ,IAAI,EAAC,SAAS;MAACJ,IAAI,EAAC;;wBAAQ,MAAGK,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;UAC3CT,MAAA,CAAAE,eAAe,I,cAAjCI,YAAA,CAA0EN,MAAA;;MAAvCQ,IAAI,EAAC,SAAS;MAACJ,IAAI,EAAC;;wBAAQ,MAAGK,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;yBAClEH,YAAA,CAAyDN,MAAA;;MAA3CQ,IAAI,EAAC,SAAS;MAACJ,IAAI,EAAC;;wBAAQ,MAAOK,MAAA,SAAAA,MAAA,Q,iBAAP,SAAO,E;;;uCAEnDb,mBAAA,CAAqD;MAAlDH,KAAK,EAAC;IAAmB,GAAC,sBAAoB,qBACjDG,mBAAA,CASM,OATN0B,WASM,GARJ1B,mBAAA,CAGM,OAHN2B,WAGM,GAFJxB,YAAA,CAAwDC,MAAA;MAAhDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbN,YAAA,CAAaC,MAAA,e;;oCAC/CJ,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAGM,OAHN4B,WAGM,GAFJzB,YAAA,CAAwDC,MAAA;MAAhDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAa,CAAbN,YAAA,CAAaC,MAAA,e;;oCAC/CJ,mBAAA,CAAmB,cAAb,QAAM,oB,KAGhBA,mBAAA,CAWM,OAXN6B,WAWM,G,CATKzB,MAAA,CAAAoB,eAAe,IAAIpB,MAAA,CAAAE,eAAe,I,cAD3CI,YAAA,CAOWN,MAAA;;MALTQ,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEf,MAAA,CAAA0B,uBAAuB;MAC9BT,OAAO,EAAEjB,MAAA,CAAA2B;;wBACX,MAEDlB,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;sCACqBT,MAAA,CAAAoB,eAAe,I,cAApCd,YAAA,CAA6DN,MAAA;;MAAvBmB,QAAQ,EAAR;;wBAAS,MAAGV,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;yBAClDH,YAAA,CAA4CN,MAAA;;MAA3BmB,QAAQ,EAAR;;wBAAS,MAAOV,MAAA,SAAAA,MAAA,Q,iBAAP,SAAO,E;;;;;;kCAKvCZ,mBAAA,aAAgB,EAChBE,YAAA,CAwFUC,MAAA;IAxFO4B,IAAI,EAAE5B,MAAA,CAAA6B,eAAe;yDAAf7B,MAAA,CAAA6B,eAAe,GAAAC,MAAA;IAAEC,MAAM,EAAC,MAAM;IAACC,KAAK,EAAC,MAAM;IAACC,KAAsC,EAAtC;MAAA;MAAA;IAAA;;IA2EtDC,MAAM,EAAAC,QAAA,CACf,MAUM,CAVNvC,mBAAA,CAUM,OAVNwC,WAUM,GATJrC,YAAA,CAAwDC,MAAA;MAA7Ce,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAE9B,MAAA,CAAA6B,eAAe;;wBAAU,MAAEpB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC7CV,YAAA,CAOWC,MAAA;MANTQ,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEf,MAAA,CAAAqC,oBAAoB;MAC3BlB,QAAQ,GAAGnB,MAAA,CAAAsC,eAAe;MAC1BrB,OAAO,EAAEjB,MAAA,CAAA2B;;wBACX,MACM,C,iBADN,QACM,GAAAY,gBAAA,CAAGvC,MAAA,CAAAsC,eAAe,gD;;;sBAnF7B,MAwEM,CAxEN1C,mBAAA,CAwEM,OAxEN4C,WAwEM,GAvEJzC,YAAA,CAEUC,MAAA;MAFDgC,KAAK,EAAC,MAAM;MAACxB,IAAI,EAAC,MAAM;MAACyB,KAA4B,EAA5B;QAAA;MAAA;;wBAA6B,MAE/DxB,MAAA,SAAAA,MAAA,Q,iBAF+D,uCAE/D,E;;;QAEAb,mBAAA,CA0CM,OA1CN6C,WA0CM,GAzCJ7C,mBAAA,CAqBM;MApBJH,KAAK,EAAAQ,eAAA,EAAC,gBAAgB;QAAA,YACAD,MAAA,CAAAsC,eAAe;MAAA;MACpCvB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAE9B,MAAA,CAAAsC,eAAe;QAEvB1C,mBAAA,CAIM,OAJN8C,WAIM,GAHJ3C,YAAA,CAESC,MAAA;MAFDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAAe,CAAfN,YAAA,CAAeC,MAAA,gB;;sCAGnBJ,mBAAA,CAOM;MAPDH,KAAK,EAAC;IAAc,IACvBG,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAAoB,WAAjB,eAAa,GAChBA,mBAAA,CAGM;MAHDH,KAAK,EAAC;IAAe,IACxBG,mBAAA,CAA+B;MAAzBH,KAAK,EAAC;IAAO,GAAC,MAAI,GACxBG,mBAAA,CAAwC;MAAlCH,KAAK,EAAC;IAAgB,GAAC,MAAI,E,uBAGrCG,mBAAA,CAEM,OAFN+C,WAEM,GADJ5C,YAAA,CAA6CC,MAAA;MAAtCQ,IAAI,EAAC,SAAS;MAACJ,IAAI,EAAC;;wBAAQ,MAAEK,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;2BAIzCb,mBAAA,CAiBM;MAhBJH,KAAK,EAAAQ,eAAA,EAAC,gBAAgB;QAAA,YACAD,MAAA,CAAAsC,eAAe;MAAA;MACpCvB,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAE9B,MAAA,CAAAsC,eAAe;QAEvB1C,mBAAA,CAIM,OAJNgD,WAIM,GAHJ7C,YAAA,CAESC,MAAA;MAFDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBACtB,MAAe,CAAfN,YAAA,CAAeC,MAAA,gB;;sCAGnBJ,mBAAA,CAMM;MANDH,KAAK,EAAC;IAAc,IACvBG,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAmB,WAAhB,cAAY,GACfA,mBAAA,CAEM;MAFDH,KAAK,EAAC;IAAe,IACxBG,mBAAA,CAA+B;MAAzBH,KAAK,EAAC;IAAO,GAAC,MAAI,E,0CAMhCG,mBAAA,CAsBM,OAtBNiD,WAsBM,G,4BArBJjD,mBAAA,CAAc,YAAV,OAAK,qBACTA,mBAAA,CAmBM,OAnBNkD,WAmBM,GAlBJlD,mBAAA,CAGM,OAHNmD,WAGM,GAFJhD,YAAA,CAA2DC,MAAA;MAAnDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAgB,CAAhBN,YAAA,CAAgBC,MAAA,iB;;oCAClDJ,mBAAA,CAAmB,cAAb,QAAM,oB,GAEdA,mBAAA,CAEM,OAFNoD,WAEM,GADJjD,YAAA,CAA4DC,MAAA;MAApDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAiB,CAAjBN,YAAA,CAAiBC,MAAA,kB;;UAErDJ,mBAAA,CAGM,OAHNqD,WAGM,GAFJlD,YAAA,CAA6DC,MAAA;MAArDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAkB,CAAlBN,YAAA,CAAkBC,MAAA,mB;;oCACpDJ,mBAAA,CAAiB,cAAX,MAAI,oB,GAEZA,mBAAA,CAEM,OAFNsD,WAEM,GADJnD,YAAA,CAA4DC,MAAA;MAApDI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAAiB,CAAjBN,YAAA,CAAiBC,MAAA,kB;;UAErDJ,mBAAA,CAGM,OAHNuD,WAGM,GAFJpD,YAAA,CAAuEC,MAAA;MAA/DI,IAAI,EAAC,IAAI;MAACC,KAAK,EAAC;;wBAAU,MAA4B,CAA5BN,YAAA,CAA4BC,MAAA,4B;;oCAC9DJ,mBAAA,CAAiB,cAAX,MAAI,oB;;+BAqBpBC,mBAAA,aAAgB,EAChBE,YAAA,CAgFUC,MAAA;IAhFO4B,IAAI,EAAE5B,MAAA,CAAAoD,eAAe;yDAAfpD,MAAA,CAAAoD,eAAe,GAAAtB,MAAA;IAAEC,MAAM,EAAC,MAAM;IAACC,KAAK,EAAC,MAAM;IAACC,KAAsC,EAAtC;MAAA;MAAA;IAAA;;IA2DtDC,MAAM,EAAAC,QAAA,CACf,MAkBM,CAlBNvC,mBAAA,CAkBM,OAlBNyD,WAkBM,GAjBJtD,YAAA,CAAkFC,MAAA;MAAvEe,OAAK,EAAAN,MAAA,QAAAA,MAAA,MAAAqB,MAAA,IAAE9B,MAAA,CAAAoD,eAAe;MAAWjC,QAAQ,EAAEnB,MAAA,CAAAkB;;wBAAe,MAAET,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;sCAE9D6C,IAAA,CAAAC,wBAAwB,EAAEC,OAAO,I,cAD1ClD,YAAA,CAQWN,MAAA;;MANTQ,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEuC,IAAA,CAAAG,wBAAwB;MAC/BxC,OAAO,EAAEjB,MAAA,CAAAkB,aAAa;MACtBC,QAAQ,GAAGmC,IAAA,CAAAI;;wBACb,MAEDjD,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;4EACAH,YAAA,CAMWN,MAAA;;MAJTQ,IAAI,EAAC,SAAS;MACbO,OAAK,EAAEuC,IAAA,CAAAK;;wBACT,MAEDlD,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;sBA5EJ,MAwDM,CAxDNb,mBAAA,CAwDM,OAxDNgE,WAwDM,GAvDJ7D,YAAA,CAEUC,MAAA;MAFDgC,KAAK,EAAC,SAAS;MAACxB,IAAI,EAAC,MAAM;MAACyB,KAA4B,EAA5B;QAAA;MAAA;;wBAA6B,MAElExB,MAAA,SAAAA,MAAA,Q,iBAFkE,8BAElE,E;;;QAEAV,YAAA,CA+BSC,MAAA;MA9BP6D,GAAG,EAAC,eAAe;MAClBC,KAAK,EAAER,IAAA,CAAAS,UAAU;MACjBC,KAAK,EAAEV,IAAA,CAAAW,WAAW;MACnB,iBAAe,EAAC,KAAK;MACrB7D,IAAI,EAAC;;wBAEL,MAUc,CAVdL,YAAA,CAUcC,MAAA;QAVDkE,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC7B,MAQU,CARVpE,YAAA,CAQUC,MAAA;UAPAoE,KAAK,EAAEd,IAAA,CAAAS,UAAU,CAACM,QAAQ;gEAAnBf,IAAA,CAAAS,UAAU,CAACM,QAAQ,GAAAvC,MAAA;UAClCwC,WAAW,EAAC,WAAW;UACtBnD,QAAQ,EAAEnB,MAAA,CAAAkB;;UAEAqD,MAAM,EAAApC,QAAA,CACf,MAA2B,CAA3BpC,YAAA,CAA2BC,MAAA;8BAAnB,MAAU,CAAVD,YAAA,CAAUC,MAAA,Y;;;;;;UAKxBD,YAAA,CAWcC,MAAA;QAXDkE,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAC9B,MASU,CATVpE,YAAA,CASUC,MAAA;UARAoE,KAAK,EAAEd,IAAA,CAAAS,UAAU,CAACS,QAAQ;gEAAnBlB,IAAA,CAAAS,UAAU,CAACS,QAAQ,GAAA1C,MAAA;UAClCwC,WAAW,EAAC,YAAY;UACvBnD,QAAQ,EAAEnB,MAAA,CAAAkB,aAAa;UACxBuD,SAAS,EAAC;;UAECF,MAAM,EAAApC,QAAA,CACf,MAAiC,CAAjCpC,YAAA,CAAiCC,MAAA;8BAAzB,MAAgB,CAAhBD,YAAA,CAAgBC,MAAA,iB;;;;;;;;uEAMhCJ,mBAAA,CAQM;MARDH,KAAK,EAAC;IAA2B,IACpCG,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAKK,aAJHA,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAAwB,YAApB,iBAAe,GACnBA,mBAAA,CAAwB,YAApB,iBAAe,GACnBA,mBAAA,CAAqB,YAAjB,cAAY,E,uBAIT0D,IAAA,CAAAC,wBAAwB,I,cAAnC7D,mBAAA,CAOM,OAPNgF,WAOM,GANJ3E,YAAA,CAKUC,MAAA;MAJPgC,KAAK,EAAEsB,IAAA,CAAAC,wBAAwB,CAACC,OAAO;MACvChD,IAAI,EAAE8C,IAAA,CAAAC,wBAAwB,CAACC,OAAO;;wBAEvC,MAAsC,C,kCAAnCF,IAAA,CAAAC,wBAAwB,CAACoB,OAAO,iB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}