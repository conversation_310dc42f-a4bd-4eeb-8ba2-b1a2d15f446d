{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport { ref, onMounted, computed } from 'vue';\nimport { NSpin, NGrid, NGi, NCard, NAlert, NDescriptions, NDescriptionsItem, NButton, NTag, NList, NListItem, NIcon, NEmpty, useMessage } from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport { CheckmarkCircleOutline, CloseCircleOutline, AppsOutline } from '@vicons/ionicons5';\nexport default {\n  __name: 'Dashboard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const userStore = useUserStore();\n    const message = useMessage();\n    const recentApps = ref([]);\n\n    // 安全相关数据\n    const securityScore = ref(75); // 安全评分 0-100\n\n    const securityItems = computed(() => [{\n      key: 'email_verified',\n      name: '邮箱验证',\n      status: userStore.user?.is_email_verified || false,\n      actionText: '去验证'\n    }, {\n      key: 'phone_verified',\n      name: '手机验证',\n      status: userStore.user?.is_phone_verified || false,\n      actionText: '去验证'\n    }, {\n      key: 'level2_verified',\n      name: '实名认证',\n      status: userStore.user?.level2_verified || false,\n      actionText: '去认证'\n    }, {\n      key: 'mfa_enabled',\n      name: '双因子认证',\n      status: userStore.user?.security_mfa_enabled || false,\n      actionText: '去开启'\n    }]);\n\n    // 检查是否为深色模式\n    const isDarkMode = computed(() => {\n      const themeMode = localStorage.getItem('theme') || 'system';\n      if (themeMode === 'system') {\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n      }\n      return themeMode === 'dark';\n    });\n    const formatDateTime = dateString => {\n      if (!dateString) return 'N/A';\n      const date = new Date(dateString);\n      // Using toLocaleString for a more standard format, customize as needed\n      return date.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit',\n        hour12: false\n      }).replace(/\\//g, '-');\n    };\n\n    // 安全相关方法\n    const getSecurityArcPath = score => {\n      // 将分数转换为弧度 (0-100 映射到 0-π)\n      const angle = score / 100 * Math.PI;\n      const x = 100 + 80 * Math.cos(Math.PI - angle);\n      const y = 100 - 80 * Math.sin(Math.PI - angle);\n      return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n    };\n    const getSecurityColor = score => {\n      if (score >= 80) return '#18a058'; // 绿色 - 安全\n      if (score >= 60) return '#f0a020'; // 橙色 - 一般\n      return '#d03050'; // 红色 - 危险\n    };\n    const getSecurityLevelType = score => {\n      if (score >= 80) return 'success';\n      if (score >= 60) return 'warning';\n      return 'error';\n    };\n    const getSecurityLevelText = score => {\n      if (score >= 80) return '安全';\n      if (score >= 60) return '一般';\n      return '危险';\n    };\n\n    // 紧凑型仪表盘弧线路径计算\n    const getCompactSecurityArcPath = score => {\n      // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n      const angle = score / 100 * Math.PI;\n      const x = 60 + 45 * Math.cos(Math.PI - angle);\n      const y = 65 - 45 * Math.sin(Math.PI - angle);\n      return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n    };\n    const handleSecurityAction = key => {\n      switch (key) {\n        case 'email_verified':\n          message.info('邮箱验证功能开发中');\n          break;\n        case 'phone_verified':\n          message.info('手机验证功能开发中');\n          break;\n        case 'level2_verified':\n          window.location.href = '/verification';\n          break;\n        case 'mfa_enabled':\n          window.location.href = '/security';\n          break;\n        default:\n          message.info('功能开发中');\n      }\n    };\n\n    // 计算安全评分\n    const calculateSecurityScore = () => {\n      const items = securityItems.value;\n      const completedItems = items.filter(item => item.status).length;\n      const score = Math.round(completedItems / items.length * 100);\n      securityScore.value = score;\n    };\n    const fetchDashboardData = async () => {\n      loading.value = true;\n      try {\n        const apiClient = getApiClient();\n        const response = await apiClient.get('/dashboard');\n        if (response.data && response.data.success) {\n          // 更新用户信息，使用后端返回的格式化数据\n          if (response.data.user) {\n            userStore.user = {\n              ...userStore.user,\n              ...response.data.user,\n              // 使用后端返回的格式化时间，如果没有则使用原始数据\n              createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n              lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n              last_login: response.data.user.last_login,\n              lastLoginIp: response.data.user.lastLoginIp,\n              level2_verified: response.data.user.level2_verified\n            };\n          }\n\n          // 更新应用列表\n          recentApps.value = response.data.recentApps || [];\n          console.log('仪表盘数据加载成功:', response.data);\n        }\n\n        // 计算安全评分\n        calculateSecurityScore();\n      } catch (error) {\n        console.error(\"Failed to fetch dashboard data:\", error);\n        message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n      } finally {\n        loading.value = false;\n      }\n    };\n    onMounted(() => {\n      // We need user data, if not present, maybe fetch it or rely on login flow\n      if (!userStore.user) {\n        // This case might happen on a page refresh, you might want to fetch user data\n        // For now, we assume user data is populated from login\n      }\n      fetchDashboardData();\n    });\n    const __returned__ = {\n      loading,\n      userStore,\n      message,\n      recentApps,\n      securityScore,\n      securityItems,\n      isDarkMode,\n      formatDateTime,\n      getSecurityArcPath,\n      getSecurityColor,\n      getSecurityLevelType,\n      getSecurityLevelText,\n      getCompactSecurityArcPath,\n      handleSecurityAction,\n      calculateSecurityScore,\n      fetchDashboardData,\n      ref,\n      onMounted,\n      computed,\n      get NSpin() {\n        return NSpin;\n      },\n      get NGrid() {\n        return NGrid;\n      },\n      get NGi() {\n        return NGi;\n      },\n      get NCard() {\n        return NCard;\n      },\n      get NAlert() {\n        return NAlert;\n      },\n      get NDescriptions() {\n        return NDescriptions;\n      },\n      get NDescriptionsItem() {\n        return NDescriptionsItem;\n      },\n      get NButton() {\n        return NButton;\n      },\n      get NTag() {\n        return NTag;\n      },\n      get NList() {\n        return NList;\n      },\n      get NListItem() {\n        return NListItem;\n      },\n      get NIcon() {\n        return NIcon;\n      },\n      get NEmpty() {\n        return NEmpty;\n      },\n      get useMessage() {\n        return useMessage;\n      },\n      get useUserStore() {\n        return useUserStore;\n      },\n      get getApiClient() {\n        return getApiClient;\n      },\n      get CheckmarkCircleOutline() {\n        return CheckmarkCircleOutline;\n      },\n      get CloseCircleOutline() {\n        return CloseCircleOutline;\n      },\n      get AppsOutline() {\n        return AppsOutline;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "NSpin", "NGrid", "NGi", "NCard", "N<PERSON><PERSON><PERSON>", "NDescriptions", "NDescriptionsItem", "NButton", "NTag", "NList", "NListItem", "NIcon", "NEmpty", "useMessage", "useUserStore", "getApiClient", "CheckmarkCircleOutline", "CloseCircleOutline", "AppsOutline", "loading", "userStore", "message", "recentApps", "securityScore", "securityItems", "key", "name", "status", "user", "is_email_verified", "actionText", "is_phone_verified", "level2_verified", "security_mfa_enabled", "isDarkMode", "themeMode", "localStorage", "getItem", "window", "matchMedia", "matches", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "year", "month", "day", "hour", "minute", "second", "hour12", "replace", "getSecurityArcPath", "score", "angle", "Math", "PI", "x", "cos", "y", "sin", "getSecurityColor", "getSecurityLevelType", "getSecurityLevelText", "getCompactSecurityArcPath", "handleSecurityAction", "info", "location", "href", "calculateSecurityScore", "items", "value", "completedItems", "filter", "item", "length", "round", "fetchDashboardData", "apiClient", "response", "get", "data", "success", "createdAt", "registrationTime", "formatted", "lastLoginAt", "lastLoginTime", "last_login", "lastLoginIp", "console", "log", "error"], "sources": ["G:/Project/KongKuang-Network/kongkuang-auth/src/views/Dashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"dashboard-container\">\n    <n-spin :show=\"loading\">\n      <div class=\"dashboard-content\">\n        <h2 class=\"welcome-title\">你好, {{ userStore.user?.username }}</h2>\n\n        <n-alert title=\"通知\" type=\"info\" :bordered=\"true\" class=\"info-alert\">\n          KongKuang ID 现已开放 OAuth 应用注册, 在\"顶部菜单栏-更多\"启用开发者选项(需要已完成实名认证).\n          之后, 菜单栏中的开发者选项将会被设置为可见, 您即可添加您的 OAuth 应用程序.\n          我们仍在进行开发工作, KongKuang ID 基于 OAuth 2.0 标准协议, 有关技术文档将在后续逐步补全, 感谢您的理解.\n        </n-alert>\n\n        <n-grid x-gap=\"16\" y-gap=\"16\" :cols=\"3\" style=\"flex: 1;\">\n          <n-gi :span=\"2\">\n            <n-card :bordered=\"false\" class=\"user-info-panel\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n              <n-descriptions\n                label-placement=\"top\"\n                :column=\"2\"\n              >\n                <n-descriptions-item label=\"ID\">\n                  {{ userStore.user?.id }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"实名状态\">\n                  <n-tag\n                    :bordered=\"false\"\n                    :type=\"userStore.user?.level2_verified ? 'success' : 'warning'\"\n                    size=\"small\"\n                  >\n                    {{ userStore.user?.level2_verified ? '已实名' : '未实名' }}\n                  </n-tag>\n                </n-descriptions-item>\n                <n-descriptions-item label=\"注册时间\">\n                  {{ formatDateTime(userStore.user?.createdAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录时间\">\n                  {{ formatDateTime(userStore.user?.last_login || userStore.user?.lastLoginAt) }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"最后登录 IP\">\n                  {{ userStore.user?.lastLoginIp || '未知' }}\n                </n-descriptions-item>\n                <n-descriptions-item label=\"用户状态\">\n                   <n-tag :bordered=\"false\" type=\"success\" size=\"small\">正常</n-tag>\n                </n-descriptions-item>\n                 <n-descriptions-item label=\"绑定邮箱\" :span=\"2\">\n                  <div class=\"email-item\">\n                    <span>{{ userStore.user?.email }}</span>\n                     <n-button text type=\"primary\" size=\"small\">换绑</n-button>\n              </div>\n                </n-descriptions-item>\n              </n-descriptions>\n               <n-button type=\"primary\" ghost @click=\"$router.push('/security')\" style=\"margin-top: 16px;\">\n                  更改密码\n              </n-button>\n            </n-card>\n          </n-gi>\n\n          <n-gi :span=\"1\">\n            <div class=\"side-cards\">\n              <!-- 授权服务卡片 -->\n              <n-card title=\"可使用空旷账户登录的服务\" :bordered=\"false\" class=\"right-card services-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"services-content\">\n                  <div v-if=\"recentApps && recentApps.length > 0\" class=\"service-list\">\n                    <div v-for=\"app in recentApps.slice(0, 3)\" :key=\"app.id\" class=\"service-item-compact\">\n                      <div class=\"service-dot\"></div>\n                      <span class=\"service-name\">{{ app.name }}</span>\n                    </div>\n                  </div>\n                  <div v-else class=\"no-services\">\n                    <n-icon size=\"24\" color=\"#d0d0d0\">\n                      <apps-outline />\n                    </n-icon>\n                    <span>暂无服务</span>\n                  </div>\n                </div>\n              </n-card>\n\n              <!-- 账户安全指标卡片 -->\n              <n-card title=\"安全指标\" :bordered=\"false\" class=\"compact-card security-card\" :theme-overrides=\"{ color: isDarkMode ? '#2a2a30' : '#ffffff' }\">\n                <div class=\"security-dashboard-compact\">\n                  <!-- 紧凑型安全评分仪表盘 -->\n                  <div class=\"security-gauge-compact\">\n                    <svg class=\"gauge-svg-compact\" viewBox=\"0 0 120 80\" width=\"120\" height=\"80\">\n                      <!-- 背景弧线 -->\n                      <path\n                        d=\"M 15 65 A 45 45 0 0 1 105 65\"\n                        fill=\"none\"\n                        :stroke=\"isDarkMode ? '#3a3a3a' : '#e0e0e0'\"\n                        stroke-width=\"6\"\n                        stroke-linecap=\"round\"\n                      />\n                      <!-- 进度弧线 -->\n                      <path\n                        :d=\"getCompactSecurityArcPath(securityScore)\"\n                        fill=\"none\"\n                        :stroke=\"getSecurityColor(securityScore)\"\n                        stroke-width=\"6\"\n                        stroke-linecap=\"round\"\n                        class=\"security-arc\"\n                      />\n                      <!-- 中心文字 -->\n                      <text x=\"60\" y=\"55\" text-anchor=\"middle\" class=\"gauge-score-compact\">\n                        {{ securityScore }}%\n                      </text>\n                      <text x=\"60\" y=\"70\" text-anchor=\"middle\" class=\"gauge-label-compact\">\n                        {{ getSecurityLevelText(securityScore) }}\n                      </text>\n                    </svg>\n                  </div>\n\n                  <!-- 紧凑型安全项目列表 -->\n                  <div class=\"security-items-compact\">\n                    <div class=\"security-item-compact\" v-for=\"item in securityItems\" :key=\"item.key\">\n                      <n-icon\n                        size=\"12\"\n                        :color=\"item.status ? '#18a058' : '#d03050'\"\n                      >\n                        <component :is=\"item.status ? 'checkmark-circle-outline' : 'close-circle-outline'\" />\n                      </n-icon>\n                      <span class=\"item-name-compact\">{{ item.name }}</span>\n                    </div>\n                  </div>\n                </div>\n              </n-card>\n            </div>\n          </n-gi>\n        </n-grid>\n      </div>\n    </n-spin>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted, computed } from 'vue';\nimport {\n  NSpin,\n  NGrid,\n  NGi,\n  NCard,\n  NAlert,\n  NDescriptions,\n  NDescriptionsItem,\n  NButton,\n  NTag,\n  NList,\n  NListItem,\n  NIcon,\n  NEmpty,\n  useMessage\n} from 'naive-ui';\nimport { useUserStore } from '../stores/user';\nimport { getApiClient } from '../utils/api';\nimport {\n  CheckmarkCircleOutline,\n  CloseCircleOutline,\n  AppsOutline\n} from '@vicons/ionicons5';\n\nconst loading = ref(false);\nconst userStore = useUserStore();\nconst message = useMessage();\n\nconst recentApps = ref([]);\n\n// 安全相关数据\nconst securityScore = ref(75); // 安全评分 0-100\n\nconst securityItems = computed(() => [\n  {\n    key: 'email_verified',\n    name: '邮箱验证',\n    status: userStore.user?.is_email_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'phone_verified',\n    name: '手机验证',\n    status: userStore.user?.is_phone_verified || false,\n    actionText: '去验证'\n  },\n  {\n    key: 'level2_verified',\n    name: '实名认证',\n    status: userStore.user?.level2_verified || false,\n    actionText: '去认证'\n  },\n  {\n    key: 'mfa_enabled',\n    name: '双因子认证',\n    status: userStore.user?.security_mfa_enabled || false,\n    actionText: '去开启'\n  }\n]);\n\n// 检查是否为深色模式\nconst isDarkMode = computed(() => {\n  const themeMode = localStorage.getItem('theme') || 'system';\n  if (themeMode === 'system') {\n    return window.matchMedia('(prefers-color-scheme: dark)').matches;\n  }\n  return themeMode === 'dark';\n});\n\nconst formatDateTime = (dateString) => {\n  if (!dateString) return 'N/A';\n  const date = new Date(dateString);\n  // Using toLocaleString for a more standard format, customize as needed\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    hour12: false\n  }).replace(/\\//g, '-');\n};\n\n// 安全相关方法\nconst getSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)\n  const angle = (score / 100) * Math.PI;\n  const x = 100 + 80 * Math.cos(Math.PI - angle);\n  const y = 100 - 80 * Math.sin(Math.PI - angle);\n\n  return `M 20 100 A 80 80 0 0 1 ${x} ${y}`;\n};\n\nconst getSecurityColor = (score) => {\n  if (score >= 80) return '#18a058'; // 绿色 - 安全\n  if (score >= 60) return '#f0a020'; // 橙色 - 一般\n  return '#d03050'; // 红色 - 危险\n};\n\nconst getSecurityLevelType = (score) => {\n  if (score >= 80) return 'success';\n  if (score >= 60) return 'warning';\n  return 'error';\n};\n\nconst getSecurityLevelText = (score) => {\n  if (score >= 80) return '安全';\n  if (score >= 60) return '一般';\n  return '危险';\n};\n\n// 紧凑型仪表盘弧线路径计算\nconst getCompactSecurityArcPath = (score) => {\n  // 将分数转换为弧度 (0-100 映射到 0-π)，适配紧凑型尺寸\n  const angle = (score / 100) * Math.PI;\n  const x = 60 + 45 * Math.cos(Math.PI - angle);\n  const y = 65 - 45 * Math.sin(Math.PI - angle);\n\n  return `M 15 65 A 45 45 0 0 1 ${x} ${y}`;\n};\n\nconst handleSecurityAction = (key) => {\n  switch (key) {\n    case 'email_verified':\n      message.info('邮箱验证功能开发中');\n      break;\n    case 'phone_verified':\n      message.info('手机验证功能开发中');\n      break;\n    case 'level2_verified':\n      window.location.href = '/verification';\n      break;\n    case 'mfa_enabled':\n      window.location.href = '/security';\n      break;\n    default:\n      message.info('功能开发中');\n  }\n};\n\n// 计算安全评分\nconst calculateSecurityScore = () => {\n  const items = securityItems.value;\n  const completedItems = items.filter(item => item.status).length;\n  const score = Math.round((completedItems / items.length) * 100);\n  securityScore.value = score;\n};\n\nconst fetchDashboardData = async () => {\n  loading.value = true;\n  try {\n    const apiClient = getApiClient();\n    const response = await apiClient.get('/dashboard');\n\n    if (response.data && response.data.success) {\n      // 更新用户信息，使用后端返回的格式化数据\n      if (response.data.user) {\n        userStore.user = {\n          ...userStore.user,\n          ...response.data.user,\n          // 使用后端返回的格式化时间，如果没有则使用原始数据\n          createdAt: response.data.user.registrationTime?.formatted || response.data.user.createdAt,\n          lastLoginAt: response.data.user.lastLoginTime?.formatted || response.data.user.lastLoginAt,\n          last_login: response.data.user.last_login,\n          lastLoginIp: response.data.user.lastLoginIp,\n          level2_verified: response.data.user.level2_verified\n        };\n      }\n\n      // 更新应用列表\n      recentApps.value = response.data.recentApps || [];\n\n      console.log('仪表盘数据加载成功:', response.data);\n    }\n\n    // 计算安全评分\n    calculateSecurityScore();\n  } catch (error) {\n    console.error(\"Failed to fetch dashboard data:\", error);\n    message.error('加载仪表盘数据失败: ' + (error.response?.data?.message || error.message));\n  } finally {\n    loading.value = false;\n  }\n};\n\n    onMounted(() => {\n  // We need user data, if not present, maybe fetch it or rely on login flow\n  if (!userStore.user) {\n    // This case might happen on a page refresh, you might want to fetch user data\n    // For now, we assume user data is populated from login\n  }\n  fetchDashboardData();\n});\n</script>\n<style scoped>\n.dashboard-container {\n  padding: 16px;\n  min-height: 100%;\n}\n\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n}\n\n.welcome-title {\n  font-size: 24px;\n  font-weight: 600;\n  margin-bottom: 12px;\n  color: var(--n-text-color-1);\n}\n\n.info-alert {\n  margin-bottom: 16px;\n  flex-shrink: 0;\n}\n\n.user-info-panel {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.email-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 100%;\n}\n\n/* 水平并排布局 */\n.side-cards-horizontal {\n  display: flex;\n  gap: 16px;\n  height: 100%;\n}\n\n.compact-card {\n  flex: 1;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n/* 授权服务卡片样式 */\n.services-card {\n  min-width: 0; /* 允许内容收缩 */\n}\n\n.services-content {\n  min-height: 120px;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n\n.service-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.service-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 4px 0;\n}\n\n.service-dot {\n  width: 6px;\n  height: 6px;\n  border-radius: 50%;\n  background-color: var(--n-primary-color);\n  flex-shrink: 0;\n}\n\n.service-name {\n  font-size: 13px;\n  color: var(--n-text-color-1);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.no-services {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n  color: var(--n-text-color-3);\n  font-size: 12px;\n}\n\n.more-services {\n  font-size: 11px;\n  color: var(--n-text-color-3);\n  text-align: center;\n  margin-top: 4px;\n}\n\n.right-card {\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  height: fit-content;\n}\n\n.service-item {\n  padding: 8px 4px !important;\n  transition: color 0.3s;\n}\n\n.service-item:hover {\n  color: var(--n-primary-color);\n}\n\n/* 紧凑型安全指标卡片样式 */\n.security-card {\n  min-width: 0; /* 允许内容收缩 */\n}\n\n.security-dashboard-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  min-height: 120px;\n}\n\n.security-gauge-compact {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 6px;\n}\n\n.gauge-svg-compact {\n  width: 100%;\n  height: auto;\n  max-width: 120px;\n}\n\n.gauge-score-compact {\n  font-size: 18px;\n  font-weight: 600;\n  fill: var(--n-text-color-1);\n}\n\n.gauge-label-compact {\n  font-size: 10px;\n  fill: var(--n-text-color-2);\n}\n\n.security-level-compact {\n  margin-top: 2px;\n}\n\n.security-items-compact {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.security-item-compact {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  padding: 2px 0;\n}\n\n.item-name-compact {\n  font-size: 11px;\n  color: var(--n-text-color-2);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n/* 通用样式 */\n.security-arc {\n  transition: all 0.3s ease;\n}\n\n/* 确保网格布局紧凑 */\n:deep(.n-grid) {\n  height: 100%;\n}\n\n:deep(.n-gi) {\n  height: fit-content;\n}\n\n/* 减少卡片内部间距 */\n:deep(.n-card .n-card__content) {\n  padding: 16px;\n}\n\n:deep(.n-descriptions) {\n  margin-bottom: 0;\n}\n\n:deep(.n-descriptions-item) {\n  margin-bottom: 8px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .side-cards-horizontal {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .compact-card {\n    flex: none;\n  }\n\n  .gauge-svg-compact {\n    max-width: 100px;\n  }\n\n  .gauge-score-compact {\n    font-size: 16px;\n  }\n\n  .service-name,\n  .item-name-compact {\n    font-size: 12px;\n  }\n}\n</style>"], "mappings": ";;AAoIA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AAC9C,SACEC,KAAK,EACLC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,aAAa,EACbC,iBAAiB,EACjBC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,KAAK,EACLC,MAAM,EACNC,UAAS,QACJ,UAAU;AACjB,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,cAAc;AAC3C,SACEC,sBAAsB,EACtBC,kBAAkB,EAClBC,WAAU,QACL,mBAAmB;;;;;;;IAE1B,MAAMC,OAAO,GAAGtB,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMuB,SAAS,GAAGN,YAAY,CAAC,CAAC;IAChC,MAAMO,OAAO,GAAGR,UAAU,CAAC,CAAC;IAE5B,MAAMS,UAAU,GAAGzB,GAAG,CAAC,EAAE,CAAC;;IAE1B;IACA,MAAM0B,aAAa,GAAG1B,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;IAE/B,MAAM2B,aAAa,GAAGzB,QAAQ,CAAC,MAAM,CACnC;MACE0B,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEP,SAAS,CAACQ,IAAI,EAAEC,iBAAiB,IAAI,KAAK;MAClDC,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEP,SAAS,CAACQ,IAAI,EAAEG,iBAAiB,IAAI,KAAK;MAClDD,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,iBAAiB;MACtBC,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEP,SAAS,CAACQ,IAAI,EAAEI,eAAe,IAAI,KAAK;MAChDF,UAAU,EAAE;IACd,CAAC,EACD;MACEL,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,MAAM,EAAEP,SAAS,CAACQ,IAAI,EAAEK,oBAAoB,IAAI,KAAK;MACrDH,UAAU,EAAE;IACd,EACD,CAAC;;IAEF;IACA,MAAMI,UAAU,GAAGnC,QAAQ,CAAC,MAAM;MAChC,MAAMoC,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,QAAQ;MAC3D,IAAIF,SAAS,KAAK,QAAQ,EAAE;QAC1B,OAAOG,MAAM,CAACC,UAAU,CAAC,8BAA8B,CAAC,CAACC,OAAO;MAClE;MACA,OAAOL,SAAS,KAAK,MAAM;IAC7B,CAAC,CAAC;IAEF,MAAMM,cAAc,GAAIC,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,KAAK;MAC7B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC;MACA,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO,EAAE;QAClCC,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;MACV,CAAC,CAAC,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACxB,CAAC;;IAED;IACA,MAAMC,kBAAkB,GAAIC,KAAK,IAAK;MACpC;MACA,MAAMC,KAAK,GAAID,KAAK,GAAG,GAAG,GAAIE,IAAI,CAACC,EAAE;MACrC,MAAMC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAC9C,MAAMK,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAE9C,OAAO,0BAA0BG,CAAC,IAAIE,CAAC,EAAE;IAC3C,CAAC;IAED,MAAME,gBAAgB,GAAIR,KAAK,IAAK;MAClC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC;MACnC,OAAO,SAAS,CAAC,CAAC;IACpB,CAAC;IAED,MAAMS,oBAAoB,GAAIT,KAAK,IAAK;MACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;MACjC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;MACjC,OAAO,OAAO;IAChB,CAAC;IAED,MAAMU,oBAAoB,GAAIV,KAAK,IAAK;MACtC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,IAAI;MAC5B,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAMW,yBAAyB,GAAIX,KAAK,IAAK;MAC3C;MACA,MAAMC,KAAK,GAAID,KAAK,GAAG,GAAG,GAAIE,IAAI,CAACC,EAAE;MACrC,MAAMC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGF,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAC7C,MAAMK,CAAC,GAAG,EAAE,GAAG,EAAE,GAAGJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACC,EAAE,GAAGF,KAAK,CAAC;MAE7C,OAAO,yBAAyBG,CAAC,IAAIE,CAAC,EAAE;IAC1C,CAAC;IAED,MAAMM,oBAAoB,GAAI1C,GAAG,IAAK;MACpC,QAAQA,GAAG;QACT,KAAK,gBAAgB;UACnBJ,OAAO,CAAC+C,IAAI,CAAC,WAAW,CAAC;UACzB;QACF,KAAK,gBAAgB;UACnB/C,OAAO,CAAC+C,IAAI,CAAC,WAAW,CAAC;UACzB;QACF,KAAK,iBAAiB;UACpB9B,MAAM,CAAC+B,QAAQ,CAACC,IAAI,GAAG,eAAe;UACtC;QACF,KAAK,aAAa;UAChBhC,MAAM,CAAC+B,QAAQ,CAACC,IAAI,GAAG,WAAW;UAClC;QACF;UACEjD,OAAO,CAAC+C,IAAI,CAAC,OAAO,CAAC;MACzB;IACF,CAAC;;IAED;IACA,MAAMG,sBAAsB,GAAGA,CAAA,KAAM;MACnC,MAAMC,KAAK,GAAGhD,aAAa,CAACiD,KAAK;MACjC,MAAMC,cAAc,GAAGF,KAAK,CAACG,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACjD,MAAM,CAAC,CAACkD,MAAM;MAC/D,MAAMtB,KAAK,GAAGE,IAAI,CAACqB,KAAK,CAAEJ,cAAc,GAAGF,KAAK,CAACK,MAAM,GAAI,GAAG,CAAC;MAC/DtD,aAAa,CAACkD,KAAK,GAAGlB,KAAK;IAC7B,CAAC;IAED,MAAMwB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC5D,OAAO,CAACsD,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMO,SAAS,GAAGjE,YAAY,CAAC,CAAC;QAChC,MAAMkE,QAAQ,GAAG,MAAMD,SAAS,CAACE,GAAG,CAAC,YAAY,CAAC;QAElD,IAAID,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UAC1C;UACA,IAAIH,QAAQ,CAACE,IAAI,CAACvD,IAAI,EAAE;YACtBR,SAAS,CAACQ,IAAI,GAAG;cACf,GAAGR,SAAS,CAACQ,IAAI;cACjB,GAAGqD,QAAQ,CAACE,IAAI,CAACvD,IAAI;cACrB;cACAyD,SAAS,EAAEJ,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC0D,gBAAgB,EAAEC,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAACyD,SAAS;cACzFG,WAAW,EAAEP,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC6D,aAAa,EAAEF,SAAS,IAAIN,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC4D,WAAW;cAC1FE,UAAU,EAAET,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC8D,UAAU;cACzCC,WAAW,EAAEV,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAAC+D,WAAW;cAC3C3D,eAAe,EAAEiD,QAAQ,CAACE,IAAI,CAACvD,IAAI,CAACI;YACtC,CAAC;UACH;;UAEA;UACAV,UAAU,CAACmD,KAAK,GAAGQ,QAAQ,CAACE,IAAI,CAAC7D,UAAU,IAAI,EAAE;UAEjDsE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEZ,QAAQ,CAACE,IAAI,CAAC;QAC1C;;QAEA;QACAZ,sBAAsB,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvDzE,OAAO,CAACyE,KAAK,CAAC,aAAa,IAAIA,KAAK,CAACb,QAAQ,EAAEE,IAAI,EAAE9D,OAAO,IAAIyE,KAAK,CAACzE,OAAO,CAAC,CAAC;MACjF,CAAC,SAAS;QACRF,OAAO,CAACsD,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;IAEG3E,SAAS,CAAC,MAAM;MAClB;MACA,IAAI,CAACsB,SAAS,CAACQ,IAAI,EAAE;QACnB;QACA;MAAA;MAEFmD,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}