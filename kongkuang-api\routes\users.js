const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { auth } = require('../middlewares/auth');
const multer = require('multer');
const path = require('path');
const config = require('../config/config');

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadPath = path.join(__dirname, '..', config.upload.avatar_path);
    cb(null, uploadPath);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    cb(null, 'avatar-' + uniqueSuffix + extension);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: config.upload.max_file_size // 默认5MB
  },
  fileFilter: function (req, file, cb) {
    // 只允许图片文件
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.mimetype)) {
      const error = new Error('不支持的文件类型');
      error.code = 'UNSUPPORTED_FILETYPE';
      return cb(error, false);
    }
    cb(null, true);
  }
});

// 所有路由都需要验证令牌
router.use(auth);

// 用户资料路由
router.get('/profile', userController.getProfile);
router.put('/profile', userController.updateProfile);

// 获取认证状态
router.get('/verification-status', async (req, res) => {
  try {
    const { User } = require('../models');
    const level1Cache = require('../utils/level1Cache');

    const user = await User.findByPk(req.user.id);
    const level1Info = level1Cache.getLevel1Info(req.user.id);

    res.json({
      success: true,
      level1Completed: !!level1Info,
      level2Completed: user.level2_verified || false,
      level1Info: level1Info,
      level2Info: user.level2_verified ? {
        realName: user.real_name ? user.real_name.replace(/(.{1}).*(.{1})/, '$1***$2') : null,
        verifiedAt: user.level2_verified_at
      } : null
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取认证状态失败'
    });
  }
});

// 头像上传路由
router.post('/avatar', upload.single('avatar'), userController.updateAvatar);

// 邮箱和手机号更新路由
router.put('/email', userController.updateEmail);
router.put('/phone', userController.updatePhone);

module.exports = router; 